-- Migration for stage progress tracking system
-- This adds the tables needed for dynamic milestone tracking

-- Create user_stage_progress table
CREATE TABLE IF NOT EXISTS user_stage_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  stage_id INTEGER NOT NULL,
  milestones_completed TEXT[] DEFAULT '{}',
  total_milestones INTEGER NOT NULL DEFAULT 0,
  progress_percentage INTEGER NOT NULL DEFAULT 0,
  last_update TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  confidence_score INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one progress record per user per stage
  UNIQUE(user_id, stage_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_stage_progress_user_id ON user_stage_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_stage_progress_stage_id ON user_stage_progress(stage_id);
CREATE INDEX IF NOT EXISTS idx_user_stage_progress_updated ON user_stage_progress(updated_at DESC);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_stage_progress_updated_at 
    BEFORE UPDATE ON user_stage_progress 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS
ALTER TABLE user_stage_progress ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own stage progress" ON user_stage_progress
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own stage progress" ON user_stage_progress
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own stage progress" ON user_stage_progress
    FOR UPDATE USING (auth.uid() = user_id); 