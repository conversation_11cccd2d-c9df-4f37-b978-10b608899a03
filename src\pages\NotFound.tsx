
import React from 'react';
import { Button } from '@/components/ui/button';
import { Home, ArrowLeft, Sparkles, Zap } from 'lucide-react';
import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

const NotFound = () => {
  return (
    <div className="bg-oven-black text-accent-white min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow flex items-center justify-center overflow-hidden">
        <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black">
          {/* Animated background */}
          <div className="absolute inset-0">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [-20, 20, -20],
                  opacity: [0.2, 0.8, 0.2],
                  scale: [0.5, 1.5, 0.5],
                }}
                transition={{
                  duration: 4 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2,
                }}
              />
            ))}
          </div>

          <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
            >
              {/* 404 Number */}
              <motion.div
                className="mb-8"
                whileHover={{ scale: 1.05, rotate: 2 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <h1 className="text-8xl sm:text-9xl lg:text-[12rem] font-black leading-none">
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red via-cheese-gold to-sauce-red">
                    404
                  </span>
                </h1>
              </motion.div>

              {/* Error Message */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.6 }}
                className="mb-8"
              >
                <div className="relative group">
                  <div className="absolute -inset-2 bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                  <div className="relative bg-accent-white/10 backdrop-blur-md rounded-3xl p-8 border border-accent-white/20">
                    <Zap className="w-12 h-12 text-sauce-red mx-auto mb-4" />
                    <h2 className="text-3xl sm:text-4xl font-bold text-accent-white mb-4">
                      Oops! Page Not Found
                    </h2>
                    <p className="text-xl text-crust-beige/80 max-w-2xl mx-auto">
                      The page you're looking for seems to have wandered off. 
                      <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold font-semibold">
                        {" "}But don't worry, we'll get you back on track!
                      </span>
                    </p>
                  </div>
                </div>
              </motion.div>

              {/* Action Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.6 }}
                className="flex flex-col sm:flex-row justify-center items-center gap-6"
              >
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="relative group"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-2xl blur-xl opacity-70 group-hover:opacity-100 transition-opacity" />
                  <Button
                    size="lg"
                    onClick={() => window.history.back()}
                    className="relative bg-gradient-to-r from-sauce-red to-sauce-red/90 hover:from-sauce-red/90 hover:to-sauce-red text-accent-white font-bold text-lg px-10 py-6 rounded-2xl shadow-2xl border-0"
                  >
                    <ArrowLeft className="mr-3 h-6 w-6" />
                    Go Back
                  </Button>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={() => window.location.href = '/'}
                    className="bg-accent-white/10 backdrop-blur-md border-2 border-cheese-gold/50 text-cheese-gold hover:bg-cheese-gold/10 hover:border-cheese-gold hover:text-cheese-gold font-bold text-lg px-10 py-6 rounded-2xl"
                  >
                    <Home className="mr-3 h-6 w-6" />
                    Home
                  </Button>
                </motion.div>
              </motion.div>

              {/* Fun Message */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1, duration: 0.6 }}
                className="mt-12"
              >
                <div className="inline-flex items-center gap-2 bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 backdrop-blur-sm text-accent-white font-semibold px-8 py-4 rounded-2xl border border-sauce-red/30">
                  <Sparkles className="w-5 h-5 text-sauce-red" />
                  <span className="text-lg">
                    Lost? Let's get you back to building amazing things! 🥧
                  </span>
                  <Sparkles className="w-5 h-5 text-cheese-gold" />
                </div>
              </motion.div>
            </motion.div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default NotFound;
