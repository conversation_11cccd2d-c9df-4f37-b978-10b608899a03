import React from 'react';
import { Instagram, Linkedin, Mail, Sparkles, Heart, MessageSquare } from 'lucide-react';
import { motion } from 'framer-motion';

const Footer = () => {
  const navigationLinks = [
    { name: 'Home', href: '/' },
    { name: '10 in 10 Program', href: '/ten-in-ten' },
    { name: 'Family & Friends', href: '/family-friends' },
    { name: 'Apply', href: '/apply' },
    { name: 'Contact Us', href: 'mailto:<EMAIL>' }
  ];

  const socialLinks = [
    { icon: Instagram, href: 'https://www.instagram.com/piefisantacruz/', label: 'Instagram', color: 'from-pink-400 to-red-500' },
    { icon: Linkedin, href: 'https://www.linkedin.com/company/pie-fi/about/', label: 'LinkedIn', color: 'from-blue-400 to-cyan-500' },
    { icon: MessageSquare, href: 'https://discord.gg/gzQC3PBded', label: 'Discord', color: 'from-purple-400 to-indigo-500' }
  ];

  return (
    <footer className="relative bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0">
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-10, 10, -10],
              opacity: [0.2, 0.6, 0.2],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 4 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
          {/* Brand Section */}
          <motion.div 
            className="md:col-span-2"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <motion.div 
              className="flex items-center space-x-3 mb-6 group"
              whileHover={{ scale: 1.02 }}
            >
              <div className="w-12 h-12 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                <img 
                  src="/logo.png"
                  alt="Pie Fi Logo"
                  className="w-12 h-12 object-contain"
                />
              </div>
              <div>
                <h3 className="text-3xl font-black text-accent-white">
                  Pie-<span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">Fi</span>
                </h3>
                <p className="text-crust-beige/80 text-sm">Next-generation student–founder accelerator</p>
              </div>
            </motion.div>
            
            <motion.p 
              className="text-crust-beige/80 text-lg mb-6 leading-relaxed"
              whileHover={{ scale: 1.01 }}
            >
              Santa Cruz's next-generation student–founder accelerator.
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold font-semibold">
                Build something amazing.
              </span>
            </motion.p>
            
            <motion.div 
              className="flex items-center space-x-2 text-sm text-crust-beige/70"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <Heart className="w-4 h-4 text-sauce-red" />
              <span>Built by students, for students. Powered by Santa Cruz.</span>
            </motion.div>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h4 className="text-xl font-bold text-accent-white mb-6 flex items-center">
              <Sparkles className="w-5 h-5 text-sauce-red mr-2" />
              Quick Links
            </h4>
            <ul className="space-y-4">
              {navigationLinks.map((link, index) => (
                <motion.li 
                  key={link.name}
                  whileHover={{ x: 10, scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 400 }}
                >
                  <a 
                    href={link.href} 
                    className="text-crust-beige/80 hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-r hover:from-sauce-red hover:to-cheese-gold transition-all duration-300 block relative group"
                  >
                    {link.name}
                    <motion.div
                      className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-sauce-red to-cheese-gold group-hover:w-full transition-all duration-300"
                    />
                  </a>
                </motion.li>
              ))}
            </ul>
          </motion.div>

          {/* Connect Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <h4 className="text-xl font-bold text-accent-white mb-6">Connect</h4>
            
            <motion.div 
              className="mb-6 group"
              whileHover={{ scale: 1.02 }}
            >
              <div className="flex items-center space-x-3 p-4 bg-accent-white/10 backdrop-blur-md rounded-2xl border border-accent-white/20 group-hover:border-sauce-red/40 transition-all duration-300">
                <Mail className="w-5 h-5 text-sauce-red" />
                <div>
                  <p className="text-sm text-crust-beige/70">Email</p>
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-accent-white font-semibold hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-r hover:from-sauce-red hover:to-cheese-gold transition-all duration-300"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>
            </motion.div>

            <div className="flex space-x-4">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={social.label}
                  href={social.href}
                  className="relative group"
                  whileHover={{ scale: 1.1, y: -5 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, scale: 0.5 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 + 0.5 }}
                >
                  <div className={`absolute -inset-1 bg-gradient-to-r ${social.color} rounded-xl blur opacity-0 group-hover:opacity-70 transition-opacity duration-300`} />
                  <div className="relative w-12 h-12 bg-accent-white/10 backdrop-blur-md rounded-xl border border-accent-white/20 group-hover:border-accent-white/40 flex items-center justify-center transition-all duration-300">
                    <social.icon className="w-5 h-5 text-accent-white group-hover:text-white" />
                  </div>
                </motion.a>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Bottom Section */}
        <motion.div 
          className="mt-8 pt-6 border-t border-accent-white/10"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
        >
          <div className="text-center">
            <motion.p 
              className="text-crust-beige/70 text-sm"
              whileHover={{ scale: 1.02 }}
            >
              © 2025 Pie Fi. All rights reserved. 
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold font-semibold">
                {" "}Ready to build the future?
              </span>
            </motion.p>
          </div>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
