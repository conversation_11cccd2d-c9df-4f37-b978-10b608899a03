import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { supabaseAdmin } from '@/integrations/supabase/admin-client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { useScrollAnimation, scrollVariants, staggerContainer } from '@/hooks/useScrollAnimation';
import AdminStats from './AdminStats';
import AdminAccessCodes from './AdminAccessCodes';
import AdminSupporters from './AdminSupporters';
import EdgeFunctionDeployment from './EdgeFunctionDeployment';
import AdminUserManagement from './AdminUserManagement';
import { 
  normalizeUniversityName, 
  universityMatchesFilter, 
  getUniqueNormalizedUniversities 
} from '@/utils/universityNormalization';
import { 
  Users, 
  Calendar,
  TrendingUp,
  School,
  Search,
  Download,
  Eye,
  LogOut,
  User,
  Mail,
  Phone,
  ExternalLink,
  Linkedin,
  Github,
  FileText,
  Code,
  Lightbulb,
  Star,
  UserCheck,
  UserX,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Hourglass,
  Sparkles,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  BarChart3,
  Key,
  Heart,
  Database,
  Cloud
} from 'lucide-react';

type Application = {
  id: string;
  full_name: string;
  email: string;
  phone: string;
  university: string;
  year: string;
  major: string | null;
  graduation_year: string | null;
  skills: string;
  project_idea: string | null;
  team_status: string;
  why_pie_fi: string;
  linkedin: string | null;
  website: string | null;
  github: string | null;
  previous_projects: string | null;
  availability: string | null;
  references: string | null;
  resume_url: string | null;
  status: string | null;
  created_at: string;
  application_type: string | null;
};

interface AdminDashboardProps {
  onLogout: () => void;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ onLogout }) => {
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [universityFilter, setUniversityFilter] = useState('all');
  const [yearFilter, setYearFilter] = useState('all');
  const [teamStatusFilter, setTeamStatusFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);
  const [currentApplicationIndex, setCurrentApplicationIndex] = useState<number>(-1);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [activeTab, setActiveTab] = useState('applications');
  const { toast } = useToast();
  const { ref, isInView } = useScrollAnimation({ amount: 0.05 });

  useEffect(() => {
    fetchApplications();
  }, []);

  const fetchApplications = async (showSuccessToast = false) => {
    try {
      setLoading(true);
      const { data, error } = await supabaseAdmin
        .from('applications')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      const newApplications = data || [];
      setApplications(newApplications);
      
      // Show success toast for manual refresh or when explicitly requested
      if (showSuccessToast || applications.length > 0) {
        toast({
          title: "Applications Refreshed",
          description: `Successfully loaded ${newApplications.length} application${newApplications.length !== 1 ? 's' : ''}.`,
        });
      }
    } catch (error) {
      console.error('Error fetching applications:', error);
      toast({
        title: "Error",
        description: "Failed to load applications. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshClick = () => {
    fetchApplications(true);
  };

  const calculateStats = (apps: Application[]) => {
    // Only count builder applications for stats, exclude supporters
    const builderApps = apps.filter(app => app.application_type !== 'supporter');
    
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    const todayApps = builderApps.filter(app => {
      const appDate = new Date(app.created_at);
      return appDate >= today;
    }).length;

    const weekApps = builderApps.filter(app => {
      const appDate = new Date(app.created_at);
      return appDate >= weekAgo;
    }).length;

    const universities = new Set(builderApps.map(app => normalizeUniversityName(app.university))).size;

    // Status breakdown
    const statusCounts = {
      pending: builderApps.filter(app => !app.status || app.status === 'pending').length,
      shortlisted: builderApps.filter(app => app.status === 'shortlisted').length,
      accepted: builderApps.filter(app => app.status === 'accepted').length,
      rejected: builderApps.filter(app => app.status === 'rejected').length,
    };

    return {
      total: builderApps.length,
      today: todayApps,
      week: weekApps,
      universities,
      ...statusCounts
    };
  };

  const stats = calculateStats(applications);

  const filteredApplications = applications.filter(app => {
    // Only show builder applications in main tab, exclude supporters
    if (app.application_type === 'supporter') return false;
    
    const matchesSearch = searchTerm === '' || 
      app.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.university.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.skills.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (app.project_idea && app.project_idea.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesUniversity = universityMatchesFilter(app.university, universityFilter);
    const matchesYear = yearFilter === 'all' || app.year === yearFilter;
    const matchesTeamStatus = teamStatusFilter === 'all' || app.team_status === teamStatusFilter;
    const matchesStatus = statusFilter === 'all' || 
      (statusFilter === 'pending' && (!app.status || app.status === 'pending')) ||
      (statusFilter !== 'pending' && app.status === statusFilter);

    return matchesSearch && matchesUniversity && matchesYear && matchesTeamStatus && matchesStatus;
  });

  const builderApplications = applications.filter(app => app.application_type !== 'supporter');
  const uniqueUniversities = getUniqueNormalizedUniversities(builderApplications);
  const uniqueYears = [...new Set(builderApplications.map(app => app.year))].sort();
  const uniqueTeamStatuses = [...new Set(builderApplications.map(app => app.team_status))].sort();

  const openApplicationDialog = (app: Application, index: number) => {
    setSelectedApplication(app);
    setCurrentApplicationIndex(index);
  };

  const navigateToApplication = (direction: 'prev' | 'next') => {
    const newIndex = direction === 'prev' 
      ? Math.max(0, currentApplicationIndex - 1)
      : Math.min(filteredApplications.length - 1, currentApplicationIndex + 1);
    
    setCurrentApplicationIndex(newIndex);
    setSelectedApplication(filteredApplications[newIndex]);
  };

  const canNavigatePrev = currentApplicationIndex > 0;
  const canNavigateNext = currentApplicationIndex < filteredApplications.length - 1;

  const updateApplicationStatus = async (applicationId: string, newStatus: string) => {
    try {
      setIsUpdatingStatus(true);
      const { error } = await supabaseAdmin
        .from('applications')
        .update({ status: newStatus })
        .eq('id', applicationId);

      if (error) throw error;

      // Update local state
      setApplications(prev => prev.map(app => 
        app.id === applicationId ? { ...app, status: newStatus } : app
      ));

      if (selectedApplication?.id === applicationId) {
        setSelectedApplication(prev => prev ? { ...prev, status: newStatus } : null);
      }

      toast({
        title: "Status Updated",
        description: `Application status changed to ${newStatus}`,
      });
    } catch (error) {
      console.error('Error updating status:', error);
      toast({
        title: "Error",
        description: "Failed to update application status",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const exportToCSV = () => {
    try {
      if (filteredApplications.length === 0) {
        toast({
          title: "No Data to Export",
          description: "No applications available to export.",
          variant: "destructive",
        });
        return;
      }

      const headers = ['Name', 'Email', 'Phone', 'University', 'Year', 'Major', 'Skills', 'Team Status', 'Status', 'Applied Date'];
      const csvContent = [
        headers.join(','),
        ...filteredApplications.map(app => [
          `"${app.full_name?.replace(/"/g, '""') || ''}"`,
          `"${app.email?.replace(/"/g, '""') || ''}"`,
          `"${app.phone?.replace(/"/g, '""') || ''}"`,
          `"${normalizeUniversityName(app.university)?.replace(/"/g, '""') || ''}"`,
          `"${app.year?.replace(/"/g, '""') || ''}"`,
          `"${(app.major || 'N/A').replace(/"/g, '""')}"`,
          `"${app.skills?.replace(/"/g, '""') || ''}"`,
          `"${app.team_status?.replace(/"/g, '""') || ''}"`,
          `"${(app.status || 'pending').replace(/"/g, '""')}"`,
          `"${new Date(app.created_at).toLocaleDateString()}"`
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `pie-fi-applications-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Export Successful",
        description: `Successfully exported ${filteredApplications.length} application${filteredApplications.length !== 1 ? 's' : ''} to CSV.`,
      });
    } catch (error) {
      console.error('Error exporting CSV:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export applications. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getStatusIcon = (status: string | null) => {
    switch (status) {
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      case 'shortlisted':
        return <Star className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusBadgeClass = (status: string | null) => {
    switch (status) {
      case 'accepted':
        return 'bg-green-500 text-white border-green-600 hover:bg-green-600';
      case 'rejected':
        return 'bg-red-500 text-white border-red-600 hover:bg-red-600';
      case 'shortlisted':
        return 'bg-yellow-500 text-yellow-900 border-yellow-600 hover:bg-yellow-600 hover:text-white';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black flex items-center justify-center">
        <div className="text-center">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 border-4 border-sauce-red border-t-transparent rounded-full mx-auto mb-4"
          />
          <p className="text-accent-white text-xl">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black overflow-x-hidden" ref={ref}>
      {/* Animated background particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-20, 20, -20],
              opacity: [0.2, 0.8, 0.2],
              scale: [0.5, 1.5, 0.5],
            }}
            transition={{
              duration: 4 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <motion.div 
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 rounded-full border border-sauce-red/20 mb-6"
            whileHover={{ scale: 1.05 }}
          >
            <Sparkles className="w-4 h-4 text-cheese-gold" />
            <span className="font-semibold text-cheese-gold">Admin Panel</span>
          </motion.div>
          
          <h1 className="text-5xl sm:text-6xl font-black text-accent-white mb-4">
            Admin{" "}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
              Dashboard
            </span>
          </h1>
          <p className="text-xl text-crust-beige max-w-2xl mx-auto mb-8">
            Manage Pie Fi Applications with Power and Precision
          </p>
          
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              onClick={onLogout}
              className="bg-gradient-to-r from-sauce-red to-sauce-red/90 hover:from-sauce-red/90 hover:to-sauce-red text-white font-bold px-8 py-3 rounded-2xl shadow-xl border-0"
            >
              <LogOut className="h-5 w-5 mr-2" />
              Logout
            </Button>
          </motion.div>
        </motion.div>

        {/* Navigation Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-6 bg-accent-white/10 backdrop-blur-md border border-accent-white/20 rounded-2xl p-2 h-14">
              <TabsTrigger 
                value="applications" 
                className="flex items-center justify-center gap-2 data-[state=active]:bg-sauce-red data-[state=active]:text-white text-accent-white font-medium rounded-xl transition-all duration-300 h-10"
              >
                <Users className="h-4 w-4" />
                Applications
              </TabsTrigger>
              <TabsTrigger 
                value="stats" 
                className="flex items-center justify-center gap-2 data-[state=active]:bg-sauce-red data-[state=active]:text-white text-accent-white font-medium rounded-xl transition-all duration-300 h-10"
              >
                <BarChart3 className="h-4 w-4" />
                Analytics
              </TabsTrigger>
              <TabsTrigger 
                value="access-codes" 
                className="flex items-center justify-center gap-2 data-[state=active]:bg-sauce-red data-[state=active]:text-white text-accent-white font-medium rounded-xl transition-all duration-300 h-10"
              >
                <Key className="h-4 w-4" />
                Access Codes
              </TabsTrigger>
              <TabsTrigger
                value="supporters"
                className="flex items-center justify-center gap-2 data-[state=active]:bg-sauce-red data-[state=active]:text-white text-accent-white font-medium rounded-xl transition-all duration-300 h-10"
              >
                <Heart className="h-4 w-4" />
                Supporters
              </TabsTrigger>
              <TabsTrigger
                value="deployment"
                className="flex items-center justify-center gap-2 data-[state=active]:bg-sauce-red data-[state=active]:text-white text-accent-white font-medium rounded-xl transition-all duration-300 h-10"
              >
                <Cloud className="h-4 w-4" />
                Deployment
              </TabsTrigger>
              <TabsTrigger
                value="activity"
                className="flex items-center justify-center gap-2 data-[state=active]:bg-sauce-red data-[state=active]:text-white text-accent-white font-medium rounded-xl transition-all duration-300 h-10"
              >
                <Users className="h-4 w-4" />
                Users
              </TabsTrigger>
            </TabsList>

            <TabsContent value="applications" className="space-y-6 mt-8">
              {/* Applications Tab Content - keeping the existing applications management */}

        {/* Statistics Cards */}
        <div className="flex flex-wrap justify-center gap-6 mb-12">
          {[
            { title: "Total Applications", value: stats.total, icon: Users, gradient: "from-sauce-red to-pink-500", iconColor: "text-white" },
            { title: "Today", value: stats.today, icon: Calendar, gradient: "from-orange-400 to-yellow-500", iconColor: "text-white" },
            { title: "This Week", value: stats.week, icon: TrendingUp, gradient: "from-blue-400 to-blue-600", iconColor: "text-white" },
            { title: "Universities", value: stats.universities, icon: School, gradient: "from-purple-400 to-purple-600", iconColor: "text-white" },
            { title: "Pending", value: stats.pending, icon: Hourglass, gradient: "from-gray-400 to-gray-600", iconColor: "text-white" },
            { title: "Shortlisted", value: stats.shortlisted, icon: Star, gradient: "from-yellow-400 to-orange-500", iconColor: "text-white" },
            { title: "Accepted", value: stats.accepted, icon: CheckCircle, gradient: "from-green-400 to-green-600", iconColor: "text-white" },
          ].map((stat, index) => (
            <motion.div
              key={stat.title}
              variants={scrollVariants}
              whileHover={{ y: -5, scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
              className="group relative w-full sm:w-[260px] flex-shrink-0"
            >
              <div className={`absolute -inset-1 bg-gradient-to-r ${stat.gradient} rounded-3xl blur-xl opacity-60 group-hover:opacity-80 transition-opacity duration-300`} />
              <Card className="relative bg-accent-white/10 backdrop-blur-md border border-accent-white/20 rounded-3xl shadow-xl overflow-hidden h-full">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-3 rounded-2xl bg-gradient-to-r ${stat.gradient} shadow-lg`}>
                      <stat.icon className={`h-6 w-6 ${stat.iconColor}`} />
                    </div>
                    <div className="text-right">
                      <p className="text-3xl font-black text-accent-white">{stat.value}</p>
                    </div>
                  </div>
                  <p className="text-sm font-medium text-crust-beige/80">{stat.title}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="relative bg-accent-white/10 backdrop-blur-xl rounded-3xl p-8 border border-accent-white/20 shadow-2xl mb-8"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-sauce-red/5 to-cheese-gold/5 rounded-3xl" />
          
          <div className="relative z-10 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
            <div className="lg:col-span-2">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-crust-beige/60 h-5 w-5" />
                <Input
                  placeholder="Search applications..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-12 bg-accent-white/10 backdrop-blur-md border-accent-white/30 text-accent-white placeholder-crust-beige/60 rounded-2xl h-12 focus:border-sauce-red/50 focus:ring-sauce-red/20"
                />
              </div>
            </div>

            {[
              { value: universityFilter, onChange: setUniversityFilter, options: uniqueUniversities, placeholder: "University", allLabel: "All Universities" },
              { value: yearFilter, onChange: setYearFilter, options: uniqueYears, placeholder: "Year", allLabel: "All Years" },
              { value: teamStatusFilter, onChange: setTeamStatusFilter, options: uniqueTeamStatuses, placeholder: "Team Status", allLabel: "All Team Status" },
              { value: statusFilter, onChange: setStatusFilter, options: ['pending', 'shortlisted', 'accepted', 'rejected'], placeholder: "Status", allLabel: "All Status" },
            ].map((filter, index) => (
              <Select key={index} value={filter.value} onValueChange={filter.onChange}>
                <SelectTrigger className="bg-accent-white/10 backdrop-blur-md border-accent-white/30 text-accent-white rounded-2xl h-12">
                  <SelectValue placeholder={filter.placeholder} />
                </SelectTrigger>
                <SelectContent className="bg-oven-black/95 backdrop-blur-xl border-accent-white/20">
                  <SelectItem value="all">{filter.allLabel}</SelectItem>
                  {filter.options.map(option => (
                    <SelectItem key={option} value={option} className="text-accent-white hover:bg-sauce-red/20">
                      {option.replace('-', ' ')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ))}
          </div>

          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mt-8 gap-4">
            <p className="text-crust-beige/80 text-lg">
              Showing {filteredApplications.length} of {applications.length} applications
            </p>
            <div className="flex items-center gap-4">
              <TooltipProvider delayDuration={300}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <motion.div 
                      whileHover={{ scale: 1.1, y: -2 }} 
                      whileTap={{ scale: 0.95 }}
                      className="relative"
                    >
                      <div className="absolute -inset-1 bg-gradient-to-r from-sauce-red to-pink-500 rounded-full blur opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={handleRefreshClick}
                        disabled={loading}
                        className="relative text-accent-white hover:bg-sauce-red/20 hover:text-white rounded-full transition-all duration-300 shadow-lg hover:shadow-xl border border-accent-white/20 backdrop-blur-sm"
                      >
                        <motion.div
                          animate={{ rotate: loading ? 360 : 0 }}
                          transition={{ duration: 1, repeat: loading ? Infinity : 0, ease: "linear" }}
                        >
                          <RefreshCw className="h-5 w-5" />
                        </motion.div>
                      </Button>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent className="bg-oven-black/90 text-accent-white border-none shadow-xl" side="top">
                    <p>{loading ? 'Refreshing...' : 'Refresh Applications'}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider delayDuration={300}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <motion.div 
                      whileHover={{ scale: 1.05, y: -2 }} 
                      whileTap={{ scale: 0.95 }}
                      className="relative group"
                    >
                      <div className="absolute -inset-1 bg-gradient-to-r from-cheese-gold to-orange-500 rounded-2xl blur opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
                      <Button
                        onClick={exportToCSV}
                        disabled={loading || filteredApplications.length === 0}
                        className="relative bg-gradient-to-r from-cheese-gold to-orange-500 hover:from-cheese-gold/90 hover:to-orange-500/90 text-oven-black font-bold px-6 py-3 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Export CSV
                      </Button>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent className="bg-oven-black/90 text-accent-white border-none shadow-xl" side="top">
                    <p>
                      {filteredApplications.length === 0 
                        ? 'No applications to export' 
                        : `Export ${filteredApplications.length} application${filteredApplications.length !== 1 ? 's' : ''} to CSV`
                      }
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </motion.div>

        {/* Applications Table */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-accent-white/10 backdrop-blur-xl rounded-3xl border border-accent-white/20 shadow-2xl"
        >
          <div className="overflow-visible">
            <Table>
              <TableHeader>
                <TableRow className="border-accent-white/20 hover:bg-accent-white/5">
                  <TableHead className="text-accent-white font-bold text-base py-6 text-left">Name</TableHead>
                  <TableHead className="text-accent-white font-bold text-base text-center">University</TableHead>
                  <TableHead className="text-accent-white font-bold text-base text-center">Year</TableHead>
                  <TableHead className="text-accent-white font-bold text-base text-center">Status</TableHead>
                  <TableHead className="text-accent-white font-bold text-base text-center">Applied</TableHead>
                  <TableHead className="text-accent-white font-bold text-base text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredApplications.map((app, index) => (
                  <motion.tr 
                    key={app.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="border-accent-white/10 hover:bg-accent-white/5 transition-all duration-300"
                  >
                    <TableCell className="py-6 text-left">
                      <div>
                        <div className="font-bold text-accent-white text-lg">{app.full_name}</div>
                        <div className="text-crust-beige/80">{app.email}</div>
                      </div>
                    </TableCell>
                    <TableCell className="text-accent-white font-medium text-center">{normalizeUniversityName(app.university)}</TableCell>
                    <TableCell className="text-center">
                      <Badge className="bg-gradient-to-r from-blue-400 to-blue-600 text-white border-0 px-3 py-1 rounded-full">
                        {app.year}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <motion.div
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              className="inline-block"
                            >
                              <Badge className={`${getStatusBadgeClass(app.status)} px-3 py-1 rounded-full font-medium transition-all duration-300 cursor-pointer`}>
                                {getStatusIcon(app.status)}
                                <span className="ml-2">{app.status || 'pending'}</span>
                              </Badge>
                            </motion.div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Application Status: {(app.status || 'pending').charAt(0).toUpperCase() + (app.status || 'pending').slice(1)}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="text-crust-beige/80 font-medium text-center">
                      {new Date(app.created_at).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex gap-2 justify-center">
                        {/* Quick action buttons */}
                        {(!app.status || app.status === 'pending') && (
                          <TooltipProvider delayDuration={300}>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <motion.div 
                                  whileHover={{ scale: 1.1 }} 
                                  whileTap={{ scale: 0.9 }}
                                >
                                  <Button
                                    size="sm"
                                    onClick={() => updateApplicationStatus(app.id, 'shortlisted')}
                                    disabled={isUpdatingStatus}
                                    className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white rounded-xl shadow-lg"
                                  >
                                    <Star className="h-4 w-4" />
                                  </Button>
                                </motion.div>
                              </TooltipTrigger>
                              <TooltipContent className="bg-oven-black/90 text-accent-white border-none" side="top">
                                <p>Shortlist Application</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                        
                        {app.status === 'shortlisted' && (
                          <>
                            <TooltipProvider delayDuration={300}>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <motion.div 
                                    whileHover={{ scale: 1.1 }} 
                                    whileTap={{ scale: 0.9 }}
                                  >
                                    <Button
                                      size="sm"
                                      onClick={() => updateApplicationStatus(app.id, 'accepted')}
                                      disabled={isUpdatingStatus}
                                      className="bg-gradient-to-r from-green-400 to-green-600 hover:from-green-500 hover:to-green-700 text-white rounded-xl shadow-lg"
                                    >
                                      <UserCheck className="h-4 w-4" />
                                    </Button>
                                  </motion.div>
                                </TooltipTrigger>
                                <TooltipContent className="bg-oven-black/90 text-accent-white border-none" side="top">
                                  <p>Accept Application</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider delayDuration={300}>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <motion.div 
                                    whileHover={{ scale: 1.1 }} 
                                    whileTap={{ scale: 0.9 }}
                                  >
                                    <Button
                                      size="sm"
                                      onClick={() => updateApplicationStatus(app.id, 'rejected')}
                                      disabled={isUpdatingStatus}
                                      className="bg-gradient-to-r from-red-400 to-red-600 hover:from-red-500 hover:to-red-700 text-white rounded-xl shadow-lg"
                                    >
                                      <UserX className="h-4 w-4" />
                                    </Button>
                                  </motion.div>
                                </TooltipTrigger>
                                <TooltipContent className="bg-oven-black/90 text-accent-white border-none" side="top">
                                  <p>Reject Application</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </>
                        )}

                        <Dialog>
                          <TooltipProvider delayDuration={300}>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <DialogTrigger asChild>
                                  <motion.div 
                                    whileHover={{ scale: 1.1 }} 
                                    whileTap={{ scale: 0.9 }}
                                  >
                                    <Button
                                      size="sm"
                                      className="bg-gradient-to-r from-sauce-red to-pink-500 hover:from-sauce-red/90 hover:to-pink-600 text-white rounded-xl shadow-lg"
                                      onClick={() => openApplicationDialog(app, index)}
                                    >
                                      <Eye className="h-4 w-4" />
                                    </Button>
                                  </motion.div>
                                </DialogTrigger>
                              </TooltipTrigger>
                              <TooltipContent className="bg-oven-black/90 text-accent-white border-none" side="top">
                                <p>View Full Application</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-accent-white/10 backdrop-blur-xl border border-accent-white/20 shadow-2xl rounded-3xl" aria-describedby="application-details">
                            <DialogHeader>
                              <DialogTitle className="text-2xl font-bold text-accent-white flex items-center gap-3">
                                <User className="h-6 w-6 text-sauce-red" />
                                {selectedApplication?.full_name}
                              </DialogTitle>
                              <DialogDescription className="text-crust-beige/80">
                                View complete application details, contact information, and manage application status.
                              </DialogDescription>
                            </DialogHeader>

                            {/* Status Management Section */}
                            {selectedApplication && (
                              <div className="bg-accent-white/5 backdrop-blur-md border border-accent-white/20 rounded-2xl p-6 mb-8">
                                <h3 className="text-2xl font-bold text-accent-white mb-4 flex items-center gap-3">
                                  <div className="p-2 bg-gradient-to-r from-sauce-red to-pink-500 rounded-xl shadow-lg">
                                    <AlertCircle className="h-6 w-6 text-accent-white" />
                                  </div>
                                  Application Status Management
                                </h3>
                                <div className="flex flex-wrap gap-4">
                                  {[
                                    { status: 'shortlisted', label: 'Shortlist', icon: Star, gradient: 'from-yellow-400 to-orange-500' },
                                    { status: 'accepted', label: 'Accept', icon: UserCheck, gradient: 'from-green-400 to-green-600' },
                                    { status: 'rejected', label: 'Reject', icon: UserX, gradient: 'from-red-400 to-red-600' },
                                    { status: 'pending', label: 'Reset to Pending', icon: Clock, gradient: 'from-gray-400 to-gray-600' },
                                  ].map(({ status, label, icon: Icon, gradient }) => (
                                    <motion.div key={status} whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                                      <Button
                                        onClick={() => updateApplicationStatus(selectedApplication.id, status)}
                                        disabled={isUpdatingStatus}
                                        className={`bg-gradient-to-r ${gradient} hover:opacity-90 text-white font-bold px-6 py-3 rounded-2xl shadow-lg`}
                                      >
                                        <Icon className="h-5 w-5 mr-2" />
                                        {label}
                                      </Button>
                                    </motion.div>
                                  ))}
                                </div>
                              </div>
                            )}
                      
                            {selectedApplication && (
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-accent-white">
                                {/* Personal Information */}
                                <div className="bg-accent-white/10 backdrop-blur-md rounded-2xl border border-accent-white/20 p-8 shadow-xl">
                                  <h3 className="text-2xl font-bold flex items-center gap-3 mb-6 text-sauce-red border-b border-accent-white/20 pb-4">
                                    <User className="h-7 w-7" />
                                    Personal Information
                                  </h3>
                                  <div className="space-y-6">
                                    <div className="flex items-center justify-between">
                                      <span className="text-crust-beige/80 font-semibold">Name:</span>
                                      <span className="font-bold text-lg text-accent-white">{selectedApplication.full_name}</span>
                                    </div>
                                    <div className="flex items-center gap-4">
                                      <Mail className="h-6 w-6 text-sauce-red" />
                                      <a href={`mailto:${selectedApplication.email}`} className="text-cheese-gold hover:text-cheese-gold/80 font-semibold hover:underline transition-colors">
                                        {selectedApplication.email}
                                      </a>
                                    </div>
                                    <div className="flex items-center gap-4">
                                      <Phone className="h-6 w-6 text-sauce-red" />
                                      <a href={`tel:${selectedApplication.phone}`} className="text-cheese-gold hover:text-cheese-gold/80 font-semibold hover:underline transition-colors">
                                        {selectedApplication.phone}
                                      </a>
                                    </div>
                                  </div>
                                </div>

                                {/* Academic Information */}
                                <div className="bg-accent-white/10 backdrop-blur-md rounded-2xl border border-accent-white/20 p-8 shadow-xl">
                                  <h3 className="text-2xl font-bold flex items-center gap-3 mb-6 text-sauce-red border-b border-accent-white/20 pb-4">
                                    <School className="h-7 w-7" />
                                    Academic Information
                                  </h3>
                                  <div className="space-y-6">
                                    <div className="flex items-center justify-between">
                                      <span className="text-crust-beige/80 font-semibold">University:</span>
                                      <span className="font-bold text-lg text-accent-white">{normalizeUniversityName(selectedApplication.university)}</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                      <span className="text-crust-beige/80 font-semibold">Year:</span>
                                      <Badge className="bg-gradient-to-r from-blue-400 to-blue-600 text-white px-4 py-2 rounded-xl text-base">{selectedApplication.year}</Badge>
                                    </div>
                                    <div className="flex items-center justify-between">
                                      <span className="text-crust-beige/80 font-semibold">Major:</span>
                                      <span className="font-bold text-lg text-accent-white">{selectedApplication.major || 'Not specified'}</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                      <span className="text-crust-beige/80 font-semibold">Graduation:</span>
                                      <span className="font-bold text-lg text-accent-white">{selectedApplication.graduation_year || 'Not specified'}</span>
                                    </div>
                                  </div>
                                </div>

                                {/* Technical Information */}
                                <div className="col-span-full bg-accent-white/10 backdrop-blur-md rounded-2xl border border-accent-white/20 p-8 shadow-xl">
                                  <h3 className="text-2xl font-bold flex items-center gap-3 mb-6 text-sauce-red border-b border-accent-white/20 pb-4">
                                    <Code className="h-7 w-7" />
                                    Technical & Project Information
                                  </h3>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                    <div>
                                      <h4 className="font-bold text-accent-white mb-3 text-lg">Skills:</h4>
                                      <div className="p-4 bg-accent-white/5 backdrop-blur-sm rounded-xl border border-accent-white/20">
                                        <p className="text-crust-beige">{selectedApplication.skills}</p>
                                      </div>
                                    </div>
                                    <div>
                                      <h4 className="font-bold text-accent-white mb-3 text-lg">Team Status:</h4>
                                      <Badge className={`text-base px-4 py-2 rounded-xl ${selectedApplication.team_status === 'has team' ? 'bg-gradient-to-r from-green-400 to-green-600 text-white' : 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white'}`}>
                                        {selectedApplication.team_status.replace('-', ' ')}
                                      </Badge>
                                    </div>
                                  </div>
                                  
                                  {selectedApplication.project_idea && (
                                    <div className="mb-6">
                                      <h4 className="font-bold text-accent-white mb-3 text-lg">Project Idea:</h4>
                                      <div className="p-4 bg-accent-white/5 backdrop-blur-sm rounded-xl border border-accent-white/20">
                                        <p className="text-crust-beige">{selectedApplication.project_idea}</p>
                                      </div>
                                    </div>
                                  )}

                                  {selectedApplication.previous_projects && (
                                    <div>
                                      <h4 className="font-bold text-accent-white mb-3 text-lg">Previous Projects:</h4>
                                      <div className="p-4 bg-accent-white/5 backdrop-blur-sm rounded-xl border border-accent-white/20">
                                        <p className="text-crust-beige">{selectedApplication.previous_projects}</p>
                                      </div>
                                    </div>
                                  )}
                                </div>

                                {/* Motivation */}
                                <div className="col-span-full bg-accent-white/10 backdrop-blur-md rounded-2xl border border-accent-white/20 p-8 shadow-xl">
                                  <h3 className="text-2xl font-bold flex items-center gap-3 mb-6 text-sauce-red border-b border-accent-white/20 pb-4">
                                    <Lightbulb className="h-7 w-7" />
                                    Motivation
                                  </h3>
                                  <div>
                                    <h4 className="font-bold text-accent-white mb-3 text-lg">Why Pie Fi:</h4>
                                    <div className="p-4 bg-accent-white/5 backdrop-blur-sm rounded-xl border border-accent-white/20">
                                      <p className="text-crust-beige text-sm leading-relaxed whitespace-pre-wrap">
                                        {selectedApplication.why_pie_fi || 'No response provided'}
                                      </p>
                                    </div>
                                  </div>
                                </div>

                                {/* Links & Additional Info */}
                                <div className="col-span-full bg-accent-white/10 backdrop-blur-md rounded-2xl border border-accent-white/20 p-8 shadow-xl">
                                  <h3 className="text-2xl font-bold flex items-center gap-3 mb-6 text-sauce-red border-b border-accent-white/20 pb-4">
                                    <ExternalLink className="h-7 w-7" />
                                    Links & Additional Information
                                  </h3>
                                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                                    {[
                                      { url: selectedApplication.linkedin, icon: Linkedin, label: 'LinkedIn' },
                                      { url: selectedApplication.github, icon: Github, label: 'GitHub' },
                                      { url: selectedApplication.website, icon: ExternalLink, label: 'Website' },
                                      { url: selectedApplication.resume_url, icon: FileText, label: 'Resume' },
                                    ].filter(link => link.url).map((link, index) => (
                                      <motion.a
                                        key={index}
                                        href={link.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="flex items-center gap-3 p-4 bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 rounded-xl border border-blue-200 transition-all duration-300 group"
                                        whileHover={{ scale: 1.02 }}
                                        whileTap={{ scale: 0.98 }}
                                      >
                                        <link.icon className="h-5 w-5 text-blue-600 group-hover:text-blue-800 transition-colors" />
                                        <span className="font-semibold text-blue-600 group-hover:text-blue-800 transition-colors">{link.label}</span>
                                      </motion.a>
                                    ))}
                                  </div>

                                  {selectedApplication.availability && (
                                    <div className="mb-6">
                                      <h4 className="font-bold text-accent-white mb-3 text-lg">Availability:</h4>
                                      <div className="p-4 bg-accent-white/5 backdrop-blur-sm rounded-xl border border-accent-white/20">
                                        <p className="text-crust-beige">{selectedApplication.availability}</p>
                                      </div>
                                    </div>
                                  )}

                                  {selectedApplication.references && (
                                    <div className="mb-6">
                                      <h4 className="font-bold text-accent-white mb-3 text-lg">References:</h4>
                                      <div className="p-4 bg-accent-white/5 backdrop-blur-sm rounded-xl border border-accent-white/20">
                                        <p className="text-crust-beige">{selectedApplication.references}</p>
                                      </div>
                                    </div>
                                  )}

                                  <div className="pt-6 border-t border-accent-white/20">
                                    <p className="text-accent-white/60 text-sm">
                                      Application submitted on {new Date(selectedApplication.created_at).toLocaleString()}
                                    </p>
                                  </div>
                                </div>

                                {/* Resume Section */}
                                {selectedApplication.resume_url && (
                                  <div className="col-span-full bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 backdrop-blur-md rounded-2xl border border-sauce-red/30 p-8 shadow-xl">
                                    <h3 className="text-2xl font-bold flex items-center gap-3 mb-6 text-sauce-red border-b border-sauce-red/30 pb-4">
                                      <FileText className="h-7 w-7" />
                                      Resume
                                    </h3>
                                    <div className="flex flex-col sm:flex-row gap-4 items-center justify-between bg-accent-white/10 backdrop-blur-sm rounded-xl border border-accent-white/20 p-6">
                                      <div className="flex items-center gap-4">
                                        <div className="p-3 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-xl">
                                          <FileText className="h-8 w-8 text-white" />
                                        </div>
                                        <div>
                                          <h4 className="font-bold text-accent-white text-lg">Resume PDF</h4>
                                          <p className="text-crust-beige/80">Click to view or download</p>
                                        </div>
                                      </div>
                                      <div className="flex gap-3">
                                        <motion.a
                                          href={selectedApplication.resume_url}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold rounded-xl transition-all duration-300 shadow-lg"
                                          whileHover={{ scale: 1.05 }}
                                          whileTap={{ scale: 0.95 }}
                                        >
                                          <Eye className="h-5 w-5" />
                                          View Resume
                                        </motion.a>
                                        <motion.a
                                          href={selectedApplication.resume_url}
                                          download
                                          className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold rounded-xl transition-all duration-300 shadow-lg"
                                          whileHover={{ scale: 1.05 }}
                                          whileTap={{ scale: 0.95 }}
                                        >
                                          <Download className="h-5 w-5" />
                                          Download
                                        </motion.a>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                      </div>
                    </TableCell>
                  </motion.tr>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredApplications.length === 0 && (
            <motion.div 
              className="text-center py-16"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              <div className="p-6 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 rounded-2xl inline-block mb-6">
                <Users className="h-16 w-16 mx-auto text-sauce-red" />
              </div>
              <p className="text-crust-beige/80 text-xl mb-4">No applications found matching your criteria.</p>
              <p className="text-crust-beige/60">Try adjusting your filters or search terms.</p>
            </motion.div>
          )}
        </motion.div>
            </TabsContent>

            <TabsContent value="stats" className="space-y-6 mt-8">
              <AdminStats />
            </TabsContent>

            <TabsContent value="access-codes" className="space-y-6 mt-8">
              <AdminAccessCodes />
            </TabsContent>

            <TabsContent value="supporters" className="space-y-6 mt-8">
              <AdminSupporters />
            </TabsContent>

            <TabsContent value="deployment" className="space-y-6 mt-8">
              <EdgeFunctionDeployment />
            </TabsContent>

            <TabsContent value="activity" className="space-y-6 mt-8">
              <AdminUserManagement />
            </TabsContent>
          </Tabs>
        </motion.div>
      </div>
    </div>
  );
};

export default AdminDashboard;
