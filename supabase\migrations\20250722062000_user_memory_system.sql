-- User Memory System for tracking progress, context, and personalized AI interactions
-- This enables continuous learning and personalized guidance

-- User Memory Entries table for storing all user interactions and context
CREATE TABLE user_memory_entries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  memory_type VARCHAR NOT NULL CHECK (memory_type IN ('interaction', 'progress', 'insight', 'preference', 'context')),
  content JSONB NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for efficient querying
CREATE INDEX idx_user_memory_user_id ON user_memory_entries(user_id);
CREATE INDEX idx_user_memory_type ON user_memory_entries(memory_type);
CREATE INDEX idx_user_memory_created_at ON user_memory_entries(created_at);
CREATE INDEX idx_user_memory_metadata ON user_memory_entries USING GIN(metadata);

-- User Dev Journey Stages tracking
CREATE TABLE user_dev_journey_stages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  stage_id INTEGER NOT NULL CHECK (stage_id >= 0 AND stage_id <= 5),
  confidence_score INTEGER DEFAULT 50 CHECK (confidence_score >= 0 AND confidence_score <= 100),
  reasoning TEXT,
  next_actions TEXT[],
  applicable_frameworks TEXT[],
  stage_entered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for stage tracking
CREATE INDEX idx_user_stage_user_id ON user_dev_journey_stages(user_id);
CREATE INDEX idx_user_stage_stage_id ON user_dev_journey_stages(stage_id);
CREATE INDEX idx_user_stage_entered_at ON user_dev_journey_stages(stage_entered_at);

-- Contextual Help Requests tracking
CREATE TABLE contextual_help_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  question TEXT NOT NULL,
  user_stage_id INTEGER,
  frameworks_used TEXT[],
  response_summary TEXT,
  satisfaction_rating INTEGER CHECK (satisfaction_rating >= 1 AND satisfaction_rating <= 5),
  follow_up_needed BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for help tracking
CREATE INDEX idx_help_user_id ON contextual_help_requests(user_id);
CREATE INDEX idx_help_stage_id ON contextual_help_requests(user_stage_id);
CREATE INDEX idx_help_created_at ON contextual_help_requests(created_at);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_user_memory_updated_at 
  BEFORE UPDATE ON user_memory_entries 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_stage_updated_at 
  BEFORE UPDATE ON user_dev_journey_stages 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- RLS Policies for user memory entries
ALTER TABLE user_memory_entries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own memory entries" ON user_memory_entries
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own memory entries" ON user_memory_entries
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own memory entries" ON user_memory_entries
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for dev journey stages
ALTER TABLE user_dev_journey_stages ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own stage data" ON user_dev_journey_stages
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own stage data" ON user_dev_journey_stages
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own stage data" ON user_dev_journey_stages
  FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for help requests
ALTER TABLE contextual_help_requests ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own help requests" ON contextual_help_requests
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own help requests" ON contextual_help_requests
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own help requests" ON contextual_help_requests
  FOR UPDATE USING (auth.uid() = user_id); 