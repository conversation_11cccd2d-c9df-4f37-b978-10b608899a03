import React from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import DemoDay from '@/components/DemoDay';
import TimelineAndAudience from '@/components/TimelineAndAudience';
import ApplicationForm from '@/components/ApplicationForm';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Users, Lightbulb, Wrench, DollarSign, Home, Zap, Sparkles, ArrowRight, Target, Trophy, Rocket, Building, Code, Globe, Clock, Calendar, TrendingUp, Award, CheckCircle, Star, Gauge, Timer } from 'lucide-react';
import { motion } from 'framer-motion';
import { useScrollAnimation, scrollVariants, staggerContainer } from '@/hooks/useScrollAnimation';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';

// Complete Stack - Everything Provided
const completeStack = [
  {
    category: "Community & Mentorship",
    items: [
  {
    icon: Users,
        title: "Elite Builder Network",
        description: "Join the most motivated creators in Santa Cruz",
        gradient: "from-blue-500 to-purple-600",
        detail: "Weekly meetups, peer collaboration, shared wins"
  },
  {
    icon: Lightbulb,
        title: "Expert Mentorship",
        description: "Technical, business, and legal guidance on-demand",
        gradient: "from-yellow-400 to-orange-500",
        detail: "Weekly sessions + Slack access to industry veterans"
      }
    ]
  },
  {
    category: "Infrastructure & Resources",
    items: [
  {
    icon: Home,
        title: "Builder's Hub",
        description: "Dedicated workspace and community hangout spot",
        gradient: "from-emerald-400 to-teal-500",
        detail: "Open access, collaborative spaces, pizza always available"
      },
  { 
    icon: Zap, 
        title: "Tech Credits Galore",
        description: "AWS, OpenAI, Figma, Vercel, and 20+ startup tools",
        gradient: "from-purple-400 to-pink-500",
        detail: "Thousands in credits to power your startup"
      }
    ]
  },
  {
    category: "Legal & Financial",
    items: [
      {
        icon: Building,
        title: "Legal Support",
        description: "Legal support for pre-seed stage startups",
        gradient: "from-indigo-400 to-blue-500",
        detail: "Company formation, legal frameworks, compliance guidance"
  },
  { 
    icon: DollarSign, 
        title: "Small Grants",
        description: "Essential cost support to remove development barriers",
        gradient: "from-green-400 to-emerald-500",
        detail: "Covers hosting, APIs, tools - not fundraising replacement"
      }
    ]
  }
];

const TenInTen = () => {
  const { ref, isInView } = useScrollAnimation({
    threshold: 0.1, // More lenient threshold
    rootMargin: '50px' // Start animation earlier
  });
  const isMobile = useIsMobile();

  return (
    <div className="bg-oven-black text-accent-white min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        {/* Hero Section - Enhanced with 10in10 Theme */}
        <section className="relative pt-32 pb-20 overflow-hidden">
          <div className="absolute inset-0">
            {/* Simplified background for mobile - reduce animations */}
            {!isMobile ? (
              <>
                {/* Desktop: Full animations */}
                <motion.div 
                  className="absolute top-1/4 left-1/4 w-[600px] h-[600px] rounded-full bg-gradient-to-r from-sauce-red/25 to-cheese-gold/25 blur-3xl will-change-transform"
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 360],
                    opacity: [0.2, 0.4, 0.2]
                  }}
                  transition={{
                    duration: 20,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                  style={{ transform: 'translateZ(0)' }}
                />
                <motion.div 
                  className="absolute bottom-1/3 right-1/4 w-[500px] h-[500px] rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 blur-3xl will-change-transform"
                  animate={{
                    scale: [1, 1.15, 1],
                    opacity: [0.15, 0.35, 0.15]
                  }}
                  transition={{
                    duration: 16,
                    repeat: Infinity,
                    ease: "linear",
                    delay: 2
                  }}
                  style={{ transform: 'translateZ(0)' }}
                />
              </>
            ) : (
              <>
                {/* Mobile: Static gradients only */}
                <div className="absolute top-1/4 left-1/4 w-[400px] h-[400px] rounded-full bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 blur-2xl" />
                <div className="absolute bottom-1/3 right-1/4 w-[300px] h-[300px] rounded-full bg-gradient-to-r from-purple-500/15 to-pink-500/15 blur-2xl" />
              </>
            )}

            {/* Geometric shapes - desktop only */}
            {!isMobile && [
              { shape: "circle", size: 40, delay: 0, x: 15, y: 25, color: "bg-sauce-red/20" },
              { shape: "square", size: 30, delay: 1, x: 85, y: 35, color: "bg-cheese-gold/25" },
              { shape: "triangle", size: 35, delay: 2, x: 25, y: 65, color: "bg-purple-400/20" },
              { shape: "diamond", size: 25, delay: 0.5, x: 75, y: 70, color: "bg-blue-400/25" }
            ].map((item, i) => (
              <motion.div
                key={i}
                className={`absolute select-none pointer-events-none ${item.color}`}
                style={{
                  left: `${item.x}%`,
                  top: `${item.y}%`,
                  width: `${item.size}px`,
                  height: `${item.size}px`,
                  borderRadius: item.shape === 'circle' ? '50%' : 
                               item.shape === 'triangle' ? '0' : 
                               item.shape === 'diamond' ? '0' : '8px',
                  clipPath: item.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' :
                           item.shape === 'diamond' ? 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)' : 'none'
                }}
                animate={{
                  y: [-20, 20, -20],
                  x: [-10, 10, -10],
                  rotate: [0, 360],
                  opacity: [0.2, 0.6, 0.2],
                  scale: [0.8, 1.1, 0.8]
                }}
                transition={{
                  duration: 8 + i * 0.5,
                  repeat: Infinity,
                  delay: item.delay,
                  ease: "linear"
                }}
              />
            ))}

            {/* Sparkle particles - reduced on mobile */}
            {(isMobile ? [...Array(2)] : [...Array(6)]).map((_, i) => (
              <motion.div
                key={`sparkle-${i}`}
                className="absolute w-1 h-1 rounded-full"
                style={{
                  left: `${20 + (i * 20)}%`,
                  top: `${30 + (i % 2) * 30}%`,
                  backgroundColor: i % 2 === 0 ? '#FF4747' : '#FFB74D',
                  willChange: 'transform, opacity'
                }}
                animate={{
                  scale: [0, 1, 0],
                  opacity: [0, 0.8, 0],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  delay: i * 1,
                  ease: "linear"
                }}
              />
            ))}
          </div>

          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div 
                className="inline-flex items-center gap-2 px-4 py-2 sm:px-6 sm:py-3 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 backdrop-blur-sm border border-sauce-red/20 rounded-full text-sauce-red font-semibold text-xs sm:text-sm mb-6 sm:mb-8"
                whileHover={!isMobile ? { scale: 1.05 } : undefined}
              >
                <Sparkles className="w-3 h-3 sm:w-4 sm:h-4" />
                10 Startups in 10 Weeks
                <Sparkles className="w-3 h-3 sm:w-4 sm:h-4" />
              </motion.div>
              
              {/* Fixed responsive sizing for "10 in 10" */}
              <div className="flex items-center justify-center gap-2 sm:gap-4 lg:gap-8 mb-6 sm:mb-8 px-2">
                <motion.div 
                  className="text-6xl sm:text-8xl md:text-9xl lg:text-[10rem] xl:text-[12rem] font-black text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold leading-none"
                  animate={!isMobile ? { 
                    textShadow: [
                      "0 0 20px rgba(255, 71, 71, 0.5)",
                      "0 0 40px rgba(255, 71, 71, 0.8)",
                      "0 0 20px rgba(255, 71, 71, 0.5)"
                    ]
                  } : undefined}
                  transition={!isMobile ? { duration: 3, repeat: Infinity } : undefined}
                >
                  10
                </motion.div>
                <motion.div 
                  className="text-3xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-accent-white/60 leading-none"
                  animate={!isMobile ? { opacity: [0.6, 1, 0.6] } : undefined}
                  transition={!isMobile ? { duration: 2, repeat: Infinity } : undefined}
                >
                  in
                </motion.div>
                <motion.div 
                  className="text-6xl sm:text-8xl md:text-9xl lg:text-[10rem] xl:text-[12rem] font-black text-transparent bg-clip-text bg-gradient-to-r from-cheese-gold to-sauce-red leading-none"
                  animate={!isMobile ? { 
                    textShadow: [
                      "0 0 20px rgba(255, 183, 77, 0.5)",
                      "0 0 40px rgba(255, 183, 77, 0.8)",
                      "0 0 20px rgba(255, 183, 77, 0.5)"
                    ]
                  } : undefined}
                  transition={!isMobile ? { duration: 3, repeat: Infinity, delay: 1.5 } : undefined}
                >
                  10
                </motion.div>
              </div>
              
              {/* Stats cards - simplified for mobile */}
              <motion.div 
                className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-6 max-w-6xl mx-auto mb-6 sm:mb-8"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
              >
                {[
                  {
                    icon: Rocket,
                    value: "10",
                    label: "Teams Selected",
                    gradient: "from-blue-400 to-purple-500"
                  },
                  {
                    icon: Zap,
                    value: "10",
                    label: "Week Sprint",
                    gradient: "from-sauce-red to-pink-500"
                  },
                  {
                    emoji: "🍕",
                    value: "10",
                    label: "Pizzas Per Team",
                    gradient: "from-cheese-gold to-orange-400"
                  },
                  {
                    icon: Trophy,
                    value: "1",
                    label: "Epic Demo Day",
                    gradient: "from-green-400 to-blue-500"
                  }
                ].map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    className="relative group"
                    initial={{ opacity: 0, y: 30, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{ 
                      duration: 0.5, 
                      delay: 0.3 + index * 0.1,
                      ease: "easeOut"
                    }}
                    whileHover={!isMobile ? { 
                      scale: 1.05, 
                      y: -5,
                      transition: {
                        duration: 0.2, 
                        ease: "easeOut"
                      }
                    } : undefined}
                  >
                    <motion.div 
                      className="relative bg-gradient-to-br from-accent-white/15 to-accent-white/5 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-4 sm:p-6 lg:p-8 border-2 border-accent-white/30 hover:border-accent-white/50 shadow-2xl transition-all duration-200"
                      style={{
                        background: `linear-gradient(145deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05))`,
                        willChange: 'transform'
                      }}
                    >
                      {/* Icon container */}
                      <motion.div 
                        className={`w-10 h-10 sm:w-12 sm:h-12 lg:w-16 lg:h-16 rounded-xl sm:rounded-2xl bg-gradient-to-r ${stat.gradient} ${stat.emoji ? 'text-lg sm:text-2xl lg:text-3xl flex items-center justify-center' : 'p-2 sm:p-3 lg:p-4'} mx-auto mb-2 sm:mb-4 shadow-2xl relative overflow-hidden`}
                        whileHover={!isMobile ? { scale: 1.1 } : undefined}
                        transition={{ duration: 0.3, ease: "easeOut" }}
                      >
                        <div className="absolute inset-0 bg-white/20 rounded-xl sm:rounded-2xl" />
                        {stat.emoji ? (
                          <span className="relative z-10">{stat.emoji}</span>
                        ) : (
                          <stat.icon className="w-6 h-6 sm:w-6 sm:h-6 lg:w-8 lg:h-8 text-white relative z-10" />
                        )}
                      </motion.div>
                      
                      {/* Value */}
                      <div className="text-2xl sm:text-3xl lg:text-4xl font-black text-accent-white mb-1 sm:mb-2 relative">
                        {stat.value}
                        {/* Sparkle overlay - desktop only */}
                        {!isMobile && (
                          <motion.div
                            className="absolute -top-1 -right-1 text-xs"
                            animate={{ 
                              opacity: [0.3, 1, 0.3],
                              rotate: [0, 360]
                            }}
                            transition={{ 
                              duration: 3, 
                              repeat: Infinity,
                              delay: index * 0.8,
                              ease: "linear"
                            }}
                          >
                            ✨
                          </motion.div>
                        )}
                      </div>
                    
                      {/* Label */}
                      <div className="text-xs sm:text-sm font-bold text-crust-beige/90 tracking-wide uppercase">
                        {stat.label}
                      </div>
                    </motion.div>
                  </motion.div>
                ))}
              </motion.div>
              
              <p className="text-lg sm:text-xl lg:text-2xl text-crust-beige/90 max-w-4xl mx-auto mb-6 sm:mb-8 px-4">
                Santa Cruz's most intensive startup accelerator. Build, launch, and demo your startup 
                in just 10 weeks with everything you need to succeed.
              </p>
              
              <motion.a
                href="#apply"
                className="inline-flex items-center gap-2 sm:gap-3 px-6 py-3 sm:px-8 sm:py-4 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-xl sm:rounded-2xl font-bold text-oven-black text-base sm:text-lg shadow-2xl cursor-pointer"
                whileHover={!isMobile ? { scale: 1.05, y: -2 } : undefined}
                whileTap={{ scale: 0.95 }}
              >
                <Rocket className="w-5 h-5 sm:w-6 sm:h-6" />
                Launch Your Startup Journey
                <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
              </motion.a>
            </motion.div>
          </div>
        </section>

        {/* The Complete Stack - Redesigned with better mobile layout */}
        <section className="relative py-16 sm:py-24 lg:py-32 overflow-hidden bg-gradient-to-br from-crust-beige via-accent-white to-crust-beige" ref={ref}>
          {/* Simplified background particles for mobile */}
          <div className="absolute inset-0 opacity-30">
            {(isMobile ? [...Array(10)] : [...Array(30)]).map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 sm:w-2 sm:h-2 bg-sauce-red/20 rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={!isMobile ? {
                  scale: [0, 1, 0],
                  opacity: [0, 0.5, 0],
                } : undefined}
                transition={!isMobile ? {
                  duration: 3 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2,
                } : undefined}
              />
            ))}
          </div>

          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              animate={isInView ? "visible" : "hidden"}
              variants={scrollVariants}
              className="text-center mb-12 sm:mb-16 lg:mb-20"
            >
              <motion.div 
                className="inline-flex items-center gap-2 px-4 py-2 sm:px-6 sm:py-3 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 backdrop-blur-sm border border-sauce-red/20 rounded-full text-sauce-red font-semibold text-xs sm:text-sm mb-6 sm:mb-8"
                whileHover={!isMobile ? { scale: 1.05 } : undefined}
              >
                <Zap className="w-3 h-3 sm:w-4 sm:h-4" />
                Everything You Need
                <Zap className="w-3 h-3 sm:w-4 sm:h-4" />
              </motion.div>
              
              <h2 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-black text-oven-black mb-4 sm:mb-6 px-4">
                The Complete{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                  Startup Stack
                </span>
              </h2>
              <p className="text-base sm:text-lg lg:text-xl text-oven-black/70 max-w-3xl mx-auto px-4">
                From idea to incorporation to Demo Day - we provide everything you need to succeed
              </p>
            </motion.div>
            
            <motion.div 
              className="space-y-8 sm:space-y-12 lg:space-y-16"
              initial="hidden"
              animate={isInView ? "visible" : "hidden"}
              variants={staggerContainer}
            >
              {completeStack.map((category, categoryIndex) => (
                <motion.div 
                  key={category.category} 
                  variants={scrollVariants}
                  className="relative"
                >
                  <div className="text-center mb-6 sm:mb-8 lg:mb-12">
                    <h3 className="text-2xl sm:text-3xl font-bold text-oven-black mb-4 px-4">{category.category}</h3>
                  </div>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
                    {category.items.map((item, itemIndex) => (
                      <motion.div
                        key={item.title}
                        className="group relative"
                        whileHover={!isMobile ? { y: -10, scale: 1.02 } : undefined}
                        transition={{ type: "spring", stiffness: 300 }}
                      >
                        {!isMobile && (
                          <div className={`absolute -inset-1 bg-gradient-to-r ${item.gradient} rounded-2xl sm:rounded-3xl blur-xl opacity-0 group-hover:opacity-60 transition-opacity duration-500`} />
                        )}
                        
                        <Card className="relative bg-accent-white/90 backdrop-blur-md border border-oven-black/10 rounded-2xl sm:rounded-3xl shadow-xl h-full overflow-hidden">
                          <div className="absolute inset-0 bg-gradient-to-br from-accent-white/50 to-transparent" />
                          
                          <CardContent className="relative z-10 p-4 sm:p-6 lg:p-8">
                            <div className="flex items-start space-x-4 sm:space-x-6">
                              <motion.div 
                                className={`p-3 sm:p-4 rounded-xl sm:rounded-2xl bg-gradient-to-r ${item.gradient} shadow-lg flex-shrink-0`}
                                whileHover={!isMobile ? { rotate: 360, scale: 1.1 } : undefined}
                                transition={{ duration: 0.6 }}
                              >
                                <item.icon className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
                              </motion.div>
                              <div className="flex-1 min-w-0">
                                <h4 className="text-lg sm:text-xl lg:text-2xl font-bold text-oven-black group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-sauce-red group-hover:to-cheese-gold transition-all duration-300 mb-2 sm:mb-3">
                                  {item.title}
                                </h4>
                                <p className="text-oven-black/70 text-sm sm:text-base lg:text-lg mb-3 sm:mb-4">{item.description}</p>
                                <p className="text-oven-black/60 text-xs sm:text-sm leading-relaxed">{item.detail}</p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Timeline and Audience */}
        <TimelineAndAudience />

        {/* Full Demo Day Component */}
        <DemoDay />

        {/* Application Section */}
        <ApplicationForm />
      </main>
      <Footer />
    </div>
  );
};

export default TenInTen; 