-- Fix migration for Pie Fi Dashboard Database Schema
-- This handles existing partial schema and fixes foreign key issues

-- Create tables only if they don't exist
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT auth.uid(),
  email VARCHAR UNIQUE NOT NULL,
  full_name VA<PERSON>HAR NOT NULL,
  track VARCHAR CHECK (track IN ('newbie', 'builder', 'scaler')),
  onboarding_completed BOOLEAN DEFAULT false,
  notion_workspace_id VARCHAR,
  discord_user_id VARCHAR,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS user_projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR NOT NULL,
  description TEXT,
  current_stage VARCHAR DEFAULT 'ideation',
  track <PERSON><PERSON><PERSON><PERSON> CHECK (track IN ('newbie', 'builder', 'scaler')),
  weekly_goals JSONB DEFAULT '[]'::jsonb,
  progress_data JSONB DEFAULT '{}'::jsonb,
  notion_page_id VARCHAR,
  discord_thread_id VARCHAR,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS daily_updates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES user_projects(id) ON DELETE SET NULL,
  content TEXT NOT NULL,
  ai_insights JSONB DEFAULT '{}'::jsonb,
  sentiment_score FLOAT CHECK (sentiment_score >= 0 AND sentiment_score <= 1),
  recommended_resources JSONB DEFAULT '[]'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS ai_conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  conversation_type VARCHAR NOT NULL CHECK (conversation_type IN ('onboarding', 'daily_update', 'help', 'goal_setting')),
  messages JSONB NOT NULL DEFAULT '[]'::jsonb,
  context JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS pie_fi_mentors (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  expertise TEXT[] NOT NULL DEFAULT '{}',
  bio TEXT NOT NULL,
  availability VARCHAR,
  email VARCHAR,
  discord_handle VARCHAR,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS pie_fi_resources (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR NOT NULL,
  description TEXT NOT NULL,
  resource_type VARCHAR NOT NULL CHECK (resource_type IN ('guide', 'template', 'tool', 'course', 'article')),
  url VARCHAR,
  tags TEXT[] DEFAULT '{}',
  track_relevance TEXT[] DEFAULT '{}' CHECK (track_relevance <@ ARRAY['newbie', 'builder', 'scaler']),
  difficulty_level VARCHAR DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add missing columns to existing tables
DO $$
BEGIN
  -- Add active column to pie_fi_mentors if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'pie_fi_mentors' AND column_name = 'active') THEN
    ALTER TABLE pie_fi_mentors ADD COLUMN active BOOLEAN DEFAULT true;
  END IF;

  -- Add active column to pie_fi_resources if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'pie_fi_resources' AND column_name = 'active') THEN
    ALTER TABLE pie_fi_resources ADD COLUMN active BOOLEAN DEFAULT true;
  END IF;

  -- Add track column to users if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'track') THEN
    ALTER TABLE users ADD COLUMN track VARCHAR CHECK (track IN ('newbie', 'builder', 'scaler'));
  END IF;

  -- Add onboarding_completed column to users if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'onboarding_completed') THEN
    ALTER TABLE users ADD COLUMN onboarding_completed BOOLEAN DEFAULT false;
  END IF;
END $$;

-- Create indexes only if they don't exist
DO $$ 
BEGIN
  -- Users indexes
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_users_email') THEN
    CREATE INDEX idx_users_email ON users(email);
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_users_track') THEN
    CREATE INDEX idx_users_track ON users(track);
  END IF;

  -- Projects indexes
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_user_projects_user_id') THEN
    CREATE INDEX idx_user_projects_user_id ON user_projects(user_id);
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_user_projects_track') THEN
    CREATE INDEX idx_user_projects_track ON user_projects(track);
  END IF;

  -- Daily updates indexes
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_daily_updates_user_id') THEN
    CREATE INDEX idx_daily_updates_user_id ON daily_updates(user_id);
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_daily_updates_created_at') THEN
    CREATE INDEX idx_daily_updates_created_at ON daily_updates(created_at DESC);
  END IF;

  -- AI conversations indexes
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_ai_conversations_user_id') THEN
    CREATE INDEX idx_ai_conversations_user_id ON ai_conversations(user_id);
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_ai_conversations_type') THEN
    CREATE INDEX idx_ai_conversations_type ON ai_conversations(conversation_type);
  END IF;

  -- Resources indexes
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_pie_fi_resources_track_relevance') THEN
    CREATE INDEX idx_pie_fi_resources_track_relevance ON pie_fi_resources USING GIN(track_relevance);
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_pie_fi_resources_tags') THEN
    CREATE INDEX idx_pie_fi_resources_tags ON pie_fi_resources USING GIN(tags);
  END IF;
END $$;

-- Create updated_at triggers only if they don't exist
DO $$
BEGIN
  -- Create trigger function if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'update_updated_at_column') THEN
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $trigger$
    BEGIN
      NEW.updated_at = NOW();
      RETURN NEW;
    END;
    $trigger$ LANGUAGE plpgsql;
  END IF;

  -- Create triggers for each table if they don't exist
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_users_updated_at') THEN
    CREATE TRIGGER update_users_updated_at
      BEFORE UPDATE ON users
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_user_projects_updated_at') THEN
    CREATE TRIGGER update_user_projects_updated_at
      BEFORE UPDATE ON user_projects
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_daily_updates_updated_at') THEN
    CREATE TRIGGER update_daily_updates_updated_at
      BEFORE UPDATE ON daily_updates
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_ai_conversations_updated_at') THEN
    CREATE TRIGGER update_ai_conversations_updated_at
      BEFORE UPDATE ON ai_conversations
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_pie_fi_mentors_updated_at') THEN
    CREATE TRIGGER update_pie_fi_mentors_updated_at
      BEFORE UPDATE ON pie_fi_mentors
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_pie_fi_resources_updated_at') THEN
    CREATE TRIGGER update_pie_fi_resources_updated_at
      BEFORE UPDATE ON pie_fi_resources
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_updates ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE pie_fi_mentors ENABLE ROW LEVEL SECURITY;
ALTER TABLE pie_fi_resources ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist and recreate them
DO $$
BEGIN
  -- Users policies
  DROP POLICY IF EXISTS "Users can read own profile" ON users;
  CREATE POLICY "Users can read own profile" ON users
    FOR SELECT USING (auth.uid() = id);

  DROP POLICY IF EXISTS "Users can update own profile" ON users;
  CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

  DROP POLICY IF EXISTS "Users can insert own profile" ON users;
  CREATE POLICY "Users can insert own profile" ON users
    FOR INSERT WITH CHECK (auth.uid() = id);

  -- Projects policies
  DROP POLICY IF EXISTS "Users can manage own projects" ON user_projects;
  CREATE POLICY "Users can manage own projects" ON user_projects
    FOR ALL USING (auth.uid() = user_id);

  -- Daily updates policies
  DROP POLICY IF EXISTS "Users can manage own daily updates" ON daily_updates;
  CREATE POLICY "Users can manage own daily updates" ON daily_updates
    FOR ALL USING (auth.uid() = user_id);

  -- AI conversations policies
  DROP POLICY IF EXISTS "Users can manage own conversations" ON ai_conversations;
  CREATE POLICY "Users can manage own conversations" ON ai_conversations
    FOR ALL USING (auth.uid() = user_id);

  -- Mentors policies (read-only for all authenticated users)
  DROP POLICY IF EXISTS "Authenticated users can read mentors" ON pie_fi_mentors;
  
  -- Check if active column exists before creating policy that uses it
  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'pie_fi_mentors' AND column_name = 'active') THEN
    CREATE POLICY "Authenticated users can read mentors" ON pie_fi_mentors
      FOR SELECT TO authenticated USING (active = true);
  ELSE
    CREATE POLICY "Authenticated users can read mentors" ON pie_fi_mentors
      FOR SELECT TO authenticated USING (true);
  END IF;

  -- Resources policies (read-only for all authenticated users)
  DROP POLICY IF EXISTS "Authenticated users can read resources" ON pie_fi_resources;
  
  -- Check if active column exists before creating policy that uses it
  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'pie_fi_resources' AND column_name = 'active') THEN
    CREATE POLICY "Authenticated users can read resources" ON pie_fi_resources
      FOR SELECT TO authenticated USING (active = true);
  ELSE
    CREATE POLICY "Authenticated users can read resources" ON pie_fi_resources
      FOR SELECT TO authenticated USING (true);
  END IF;
END $$;

-- Clean up any existing invalid data
DELETE FROM ai_conversations WHERE user_id = '00000000-0000-0000-0000-000000000000';

-- Insert sample mentors (only if table is empty)
INSERT INTO pie_fi_mentors (name, expertise, bio, availability, email, discord_handle, active)
SELECT * FROM (VALUES
  ('Sarah Chen', ARRAY['user research', 'validation', 'customer discovery', 'design thinking'], 'Expert in user-centered design with 8+ years at Google. Helped launch 3 successful B2C products reaching 10M+ users.', 'Wednesdays 2-4pm PST', '<EMAIL>', 'sarahchen#1234', true),
  ('Alex Wang', ARRAY['technical', 'architecture', 'scaling', 'backend', 'databases'], 'Full-stack engineer turned CTO. Built systems handling 100M+ requests/day. Previously at Stripe and Airbnb.', 'Fridays 1-3pm PST', '<EMAIL>', 'alexwang#5678', true),
  ('Maria Rodriguez', ARRAY['product management', 'go-to-market', 'growth', 'analytics'], 'Former PM at Notion. Led product growth from 1M to 20M users. Expert in PLG strategies.', 'Mondays 10am-12pm PST', '<EMAIL>', 'mariarodriguez#9012', true),
  ('David Kim', ARRAY['fundraising', 'business strategy', 'legal', 'operations'], 'Serial entrepreneur with 2 exits. Partner at early-stage VC fund. Expert in seed to Series A fundraising.', 'Thursdays 3-5pm PST', '<EMAIL>', 'davidkim#3456', true),
  ('Priya Patel', ARRAY['marketing', 'content', 'social media', 'community'], 'Built 100K+ follower communities. Former Head of Marketing at fast-growing B2B SaaS (0-$10M ARR).', 'Tuesdays 11am-1pm PST', '<EMAIL>', 'priyapatel#7890', true)
) AS sample_data(name, expertise, bio, availability, email, discord_handle, active)
WHERE NOT EXISTS (SELECT 1 FROM pie_fi_mentors LIMIT 1);

-- Insert sample resources (only if table is empty)
INSERT INTO pie_fi_resources (title, description, resource_type, url, tags, track_relevance, active)
SELECT * FROM (VALUES
  ('Customer Interview Template', 'A comprehensive 5-step guide to conducting effective user interviews with script templates and follow-up questions.', 'template', '/resources/customer-interview-template', ARRAY['validation', 'research', 'interviews', 'customer discovery'], ARRAY['newbie', 'builder'], true),
  ('MVP Development Guide', 'Step-by-step framework for building your first version fast. Includes tech stack recommendations and timeline.', 'guide', '/resources/mvp-guide', ARRAY['building', 'technical', 'mvp', 'development'], ARRAY['builder'], true),
  ('Startup Pitch Deck Template', 'Proven pitch deck template used by 50+ successful startups. Includes investor feedback examples.', 'template', '/resources/pitch-deck-template', ARRAY['fundraising', 'pitch', 'investors'], ARRAY['builder', 'scaler'], true),
  ('Growth Marketing Playbook', 'Complete guide to growth loops, viral mechanics, and scaling user acquisition from 0 to 1M users.', 'guide', '/resources/growth-playbook', ARRAY['growth', 'marketing', 'acquisition', 'scaling'], ARRAY['scaler'], true),
  ('User Research Toolkit', 'Collection of surveys, interview scripts, and analysis frameworks for understanding your users deeply.', 'tool', '/resources/user-research-toolkit', ARRAY['research', 'users', 'feedback', 'validation'], ARRAY['newbie', 'builder'], true),
  ('Financial Model Template', 'Comprehensive Excel/Sheets model for SaaS, marketplace, and e-commerce businesses with scenario planning.', 'template', '/resources/financial-model', ARRAY['finance', 'planning', 'fundraising'], ARRAY['builder', 'scaler'], true),
  ('Product Launch Checklist', '50-point checklist covering everything from pre-launch to post-launch metrics and iteration.', 'guide', '/resources/launch-checklist', ARRAY['launch', 'product', 'go-to-market'], ARRAY['builder', 'scaler'], true),
  ('No-Code App Builder Course', '4-hour video course on building apps without coding using tools like Bubble, Webflow, and Airtable.', 'course', '/resources/no-code-course', ARRAY['no-code', 'building', 'prototyping'], ARRAY['newbie', 'builder'], true),
  ('Founder Mental Health Guide', 'Evidence-based strategies for managing stress, avoiding burnout, and maintaining peak performance.', 'guide', '/resources/mental-health-guide', ARRAY['wellness', 'productivity', 'mental health'], ARRAY['newbie', 'builder', 'scaler'], true),
  ('Notion Project Management Template', 'Complete workspace template for managing your startup with kanban boards, OKRs, and team collaboration.', 'template', '/resources/notion-template', ARRAY['productivity', 'organization', 'project management'], ARRAY['newbie', 'builder', 'scaler'], true)
) AS sample_data(title, description, resource_type, url, tags, track_relevance, active)
WHERE NOT EXISTS (SELECT 1 FROM pie_fi_resources LIMIT 1); 