import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Loader2,
  Database,
  Brain,
  Target,
  MessageSquare,
  User,
  FileText
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import dashboardRefreshService, { RefreshProgress, RefreshResult } from '@/services/dashboardRefreshService';

interface DashboardRefreshModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  onRefreshComplete: (result: RefreshResult) => void;
}

const DashboardRefreshModal: React.FC<DashboardRefreshModalProps> = ({
  isOpen,
  onClose,
  userId,
  onRefreshComplete
}) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(true);
  const [progress, setProgress] = useState<RefreshProgress[]>([]);
  const [result, setResult] = useState<RefreshResult | null>(null);

  const refreshSteps = [
    { id: 'clear_cache', label: 'Clear Cached AI Analyses', icon: Database },
    { id: 'stage_analysis', label: 'Regenerate Stage Analysis', icon: Target },
    { id: 'milestones', label: 'Regenerate Milestone Personalization', icon: FileText },
    { id: 'daily_updates', label: 'Re-analyze Daily Updates', icon: MessageSquare },
    { id: 'user_context', label: 'Refresh User Context & Insights', icon: User },
    { id: 'milestone_feedback', label: 'Regenerate Milestone Feedback', icon: Brain }
  ];

  const handleStartRefresh = async () => {
    setShowConfirmation(false);
    setIsRefreshing(true);
    setProgress([]);
    setResult(null);

    try {
      const refreshResult = await dashboardRefreshService.refreshDashboard(
        userId,
        (progressUpdate) => {
          setProgress(prev => {
            const existing = prev.find(p => p.step === progressUpdate.step);
            if (existing) {
              return prev.map(p => p.step === progressUpdate.step ? progressUpdate : p);
            } else {
              return [...prev, progressUpdate];
            }
          });
        }
      );

      setResult(refreshResult);
      onRefreshComplete(refreshResult);
    } catch (error) {
      console.error('Refresh failed:', error);
      setResult({
        success: false,
        message: `Refresh failed: ${error}`,
        stepsCompleted: 0,
        totalSteps: 6,
        errors: [`Critical error: ${error}`]
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleClose = () => {
    if (!isRefreshing) {
      setShowConfirmation(true);
      setProgress([]);
      setResult(null);
      onClose();
    }
  };

  const getStepStatus = (stepId: string) => {
    const stepProgress = progress.find(p => p.step === stepId);
    if (!stepProgress) return 'pending';
    if (stepProgress.error) return 'error';
    if (stepProgress.completed) return 'completed';
    return 'running';
  };

  const getStepIcon = (stepId: string, IconComponent: React.ComponentType<any>) => {
    const status = getStepStatus(stepId);
    
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-400" />;
      case 'running':
        return <Loader2 className="h-5 w-5 text-blue-400 animate-spin" />;
      default:
        return <IconComponent className="h-5 w-5 text-accent-white/40" />;
    }
  };

  const overallProgress = progress.length > 0 
    ? Math.max(...progress.map(p => p.progress))
    : 0;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl bg-gradient-to-br from-pizza-red/95 to-pizza-red/90 backdrop-blur-xl border border-accent-white/20">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-accent-white flex items-center gap-3">
            <motion.div
              animate={{ rotate: isRefreshing ? 360 : 0 }}
              transition={{ duration: 2, repeat: isRefreshing ? Infinity : 0, ease: "linear" }}
            >
              <RefreshCw className="h-6 w-6 text-cheese-gold" />
            </motion.div>
            Refresh Dashboard
          </DialogTitle>
          <DialogDescription className="text-accent-white/70">
            Regenerate all AI-powered content while preserving your progress data
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {showConfirmation && !isRefreshing && !result && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              <Alert className="bg-accent-white/10 border-cheese-gold/30">
                <AlertTriangle className="h-4 w-4 text-cheese-gold" />
                <AlertDescription className="text-accent-white/90">
                  This will regenerate all AI-powered content while preserving your progress data. 
                  Your onboarding responses, milestone completions, and daily updates will be kept.
                </AlertDescription>
              </Alert>

              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-accent-white">What will be refreshed:</h3>
                <div className="grid gap-2">
                  {refreshSteps.map((step) => (
                    <div key={step.id} className="flex items-center gap-3 p-3 bg-accent-white/5 rounded-lg">
                      <step.icon className="h-4 w-4 text-cheese-gold" />
                      <span className="text-accent-white/80">{step.label}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  onClick={handleStartRefresh}
                  className="flex-1 bg-gradient-to-r from-cheese-gold to-cheese-gold/80 hover:from-cheese-gold/90 hover:to-cheese-gold/70 text-pizza-red font-semibold"
                >
                  Start Refresh
                </Button>
                <Button
                  onClick={handleClose}
                  variant="outline"
                  className="border-accent-white/30 text-accent-white hover:bg-accent-white/10"
                >
                  Cancel
                </Button>
              </div>
            </motion.div>
          )}

          {(isRefreshing || result) && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              {/* Overall Progress */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-accent-white font-medium">Overall Progress</span>
                  <span className="text-accent-white/70">{Math.round(overallProgress)}%</span>
                </div>
                <Progress value={overallProgress} className="h-2" />
              </div>

              {/* Step Progress */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-accent-white">Refresh Steps</h3>
                <div className="space-y-2">
                  {refreshSteps.map((step) => {
                    const status = getStepStatus(step.id);
                    const stepProgress = progress.find(p => p.step === step.id);
                    
                    return (
                      <motion.div
                        key={step.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        className={`flex items-center gap-3 p-3 rounded-lg transition-all duration-200 ${
                          status === 'completed' ? 'bg-green-500/10 border border-green-500/20' :
                          status === 'error' ? 'bg-red-500/10 border border-red-500/20' :
                          status === 'running' ? 'bg-blue-500/10 border border-blue-500/20' :
                          'bg-accent-white/5 border border-accent-white/10'
                        }`}
                      >
                        {getStepIcon(step.id, step.icon)}
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="text-accent-white font-medium">{step.label}</span>
                            {status === 'completed' && (
                              <Badge variant="secondary" className="text-xs bg-green-500/20 text-green-400">
                                Complete
                              </Badge>
                            )}
                            {status === 'error' && (
                              <Badge variant="secondary" className="text-xs bg-red-500/20 text-red-400">
                                Error
                              </Badge>
                            )}
                            {status === 'running' && (
                              <Badge variant="secondary" className="text-xs bg-blue-500/20 text-blue-400">
                                Running
                              </Badge>
                            )}
                          </div>
                          {stepProgress?.message && (
                            <p className={`text-sm mt-1 ${
                              stepProgress.error ? 'text-red-400' : 'text-accent-white/70'
                            }`}>
                              {stepProgress.message}
                            </p>
                          )}
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              </div>

              {/* Result Summary */}
              {result && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="space-y-4"
                >
                  <Alert className={`${
                    result.success 
                      ? 'bg-green-500/10 border-green-500/30' 
                      : 'bg-red-500/10 border-red-500/30'
                  }`}>
                    {result.success ? (
                      <CheckCircle className="h-4 w-4 text-green-400" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-400" />
                    )}
                    <AlertDescription className="text-accent-white/90">
                      {result.message}
                    </AlertDescription>
                  </Alert>

                  {result.newStage !== undefined && (
                    <div className="p-3 bg-accent-white/5 rounded-lg">
                      <p className="text-accent-white/80">
                        <strong>Updated Stage:</strong> Stage {result.newStage} ({result.newConfidence}% confidence)
                      </p>
                    </div>
                  )}

                  {result.errors.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="text-accent-white font-medium">Errors:</h4>
                      <div className="space-y-1">
                        {result.errors.map((error, idx) => (
                          <p key={idx} className="text-red-400 text-sm">• {error}</p>
                        ))}
                      </div>
                    </div>
                  )}

                  <Button
                    onClick={handleClose}
                    className="w-full bg-gradient-to-r from-cheese-gold to-cheese-gold/80 hover:from-cheese-gold/90 hover:to-cheese-gold/70 text-pizza-red font-semibold"
                  >
                    Close
                  </Button>
                </motion.div>
              )}
            </motion.div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DashboardRefreshModal;
