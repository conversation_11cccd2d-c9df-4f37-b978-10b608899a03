import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  Target, 
  Users, 
  DollarSign, 
  Calendar,
  CheckCircle,
  Brain,
  Award,
  Rocket,
  Download,
  Share,
  BarChart3,
  Lightbulb,
  Shield,
  Clock,
  ArrowRight,
  Star,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';
import MarkdownText from './MarkdownText';
import { supabase } from '@/integrations/supabase/client';
import devJourneyService from '@/services/devJourneyService';
import stageProgressService from '@/services/stageProgressService';
import userMemoryService from '@/services/userMemoryService';

interface InvestorPitchViewProps {
  userId: string;
  currentStage: number;
  stageConfidence: number;
}

interface PitchData {
  executiveSummary: {
    projectName: string;
    problemStatement: string;
    solutionApproach: string;
    targetAudience: string;
    uniqueValueProposition: string;
    marketOpportunity: string;
  };
  progressMetrics: {
    overallCompletion: number;
    currentStage: number;
    stagesCompleted: number;
    totalMilestones: number;
    completedMilestones: number;
    aiConfidenceAverage: number;
    projectViabilityScore: number;
  };
  milestoneAchievements: Array<{
    id: string;
    title: string;
    stage: number;
    completedDate: string;
    aiConfidence: number;
    keyDeliverables: string[];
    validationEvidence: string;
  }>;
  upcomingMilestones: Array<{
    id: string;
    title: string;
    stage: number;
    description: string;
    estimatedCompletion: string;
  }>;
  fundingNeeds: {
    currentStage: string;
    nextMilestone: string;
    supportNeeded: string[];
    projectedTimeline: string;
  };
}

const InvestorPitchView: React.FC<InvestorPitchViewProps> = ({
  userId,
  currentStage,
  stageConfidence
}) => {
  const [pitchData, setPitchData] = useState<PitchData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);

  useEffect(() => {
    loadPitchData();
  }, [userId, currentStage]);

  const loadPitchData = async () => {
    try {
      setIsLoading(true);
      
      // Get onboarding data for executive summary
      const { data: onboardingData } = await supabase
        .from('onboarding_responses')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1);

      // Get comprehensive user profile for viability score
      const { data: profileData } = await supabase
        .from('comprehensive_user_profiles')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1);

      // Get all milestone submissions
      const { data: milestoneSubmissions, error: submissionsError } = await supabase
        .from('milestone_submissions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: true });

      if (submissionsError) {
        console.error('Error fetching milestone submissions:', submissionsError);
      }

      // Get user context and memories
      const userContext = await userMemoryService.buildUserContext(userId);

      // Process and structure the data
      const processedData = await processPitchData(
        onboardingData?.[0] || null,
        profileData?.[0] || null,
        milestoneSubmissions || [],
        userContext || {},
        currentStage,
        stageConfidence
      );

      setPitchData(processedData);
    } catch (error) {
      console.error('Error loading pitch data:', error);
      toast.error('Failed to load pitch data');
    } finally {
      setIsLoading(false);
    }
  };

  const processPitchData = async (
    onboarding: any,
    profile: any,
    submissions: any[],
    userContext: any,
    stage: number,
    confidence: number
  ): Promise<PitchData> => {
    // Extract project name from various sources
    const projectName = extractProjectName(onboarding, userContext) || 'Innovative Project';

    // Process milestone achievements
    const achievements = (submissions || [])
      .filter(s => s && (s.is_approved || s.ai_feedback))
      .map(submission => {
        let aiConfidence = 75; // Default
        let keyDeliverables: string[] = [];

        try {
          if (submission.ai_insights && typeof submission.ai_insights === 'string') {
            const insights = JSON.parse(submission.ai_insights);
            aiConfidence = insights.finalConfidence || insights.confidence || 75;
            keyDeliverables = insights.keyInsights || [];
          }
        } catch (e) {
          console.warn('Error parsing AI insights:', e);
          // Use defaults
        }

        return {
          id: submission.milestone_id,
          title: submission.metadata?.personalized_title || submission.milestone_id.replace(/_/g, ' '),
          stage: submission.stage_id,
          completedDate: submission.approval_date || submission.created_at,
          aiConfidence,
          keyDeliverables,
          validationEvidence: submission.submission_content?.substring(0, 200) + '...' || ''
        };
      })
      .sort((a, b) => new Date(a.completedDate).getTime() - new Date(b.completedDate).getTime());

    // Calculate progress metrics
    const totalStages = devJourneyService.getAllStages().length;
    const completedStages = achievements.reduce((acc, curr) => {
      const stageSet = new Set(acc);
      stageSet.add(curr.stage);
      return Array.from(stageSet);
    }, [] as number[]).length;

    const overallCompletion = Math.round(((stage + 1) / totalStages) * 100);
    const avgConfidence = achievements.length > 0 
      ? Math.round(achievements.reduce((sum, a) => sum + a.aiConfidence, 0) / achievements.length)
      : confidence;

    // Get upcoming milestones
    const allStages = devJourneyService.getAllStages();
    const upcomingMilestones = [];
    
    for (let stageId = stage; stageId < Math.min(stage + 2, totalStages); stageId++) {
      const stageMilestones = stageProgressService.getStageMilestones(stageId);
      const incompleteMilestones = stageMilestones.filter(m => 
        !achievements.some(a => a.id === m.id)
      );
      
      upcomingMilestones.push(...incompleteMilestones.slice(0, 3).map(m => ({
        id: m.id,
        title: m.title,
        stage: stageId,
        description: m.description,
        estimatedCompletion: getEstimatedCompletion(stageId, stage)
      })));
    }

    return {
      executiveSummary: {
        projectName,
        problemStatement: onboarding?.problem_description || 'Addressing a significant market need',
        solutionApproach: onboarding?.solution_approach || 'Innovative solution approach',
        targetAudience: onboarding?.target_audience || 'Target market segment',
        uniqueValueProposition: onboarding?.unique_value_proposition || 'Unique competitive advantage',
        marketOpportunity: profile?.market_opportunity || 'Significant market opportunity'
      },
      progressMetrics: {
        overallCompletion,
        currentStage: stage,
        stagesCompleted: completedStages,
        totalMilestones: submissions.length,
        completedMilestones: achievements.length,
        aiConfidenceAverage: avgConfidence,
        projectViabilityScore: profile?.project_viability_score || 75
      },
      milestoneAchievements: achievements,
      upcomingMilestones: upcomingMilestones.slice(0, 5),
      fundingNeeds: {
        currentStage: allStages[stage]?.name || 'Development Stage',
        nextMilestone: upcomingMilestones[0]?.title || 'Next Phase',
        supportNeeded: profile?.recommended_next_steps || ['Technical development', 'Market validation', 'Team building'],
        projectedTimeline: getProjectedTimeline(stage, achievements.length)
      }
    };
  };

  const extractProjectName = (onboarding: any, userContext: any): string => {
    // Try to extract project name from various sources
    if (onboarding?.solution_approach) {
      const words = onboarding.solution_approach.split(' ').slice(0, 3);
      return words.join(' ').replace(/[^a-zA-Z0-9\s]/g, '');
    }
    
    if (userContext?.project_info?.name) {
      return userContext.project_info.name;
    }
    
    return 'Innovation Project';
  };

  const getEstimatedCompletion = (milestoneStage: number, currentStage: number): string => {
    const weeksFromNow = (milestoneStage - currentStage + 1) * 2;
    const date = new Date();
    date.setDate(date.getDate() + (weeksFromNow * 7));
    return date.toLocaleDateString();
  };

  const getProjectedTimeline = (stage: number, milestonesCompleted: number): string => {
    const velocity = milestonesCompleted / Math.max(1, stage + 1);
    const remainingStages = 6 - stage;
    const estimatedWeeks = Math.ceil(remainingStages / Math.max(0.5, velocity)) * 2;
    return `${estimatedWeeks} weeks to next major milestone`;
  };

  const handleExport = async () => {
    setIsExporting(true);
    try {
      // Create a simplified version for export
      const exportData = {
        projectName: pitchData?.executiveSummary.projectName,
        summary: pitchData?.executiveSummary,
        metrics: pitchData?.progressMetrics,
        achievements: pitchData?.milestoneAchievements?.length,
        viabilityScore: pitchData?.progressMetrics.projectViabilityScore,
        exportDate: new Date().toISOString()
      };

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${pitchData?.executiveSummary.projectName || 'project'}-pitch-data.json`;
      link.click();
      
      URL.revokeObjectURL(url);
      toast.success('Pitch data exported successfully!');
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export pitch data');
    } finally {
      setIsExporting(false);
    }
  };

  const handleShare = async () => {
    try {
      const shareText = `Check out my project progress: ${pitchData?.executiveSummary.projectName} - ${pitchData?.progressMetrics.overallCompletion}% complete with ${pitchData?.progressMetrics.projectViabilityScore}% viability score!`;
      
      if (navigator.share) {
        await navigator.share({
          title: `${pitchData?.executiveSummary.projectName} - Project Pitch`,
          text: shareText,
          url: window.location.href
        });
      } else {
        await navigator.clipboard.writeText(shareText);
        toast.success('Pitch summary copied to clipboard!');
      }
    } catch (error) {
      console.error('Share error:', error);
      toast.error('Failed to share pitch data');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-12">
        <div className="text-center">
          <BarChart3 className="h-12 w-12 animate-pulse text-purple-400 mx-auto mb-4" />
          <p className="text-accent-white/70">Generating your investor pitch...</p>
        </div>
      </div>
    );
  }

  if (!pitchData) {
    return (
      <div className="text-center p-12">
        <p className="text-accent-white/70">Unable to generate pitch data. Please ensure you have completed your onboarding.</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header with Export/Share */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-accent-white mb-2">
            Investor Pitch: {pitchData.executiveSummary.projectName}
          </h1>
          <p className="text-accent-white/70">
            Professional presentation of your project progress and viability
          </p>
        </div>
        <div className="flex gap-3">
          <Button
            onClick={handleShare}
            variant="outline"
            className="bg-accent-white/10 border-accent-white/20 text-accent-white hover:bg-accent-white/20"
          >
            <Share className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting}
            className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white"
          >
            {isExporting ? (
              <Clock className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Download className="h-4 w-4 mr-2" />
            )}
            Export Data
          </Button>
        </div>
      </div>

      {/* Executive Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="grid grid-cols-1 lg:grid-cols-2 gap-6"
      >
        <Card className="bg-gradient-to-br from-purple-500/10 to-blue-500/10 border-purple-400/30">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-purple-400" />
              Executive Summary
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium text-purple-400 mb-2">Problem Statement</h4>
              <div className="text-sm text-accent-white/80">
                <MarkdownText content={pitchData.executiveSummary.problemStatement} />
              </div>
            </div>
            <div>
              <h4 className="font-medium text-blue-400 mb-2">Solution Approach</h4>
              <div className="text-sm text-accent-white/80">
                <MarkdownText content={pitchData.executiveSummary.solutionApproach} />
              </div>
            </div>
            <div>
              <h4 className="font-medium text-green-400 mb-2">Unique Value Proposition</h4>
              <div className="text-sm text-accent-white/80">
                <MarkdownText content={pitchData.executiveSummary.uniqueValueProposition} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border-green-400/30">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-green-400" />
              Project Viability Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-400 mb-1">
                  {pitchData.progressMetrics.projectViabilityScore}%
                </div>
                <div className="text-xs text-accent-white/60">Viability Score</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-400 mb-1">
                  {pitchData.progressMetrics.overallCompletion}%
                </div>
                <div className="text-xs text-accent-white/60">Journey Complete</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-400 mb-1">
                  {pitchData.progressMetrics.completedMilestones}
                </div>
                <div className="text-xs text-accent-white/60">Milestones</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-yellow-400 mb-1">
                  {pitchData.progressMetrics.aiConfidenceAverage}%
                </div>
                <div className="text-xs text-accent-white/60">AI Confidence</div>
              </div>
            </div>
            
            <div className="mt-6">
              <div className="flex justify-between text-sm text-accent-white/70 mb-2">
                <span>Overall Progress</span>
                <span>{pitchData.progressMetrics.overallCompletion}%</span>
              </div>
              <Progress value={pitchData.progressMetrics.overallCompletion} className="h-3" />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Progress Timeline */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="bg-gradient-to-r from-accent-white/5 to-accent-white/10 border-accent-white/20">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <Calendar className="h-5 w-5 text-blue-400" />
              Development Timeline & Achievements
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Completed Milestones */}
              {pitchData.milestoneAchievements.length > 0 && (
                <div>
                  <h4 className="font-medium text-green-400 mb-4 flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Completed Achievements ({pitchData.milestoneAchievements.length})
                  </h4>
                  <div className="space-y-3">
                    {pitchData.milestoneAchievements.map((achievement, index) => (
                      <motion.div
                        key={achievement.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-start gap-4 p-4 bg-green-500/5 border border-green-400/20 rounded-lg"
                      >
                        <div className="flex-shrink-0 w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                          <CheckCircle className="h-4 w-4 text-green-400" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h5 className="font-medium text-accent-white">{achievement.title}</h5>
                            <div className="flex items-center gap-2">
                              <Badge className="bg-purple-500/20 text-purple-200 text-xs">
                                Stage {achievement.stage}
                              </Badge>
                              <Badge className="bg-blue-500/20 text-blue-200 text-xs">
                                {achievement.aiConfidence}% AI Confidence
                              </Badge>
                            </div>
                          </div>
                          <p className="text-sm text-accent-white/70 mb-2">
                            {achievement.validationEvidence}
                          </p>
                          {achievement.keyDeliverables.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {achievement.keyDeliverables.slice(0, 3).map((deliverable, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs">
                                  {deliverable}
                                </Badge>
                              ))}
                            </div>
                          )}
                          <div className="text-xs text-accent-white/50 mt-2">
                            Completed: {new Date(achievement.completedDate).toLocaleDateString()}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              {/* Upcoming Milestones */}
              {pitchData.upcomingMilestones.length > 0 && (
                <div>
                  <h4 className="font-medium text-blue-400 mb-4 flex items-center gap-2">
                    <Target className="h-4 w-4" />
                    Upcoming Milestones ({pitchData.upcomingMilestones.length})
                  </h4>
                  <div className="space-y-3">
                    {pitchData.upcomingMilestones.map((milestone, index) => (
                      <motion.div
                        key={milestone.id}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-start gap-4 p-4 bg-blue-500/5 border border-blue-400/20 rounded-lg"
                      >
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                          <Target className="h-4 w-4 text-blue-400" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h5 className="font-medium text-accent-white">{milestone.title}</h5>
                            <Badge className="bg-blue-500/20 text-blue-200 text-xs">
                              Stage {milestone.stage}
                            </Badge>
                          </div>
                          <p className="text-sm text-accent-white/70 mb-2">
                            {milestone.description}
                          </p>
                          <div className="text-xs text-accent-white/50">
                            Estimated completion: {milestone.estimatedCompletion}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Funding & Support Needs */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="grid grid-cols-1 lg:grid-cols-2 gap-6"
      >
        <Card className="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 border-yellow-400/30">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <Rocket className="h-5 w-5 text-yellow-400" />
              Current Status & Next Steps
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium text-yellow-400 mb-2">Current Stage</h4>
              <p className="text-accent-white/80">{pitchData.fundingNeeds.currentStage}</p>
            </div>
            <div>
              <h4 className="font-medium text-orange-400 mb-2">Next Major Milestone</h4>
              <p className="text-accent-white/80">{pitchData.fundingNeeds.nextMilestone}</p>
            </div>
            <div>
              <h4 className="font-medium text-red-400 mb-2">Projected Timeline</h4>
              <p className="text-accent-white/80">{pitchData.fundingNeeds.projectedTimeline}</p>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-500/10 to-pink-500/10 border-red-400/30">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-red-400" />
              Support & Investment Needs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <h4 className="font-medium text-red-400 mb-3">Areas Requiring Support:</h4>
              {pitchData.fundingNeeds.supportNeeded.map((need, index) => (
                <div key={index} className="flex items-center gap-3 p-2 bg-red-500/5 rounded">
                  <ArrowRight className="h-4 w-4 text-red-400 flex-shrink-0" />
                  <span className="text-accent-white/80 text-sm">{need}</span>
                </div>
              ))}
            </div>

            <div className="mt-6 p-4 bg-gradient-to-r from-red-500/10 to-pink-500/10 rounded-lg border border-red-400/20">
              <div className="flex items-center gap-2 mb-2">
                <Star className="h-4 w-4 text-yellow-400" />
                <span className="font-medium text-accent-white">Investment Opportunity</span>
              </div>
              <p className="text-sm text-accent-white/80">
                Join us in building {pitchData.executiveSummary.projectName} - a validated project with
                {pitchData.progressMetrics.projectViabilityScore}% viability score and proven development progress.
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Market Opportunity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <Card className="bg-gradient-to-r from-emerald-500/10 to-teal-500/10 border-emerald-400/30">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <Users className="h-5 w-5 text-emerald-400" />
              Market Opportunity & Target Audience
            </CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-emerald-400 mb-3">Target Audience</h4>
              <div className="text-accent-white/80 mb-4">
                <MarkdownText content={pitchData.executiveSummary.targetAudience} />
              </div>

              <div className="flex items-center gap-2 p-3 bg-emerald-500/5 rounded-lg border border-emerald-400/20">
                <Shield className="h-4 w-4 text-emerald-400" />
                <span className="text-sm text-emerald-400 font-medium">Validated Market Need</span>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-teal-400 mb-3">Market Opportunity</h4>
              <div className="text-accent-white/80 mb-4">
                <MarkdownText content={pitchData.executiveSummary.marketOpportunity} />
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div className="text-center p-3 bg-teal-500/5 rounded border border-teal-400/20">
                  <div className="text-lg font-bold text-teal-400">
                    {pitchData.progressMetrics.stagesCompleted}/{devJourneyService.getAllStages().length}
                  </div>
                  <div className="text-xs text-accent-white/60">Stages Complete</div>
                </div>
                <div className="text-center p-3 bg-purple-500/5 rounded border border-purple-400/20">
                  <div className="text-lg font-bold text-purple-400">
                    {pitchData.progressMetrics.aiConfidenceAverage}%
                  </div>
                  <div className="text-xs text-accent-white/60">Avg AI Confidence</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Call to Action */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="text-center"
      >
        <Card className="bg-gradient-to-r from-purple-500/20 to-blue-500/20 border-purple-400/40">
          <CardContent className="p-8">
            <div className="flex items-center justify-center gap-2 mb-4">
              <Zap className="h-6 w-6 text-yellow-400" />
              <h3 className="text-2xl font-bold text-accent-white">Ready to Accelerate Growth</h3>
            </div>
            <p className="text-accent-white/80 mb-6 max-w-2xl mx-auto">
              {pitchData.executiveSummary.projectName} has demonstrated concrete progress with validated milestones
              and AI-verified achievements. Join us in scaling this proven concept to market success.
            </p>
            <div className="flex items-center justify-center gap-4">
              <Button
                onClick={handleShare}
                className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white"
              >
                <Share className="h-4 w-4 mr-2" />
                Share This Pitch
              </Button>
              <Button
                onClick={handleExport}
                variant="outline"
                className="bg-accent-white/10 border-accent-white/20 text-accent-white hover:bg-accent-white/20"
              >
                <Download className="h-4 w-4 mr-2" />
                Export Full Report
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default InvestorPitchView;
