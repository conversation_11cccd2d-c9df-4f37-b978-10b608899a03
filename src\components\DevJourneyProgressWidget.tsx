import React from 'react';
import { motion } from 'framer-motion';
import { 
  Lightbulb, 
  Users, 
  Hammer, 
  Rocket, 
  TrendingUp, 
  Building,
  ChevronRight,
  CheckCircle
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import devJourneyService from '@/services/devJourneyService';

interface DevJourneyProgressWidgetProps {
  currentStage: number;
  confidence: number;
  className?: string;
}

const DevJourneyProgressWidget: React.FC<DevJourneyProgressWidgetProps> = ({
  currentStage,
  confidence,
  className = ''
}) => {
  const stages = devJourneyService.getAllStages();
  
  // Stage icons mapping
  const stageIcons = {
    0: Lightbulb,
    1: Users,
    2: Hammer,
    3: Rocket,
    4: TrendingUp,
    5: Building
  };

  // Calculate overall journey progress
  const overallProgress = Math.round(((currentStage + 1) / stages.length) * 100);

  return (
    <Card className={`bg-gradient-to-br from-accent-white/10 to-accent-white/5 backdrop-blur-sm border border-accent-white/20 ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-accent-white text-lg">Dev Journey Progress</CardTitle>
          <Badge variant="outline" className="text-purple-400 border-purple-400/30">
            {confidence}% Confidence
          </Badge>
        </div>
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-accent-white/60">
            <span>Overall Progress</span>
            <span>{overallProgress}%</span>
          </div>
          <Progress value={overallProgress} className="h-2" />
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Current Stage Highlight */}
        <div className="p-4 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg border border-purple-500/20">
          <div className="flex items-center gap-3 mb-2">
            <div className="flex-shrink-0">
              {React.createElement(stageIcons[currentStage as keyof typeof stageIcons] || Lightbulb, {
                className: "h-5 w-5 text-purple-400"
              })}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-accent-white font-semibold truncate">
                {stages[currentStage]?.title || `Stage ${currentStage}`}
              </h3>
            </div>
            <div className="flex-shrink-0">
              <Badge variant="outline" className="text-xs text-purple-400 border-purple-400/30">
                Stage {currentStage}
              </Badge>
            </div>
          </div>
          <p className="text-accent-white/80 text-sm mb-3">
            {stages[currentStage]?.description || 'Current stage description'}
          </p>
          
          {/* Enhanced Stage Progress Indicator */}
          {confidence > 0 && (
            <div className="mb-3">
              <div className="flex justify-between text-xs text-accent-white/70 mb-1">
                <span>Stage Progress</span>
                <span>{confidence}%</span>
              </div>
              <div className="w-full bg-accent-white/10 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${confidence}%` }}
                />
              </div>
            </div>
          )}
          
          {/* Key Support Areas */}
          {stages[currentStage]?.supportNeeded && (
            <div className="space-y-2">
              <p className="text-accent-white/70 text-xs font-medium">Focus Areas:</p>
              <div className="grid grid-cols-1 gap-1">
                {stages[currentStage].supportNeeded.slice(0, 3).map((support, idx) => (
                  <div key={idx} className="flex items-center gap-2 text-xs">
                    <div className="w-1.5 h-1.5 rounded-full bg-purple-400" />
                    <span className="text-accent-white/80">{support}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Stage Timeline */}
        <div className="space-y-3">
          <h4 className="text-accent-white font-medium text-sm">Journey Timeline</h4>
          <div className="space-y-2">
            {stages.map((stage, idx) => {
              const IconComponent = stageIcons[idx as keyof typeof stageIcons] || Lightbulb;
              const isCompleted = idx < currentStage;
              const isCurrent = idx === currentStage;
              const isUpcoming = idx > currentStage;
              
              return (
                <motion.div
                  key={stage.id}
                  className={`flex items-start gap-3 p-3 rounded-lg transition-all duration-200 ${
                    isCurrent ? 'bg-purple-500/10 border border-purple-500/20' :
                    isCompleted ? 'bg-green-500/5 border border-green-500/10' :
                    'bg-accent-white/5 border border-accent-white/10'
                  }`}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: idx * 0.1 }}
                >
                  <div className={`flex-shrink-0 p-1.5 rounded-full ${
                    isCurrent ? 'bg-purple-500/20' :
                    isCompleted ? 'bg-green-500/20' :
                    'bg-accent-white/10'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="h-3 w-3 text-green-400" />
                    ) : (
                      <IconComponent className={`h-3 w-3 ${
                        isCurrent ? 'text-purple-400' :
                        isCompleted ? 'text-green-400' :
                        'text-accent-white/40'
                      }`} />
                    )}
                  </div>

                  <div className="flex-1 min-w-0 space-y-1">
                    <div className="flex items-center gap-2 flex-wrap">
                      <span className={`text-xs font-medium truncate ${
                        isCurrent ? 'text-purple-400' :
                        isCompleted ? 'text-green-400' :
                        'text-accent-white/60'
                      }`}>
                        {stage.title}
                      </span>
                      {isCurrent && (
                        <Badge variant="secondary" className="text-xs px-1.5 py-0.5 flex-shrink-0">
                          Current
                        </Badge>
                      )}
                    </div>
                    <p className={`text-xs leading-relaxed ${
                      isCurrent ? 'text-accent-white/80' :
                      isCompleted ? 'text-accent-white/60' :
                      'text-accent-white/40'
                    }`}>
                      {stage.description}
                    </p>
                  </div>

                  {!isCompleted && !isCurrent && (
                    <div className="flex-shrink-0 self-center">
                      <ChevronRight className="h-3 w-3 text-accent-white/30" />
                    </div>
                  )}
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Next Stage Preview */}
        {currentStage < stages.length - 1 && (
          <div className="p-3 bg-accent-white/5 rounded-lg border border-accent-white/10">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-accent-white/70 text-xs font-medium">Next Stage:</span>
              <Badge variant="outline" className="text-xs text-blue-400 border-blue-400/30">
                {stages[currentStage + 1]?.title}
              </Badge>
            </div>
            <p className="text-accent-white/60 text-xs">
              {stages[currentStage + 1]?.description}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DevJourneyProgressWidget; 