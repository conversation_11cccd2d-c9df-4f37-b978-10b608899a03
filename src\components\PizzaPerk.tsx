import React from 'react';
import { motion } from 'framer-motion';
import { useScrollAnimation, scrollVariants } from '@/hooks/useScrollAnimation';

const PizzaPerk = () => {
  const { ref, isInView } = useScrollAnimation();

  return (
    <section className="relative py-32 overflow-hidden bg-gradient-to-br from-cheese-gold/5 via-sauce-red/5 to-cheese-gold/5" ref={ref}>
      {/* Animated background */}
      <div className="absolute inset-0">
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute text-4xl opacity-10"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-20, 20, -20],
              rotate: [0, 360, 0],
              opacity: [0.05, 0.15, 0.05],
            }}
            transition={{
              duration: 8 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          >
            🍕
          </motion.div>
        ))}
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={scrollVariants}
          className="relative group"
        >
          {/* Glow effect */}
          <div className="absolute -inset-8 bg-gradient-to-r from-cheese-gold/20 to-sauce-red/20 rounded-3xl blur-2xl opacity-50 group-hover:opacity-75 transition-opacity duration-500" />
          
          {/* Main content card */}
          <div className="relative bg-accent-white/10 backdrop-blur-md border border-accent-white/20 rounded-3xl p-12 shadow-2xl">
            <motion.div
              className="text-8xl mb-8"
              animate={{ 
                rotate: [0, 10, -10, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 3, 
                repeat: Infinity,
                ease: "easeInOut" 
              }}
            >
              🍕
            </motion.div>
            
            <h2 className="text-4xl sm:text-5xl font-black text-accent-white mb-6">
              Yes, Seriously.{" "}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-cheese-gold to-sauce-red">
                10 Pizzas.
              </span>
            </h2>
            
            <p className="text-xl text-crust-beige/90 mb-8 leading-relaxed max-w-2xl mx-auto">
              Every builder team gets{" "}
              <span className="font-bold text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                10 pizzas throughout the program
              </span>{" "}
              as a perk of being part of our community. Because the best ideas happen over pizza. 🥧
            </p>
            
            <div className="flex items-center justify-center gap-3 text-accent-white font-semibold mt-4">
              <span className="text-2xl">🎉</span>
              <span className="text-lg text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                It's part of the Pie Fi experience!
              </span>
              <span className="text-2xl">🎉</span>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default PizzaPerk;
