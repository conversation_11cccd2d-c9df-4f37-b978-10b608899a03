import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Zap, Rocket } from 'lucide-react';
import { motion } from 'framer-motion';
import { floatingVariants, scaleInVariants } from '@/hooks/useScrollAnimation';

const Hero = () => {
  const handleLearnMoreClick = () => {
    window.location.href = '/ten-in-ten';
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-oven-black via-oven-black to-purple-900/20">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        {/* Main gradient orb */}
        <motion.div 
          className="absolute top-1/4 right-1/4 w-96 h-96 rounded-full bg-gradient-to-r from-sauce-red/30 to-cheese-gold/30 blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        
        {/* Secondary orb */}
        <motion.div 
          className="absolute bottom-1/4 left-1/4 w-64 h-64 rounded-full bg-gradient-to-r from-purple-500/20 to-blue-500/20 blur-2xl"
          animate={{
            scale: [1.2, 1, 1.2],
            x: [0, 50, 0],
            y: [0, -30, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Floating geometric shapes */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className={`absolute w-4 h-4 ${i % 2 === 0 ? 'bg-cheese-gold/40' : 'bg-sauce-red/40'} rounded-full`}
            style={{
              left: `${20 + (i * 15)}%`,
              top: `${30 + (i * 10)}%`,
            }}
            animate={{
              y: [-20, 20, -20],
              x: [-10, 10, -10],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 4 + i,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.5,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center pt-28 pb-24">
        <motion.div 
          className="mb-8"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <motion.div
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 backdrop-blur-sm border border-sauce-red/20 rounded-full text-sauce-red font-semibold text-sm mb-12 hover:from-sauce-red/15 hover:to-cheese-gold/15 transition-colors"
            whileHover={{ scale: 1.05 }}
          >
            <Sparkles className="w-4 h-4" />
            🥧 The Next-Gen Builder Camp for Santa Cruz
            <Sparkles className="w-4 h-4" />
          </motion.div>
        </motion.div>
        
        {/* Animated Logo */}
        <motion.div
          initial={{ opacity: 1, scale: 1, y: 0, rotate: 0 }}
          animate={{
            opacity: 1,
            scale: [1, 1.05, 1],
            y: [0, -10, 0],
            rotate: [0, 3, -3, 0]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            times: [0, 0.5, 1],
            repeatDelay: 0
          }}
          className="my-2 flex justify-center"
        >
          <img 
            src="/logo.png"
            alt="Pie Fi Logo"
            className="w-[400px] md:w-[450px] aspect-square object-contain drop-shadow-2xl"
          />
        </motion.div>
        
        {/* Main Title with 3D-like effect */}
        <motion.div
          initial={{ opacity: 0, y: 50, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 1, delay: 0.4 }}
          className="mb-8"
        >
          <motion.h1 
            className="text-6xl sm:text-7xl lg:text-8xl font-black tracking-tight mb-4 text-transparent bg-clip-text bg-gradient-to-r from-sauce-red via-cheese-gold to-sauce-red"
            style={{
              filter: "drop-shadow(0 4px 12px rgba(255, 71, 71, 0.3))",
            }}
            whileHover={{ scale: 1.02 }}
          >
            Pie Fi
          </motion.h1>
          
          <motion.div 
            className="relative"
            variants={floatingVariants}
            animate="animate"
          >
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-accent-white">
              10 Weeks. 10 Teams.{" "}
              <span className="relative">
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-cheese-gold to-sauce-red">
                  Real Products.
                </span>
                <motion.div
                  className="absolute -top-2 -right-2"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                >
                  <Zap className="w-6 h-6 text-cheese-gold" />
                </motion.div>
              </span>
            </h2>
          </motion.div>
        </motion.div>
        
        {/* Description */}
        <motion.p 
          className="text-xl sm:text-2xl text-crust-beige/90 max-w-4xl mx-auto mb-12 leading-relaxed"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          A 10-week summer builder camp that turns students and early builders into founders.
          Build something people want, with the resources and community to make it happen.
        </motion.p>
        

        
        {/* Removed Family & Friends button - moved to PieFireOverview */}
        
        {/* Bottom tagline */}
        <motion.div 
          className="mt-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 1.4 }}
        >
          <p className="text-lg text-crust-beige/70 mb-2">
            "The next wave will be built this summer, not in five years"
          </p>
          <div className="flex justify-center">
            <motion.div
              className="flex items-center justify-center bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 backdrop-blur-sm text-accent-white font-semibold px-8 py-4 rounded-2xl border border-sauce-red/30"
              animate={{ 
                scale: [1, 1.02, 1],
                opacity: [0.9, 1, 0.9]
              }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            >
              <span className="text-2xl -ml-1 mr-1">🔥</span>
              <span className="pr-1">Applications open now! </span>
            </motion.div>
          </div>
        </motion.div>

        {/* Large Sponsors Section - Positioned Between Hero and Next Section */}
        <motion.div 
          className="mt-16 mb-8"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.6 }}
        >
          <div className="text-center">
            <div className="flex flex-col sm:flex-row items-center justify-center gap-8 sm:gap-16 px-4">
              {/* Co-Host Section */}
              <div className="text-center">
                <p className="text-lg text-crust-beige/70 mb-4 font-medium">Co-hosted with</p>
                <motion.a
                  href="https://www.santacruzworks.org/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-accent-white/90 backdrop-blur-md rounded-3xl px-12 py-8 border border-accent-white/40 flex items-center justify-center shadow-2xl block cursor-pointer"
                  whileHover={{ scale: 1.05, y: -5 }}
                  transition={{ duration: 0.3 }}
                >
                  <img src="/SCW+logo++(500+x+200+px)+(2).png" alt="Santa Cruz Works" className="h-16 w-auto sm:h-20" />
                </motion.a>
              </div>

              {/* Primary Sponsor Section */}
              <div className="text-center">
                <p className="text-lg text-crust-beige/70 mb-4 font-medium">Primary Sponsor</p>
                <motion.a
                  href="https://www.goodwinlaw.com/en"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-gradient-to-r from-sauce-red/5 to-cheese-gold/5 backdrop-blur-md rounded-3xl px-12 py-8 border-2 border-sauce-red/30 flex items-center justify-center shadow-2xl block cursor-pointer"
                  whileHover={{ scale: 1.05, y: -5, borderColor: "rgba(255, 71, 71, 0.5)" }}
                  transition={{ duration: 0.3 }}
                >
                  <img src="/Goodwin PNG.png" alt="Goodwin" className="h-16 w-auto sm:h-20" />
                </motion.a>
              </div>
            </div>
            
            {/* Sponsor CTA Button */}
            <motion.div 
              className="mt-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.8 }}
            >
              <motion.a
                href="https://gamma.app/docs/Pie-Fi-The-Builder-Launchpad-mh687fqtet9qx0u?mode=doc"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-cheese-gold/20 to-sauce-red/20 backdrop-blur-sm text-accent-white font-semibold text-sm rounded-xl border border-cheese-gold/30 hover:border-cheese-gold/50 transition-all duration-300"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <span>Want to be a sponsor?</span>
                <ArrowRight className="w-4 h-4" />
              </motion.a>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Hero;
