import { supabase } from '@/integrations/supabase/client';

interface DashboardCacheEntry {
  id?: string;
  user_id: string;
  stage_id: number;
  data_hash: string;
  dashboard_data: any;
  created_at?: string;
  expires_at: string;
}

class DashboardCacheService {
  private generateDataHash(data: any): string {
    // Create a hash based on key data points that would affect the dashboard
    const hashData = {
      stage: data.stage,
      achievements: data.achievements?.length || 0,
      submissions: data.submissions?.length || 0,
      lastUpdate: data.lastUpdate
    };
    
    const hashString = JSON.stringify(hashData);
    
    // Simple hash function for browser compatibility
    let hash = 0;
    for (let i = 0; i < hashString.length; i++) {
      const char = hashString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }

  async getCachedDashboardData(
    userId: string, 
    stageId: number, 
    dataContext: any
  ): Promise<any | null> {
    try {
      const dataHash = this.generateDataHash(dataContext);
      
      // First, try to get from localStorage for immediate access
      const localKey = `dashboard_cache_${userId}_${stageId}_${dataHash}`;
      const localData = localStorage.getItem(localKey);
      
      if (localData) {
        try {
          const parsed = JSON.parse(localData);
          if (new Date(parsed.expires_at) > new Date()) {
            console.log('✅ Using cached dashboard data from localStorage');
            return parsed.dashboard_data;
          } else {
            localStorage.removeItem(localKey);
          }
        } catch (e) {
          localStorage.removeItem(localKey);
        }
      }

      // For now, only use localStorage caching to avoid Supabase JSON query complexity
      // TODO: Implement Supabase caching with simpler schema if needed

      return null;
    } catch (error) {
      console.error('Error in getCachedDashboardData:', error);
      return null;
    }
  }

  async cacheDashboardData(
    userId: string,
    stageId: number,
    dashboardData: any,
    dataContext: any,
    expirationHours: number = 6
  ): Promise<void> {
    try {
      const dataHash = this.generateDataHash(dataContext);
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + expirationHours);

      // Cache in localStorage for immediate access
      const localKey = `dashboard_cache_${userId}_${stageId}_${dataHash}`;
      const cacheEntry = {
        dashboard_data: dashboardData,
        expires_at: expiresAt.toISOString()
      };
      localStorage.setItem(localKey, JSON.stringify(cacheEntry));

      // For now, only use localStorage caching to avoid Supabase JSON query complexity
      // TODO: Implement Supabase caching with simpler schema if needed
      console.log('💾 Cached comprehensive dashboard data in localStorage');
    } catch (error) {
      console.error('Error in cacheDashboardData:', error);
    }
  }

  async invalidateDashboardCache(userId: string, stageId?: number): Promise<void> {
    try {
      // Clear localStorage cache
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(`dashboard_cache_${userId}`)) {
          if (!stageId || key.includes(`_${stageId}_`)) {
            localStorage.removeItem(key);
          }
        }
      });

      // For now, only clear localStorage cache
      // TODO: Clear Supabase cache when implemented
      console.log('🧹 Invalidated dashboard cache');
    } catch (error) {
      console.error('Error in invalidateDashboardCache:', error);
    }
  }

  async cleanupExpiredCache(): Promise<void> {
    try {
      // Clean localStorage
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('dashboard_cache_')) {
          try {
            const data = JSON.parse(localStorage.getItem(key) || '');
            if (new Date(data.expires_at) <= new Date()) {
              localStorage.removeItem(key);
            }
          } catch (e) {
            localStorage.removeItem(key);
          }
        }
      });

      // For now, only clean localStorage
      // TODO: Clean Supabase cache when implemented
      console.log('🧹 Cleaned up expired cache entries');
    } catch (error) {
      console.error('Error in cleanupExpiredCache:', error);
    }
  }
}

export default new DashboardCacheService();
