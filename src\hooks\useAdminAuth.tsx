import { useState, useEffect } from 'react';

const ADMIN_SESSION_KEY = 'piefi_admin_session';
const ADMIN_PASSWORD = import.meta.env.VITE_ADMIN_PASSWORD as string | undefined;

export const useAdminAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if admin is already authenticated
    const savedSession = sessionStorage.getItem(ADMIN_SESSION_KEY);
    if (savedSession) {
      try {
        const session = JSON.parse(savedSession);
        const isValid = session.timestamp && 
          (Date.now() - session.timestamp < 24 * 60 * 60 * 1000); // 24 hours
        
        if (isValid) {
          setIsAuthenticated(true);
        } else {
          sessionStorage.removeItem(ADMIN_SESSION_KEY);
        }
      } catch (error) {
        sessionStorage.removeItem(ADMIN_SESSION_KEY);
      }
    }
    setIsLoading(false);
  }, []);

  useEffect(() => {
    const root = window.document.documentElement;
    if (isAuthenticated) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
    // Cleanup function to remove the class if the hook is ever unmounted
    return () => {
      root.classList.remove('dark');
    };
  }, [isAuthenticated]);

  const login = (password: string): boolean => {
    console.log('🔐 Admin login attempt');
    console.log('Environment ADMIN_PASSWORD:', ADMIN_PASSWORD ? '***SET***' : 'NOT SET');
    console.log('Input password length:', password.length);

    if (!ADMIN_PASSWORD) {
      console.error('VITE_ADMIN_PASSWORD is not set.');
      return false;
    }

    if (password === ADMIN_PASSWORD) {
      const session = {
        authenticated: true,
        timestamp: Date.now()
      };
      sessionStorage.setItem(ADMIN_SESSION_KEY, JSON.stringify(session));
      setIsAuthenticated(true);
      return true;
    }
    return false;
  };

  const logout = () => {
    sessionStorage.removeItem(ADMIN_SESSION_KEY);
    setIsAuthenticated(false);
  };

  return {
    isAuthenticated,
    isLoading,
    login,
    logout
  };
}; 