import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Menu, X, Sparkles } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { name: 'Home', href: '/' },
    { name: '10 in 10 Program', href: '/ten-in-ten' },
    { name: 'Family & Friends', href: '/family-friends' }
  ];

  if (!mounted) {
    return (
      <nav className="fixed w-full z-50 bg-transparent">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-1">
              <div className="w-14 h-14 relative flex items-center justify-center">
                <img 
                  src="/logo.png" 
                  alt="Pie Fi Logo" 
                  className="w-14 h-14 object-contain"
                />
              </div>
              <span className="text-2xl font-black text-accent-white">
                Pie <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">Fi</span>
              </span>
            </div>
          </div>
        </div>
      </nav>
    );
  }

  return (
    <motion.nav 
      className={`fixed w-full z-50 transition-all duration-300 ${
        scrolled 
          ? 'bg-oven-black/80 backdrop-blur-xl border-b border-sauce-red/20 shadow-xl' 
          : 'bg-transparent'
      }`}
      initial={{ y: 0, opacity: 1 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-6">
          {/* Logo */}
          <motion.div 
            className="relative group"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="absolute -inset-1 bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 rounded-xl blur-md opacity-60 group-hover:opacity-100 transition-opacity duration-300" />
            <a href="/" className="relative flex items-center space-x-1">
              <motion.div 
                className="w-14 h-14 relative flex items-center justify-center"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
              >
                <img 
                  src="/logo.png" 
                  alt="Pie Fi Logo" 
                  className="w-14 h-14 object-contain"
                />
              </motion.div>
              <span className="text-2xl font-black text-accent-white">
                Pie <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">Fi</span>
              </span>
            </a>
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item, index) => (
              <motion.div
                key={item.name}
                className="relative group"
                initial={{ opacity: 1, y: 0 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                <div className="absolute -inset-2 bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 rounded-lg blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <motion.a
                  href={item.href}
                  className="relative text-accent-white font-semibold hover:text-transparent hover:bg-clip-text hover:bg-gradient-to-r hover:from-sauce-red hover:to-cheese-gold transition-all duration-300 px-4 py-2 rounded-lg"
                  whileHover={{ y: -2 }}
                >
                  {item.name}
                  <motion.div
                    className="absolute -bottom-1 left-4 right-4 h-0.5 bg-gradient-to-r from-sauce-red to-cheese-gold scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
                  />
                </motion.a>
              </motion.div>
            ))}
            
            {/* Apply Now Button */}
            <motion.div
              initial={{ opacity: 1, y: 0 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="relative group"
            >
              <div className="absolute -inset-2 bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 rounded-lg blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <Button 
                onClick={() => window.location.href = '/apply#application-form'}
                className="relative bg-gradient-to-r from-sauce-red to-sauce-red/90 hover:from-sauce-red/90 hover:to-sauce-red text-accent-white font-bold px-6 py-2 rounded-xl shadow-lg border-0"
              >
                Apply Now
              </Button>
            </motion.div>
          </div>

          {/* Mobile Menu Button */}
          <motion.button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden relative p-2"
            whileTap={{ scale: 0.95 }}
          >
            <div className="w-8 h-8 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-lg flex items-center justify-center">
              {isMenuOpen ? (
                <X className="w-5 h-5 text-white" />
              ) : (
                <Menu className="w-5 h-5 text-white" />
              )}
            </div>
          </motion.button>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="md:hidden bg-oven-black/95 backdrop-blur-xl border-t border-sauce-red/20"
          >
            <div className="px-4 py-6 space-y-4">
              {navItems.map((item, index) => (
                <motion.a
                  key={item.name}
                  href={item.href}
                  className="block text-accent-white font-semibold py-3 px-4 rounded-xl hover:bg-gradient-to-r hover:from-sauce-red/10 hover:to-cheese-gold/10 transition-all duration-300"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </motion.a>
              ))}
              
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
                className="pt-2"
              >
                <Button 
                  onClick={() => {
                    window.location.href = '/apply#application-form';
                    setIsMenuOpen(false);
                  }}
                  className="w-full bg-gradient-to-r from-sauce-red to-sauce-red/90 hover:from-sauce-red/90 hover:to-sauce-red text-accent-white font-bold py-3 rounded-xl shadow-lg border-0"
                >
                  Apply Now
                </Button>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.nav>
  );
};

export default Navbar;
