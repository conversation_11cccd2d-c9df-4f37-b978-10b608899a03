import { supabase } from '@/integrations/supabase/client';
import devJourneyService from './devJourneyService';

export interface MilestoneProgress {
  stage_id: number;
  milestone_id: string;
  completed: boolean;
  submission_content?: string;
  completion_date?: string;
  is_approved: boolean;
}

export interface StageAnalysis {
  stage_id: number;
  stage_title: string;
  completion_percentage: number;
  completed_milestones: string[];
  missing_milestones: string[];
  required_milestones_completed: number;
  total_required_milestones: number;
  is_stage_complete: boolean;
  readiness_score: number; // 0-100, how ready they are for this stage
}

export interface AdaptiveJourneyState {
  current_effective_stage: number;
  confidence_score: number;
  reasoning: string;
  stage_analyses: StageAnalysis[];
  completed_stages: number[];
  next_recommended_actions: string[];
  gaps_to_address: string[];
  cross_stage_insights: string[];
}

class AdaptiveJourneyService {
  
  /**
   * Analyze user's true position across all stages based on milestone completion
   */
  async analyzeUserJourney(userId: string): Promise<AdaptiveJourneyState> {
    console.log('🔍 AdaptiveJourneyService: Starting analysis for user:', userId);
    try {
      // Get all milestone submissions for the user
      console.log('📊 Fetching milestone submissions...');
      const { data: milestones, error } = await supabase
        .from('milestone_submissions')
        .select('*')
        .eq('user_id', userId)
        .order('submission_date', { ascending: false });

      if (error) {
        console.error('❌ Error fetching milestones:', error);
        throw error;
      }

      console.log(`📈 Found ${milestones?.length || 0} milestone submissions`);
      if (milestones && milestones.length > 0) {
        console.log('🎯 Milestone breakdown:');
        milestones.forEach(m => {
          console.log(`  - Stage ${m.stage_id}: ${m.milestone_id} (${m.is_approved ? 'approved' : 'pending'})`);
        });
      }

      // Analyze each stage
      console.log('🔬 Analyzing all stages...');
      const stageAnalyses = await this.analyzeAllStages(userId, milestones || []);
      console.log('📊 Stage analyses completed:', stageAnalyses.length, 'stages analyzed');

      // Determine effective current stage
      console.log('🎯 Calculating effective current stage...');
      const currentEffectiveStage = this.calculateEffectiveStage(stageAnalyses);
      console.log('✅ Effective stage determined:', currentEffectiveStage);

      // Generate insights and recommendations
      console.log('💡 Generating insights and recommendations...');
      const insights = this.generateCrossStageInsights(stageAnalyses);
      const recommendations = this.generateRecommendations(stageAnalyses, currentEffectiveStage.stage_id);
      const gaps = this.identifyGaps(stageAnalyses, currentEffectiveStage.stage_id);
      console.log('📋 Generated:', insights.length, 'insights,', recommendations.length, 'recommendations,', gaps.length, 'gaps');

      return {
        current_effective_stage: currentEffectiveStage.stage_id,
        confidence_score: currentEffectiveStage.confidence,
        reasoning: currentEffectiveStage.reasoning,
        stage_analyses: stageAnalyses,
        completed_stages: stageAnalyses.filter(s => s.is_stage_complete).map(s => s.stage_id),
        next_recommended_actions: recommendations,
        gaps_to_address: gaps,
        cross_stage_insights: insights
      };
    } catch (error) {
      console.error('Error analyzing adaptive journey:', error);
      return this.getDefaultJourneyState();
    }
  }

  /**
   * Analyze completion status for all stages
   */
  private async analyzeAllStages(userId: string, milestones: any[]): Promise<StageAnalysis[]> {
    console.log('🔬 analyzeAllStages: Starting stage analysis');
    const stages = devJourneyService.getAllStages();
    console.log(`📊 Found ${stages.length} stages to analyze:`, stages.map(s => `Stage ${s.id}: ${s.title}`));
    const analyses: StageAnalysis[] = [];

    for (const stage of stages) {
      const stageMilestones = milestones.filter(m => m.stage_id === stage.id);
      const stageDefinition = devJourneyService.getStage(stage.id);
      
      if (!stageDefinition) continue;

      // Get stage milestone definitions (this would need to be added to devJourneyService)
      const expectedMilestones = this.getExpectedMilestonesForStage(stage.id);
      
      const completedMilestones = stageMilestones
        .filter(m => m.is_approved)
        .map(m => m.milestone_id);
      
      const missingMilestones = expectedMilestones
        .filter(m => !completedMilestones.includes(m.id))
        .map(m => m.id);

      const requiredMilestones = expectedMilestones.filter(m => m.required);
      const completedRequired = requiredMilestones.filter(m => 
        completedMilestones.includes(m.id)
      ).length;

      const completionPercentage = requiredMilestones.length > 0 
        ? Math.round((completedRequired / requiredMilestones.length) * 100)
        : 0;

      const isStageComplete = completedRequired >= requiredMilestones.length && 
        completionPercentage >= 80; // 80% threshold for completion

      // Calculate readiness score based on prerequisites and current progress
      const readinessScore = this.calculateReadinessScore(
        stage.id, 
        completedMilestones, 
        analyses
      );

      analyses.push({
        stage_id: stage.id,
        stage_title: stage.title,
        completion_percentage: completionPercentage,
        completed_milestones: completedMilestones,
        missing_milestones: missingMilestones,
        required_milestones_completed: completedRequired,
        total_required_milestones: requiredMilestones.length,
        is_stage_complete: isStageComplete,
        readiness_score: readinessScore
      });
    }

    return analyses;
  }

  /**
   * Calculate the user's effective current stage based on their progress pattern
   */
  private calculateEffectiveStage(analyses: StageAnalysis[]): { 
    stage_id: number; 
    confidence: number; 
    reasoning: string; 
  } {
    // Find the highest stage with meaningful progress (>50% completion)
    const meaningfulProgress = analyses.filter(a => a.completion_percentage >= 50);
    
    if (meaningfulProgress.length === 0) {
      return {
        stage_id: 0,
        confidence: 60,
        reasoning: "Starting journey - focus on Stage 0 fundamentals"
      };
    }

    // Sort by stage_id descending to find highest meaningful progress
    meaningfulProgress.sort((a, b) => b.stage_id - a.stage_id);
    const highestMeaningfulStage = meaningfulProgress[0];

    // Check if they have completed previous stages
    const hasGapsInEarlierStages = analyses
      .filter(a => a.stage_id < highestMeaningfulStage.stage_id)
      .some(a => a.completion_percentage < 80);

    let effectiveStage = highestMeaningfulStage.stage_id;
    let confidence = highestMeaningfulStage.completion_percentage;
    let reasoning = `Advanced to Stage ${effectiveStage} based on ${highestMeaningfulStage.completion_percentage}% completion`;

    // If there are gaps in earlier stages, recommend focusing on filling those gaps
    if (hasGapsInEarlierStages) {
      const earliestIncompleteStage = analyses
        .filter(a => a.stage_id < highestMeaningfulStage.stage_id && a.completion_percentage < 80)
        .sort((a, b) => a.stage_id - b.stage_id)[0];
      
      effectiveStage = earliestIncompleteStage.stage_id;
      confidence = Math.max(60, earliestIncompleteStage.completion_percentage);
      reasoning = `Focus on Stage ${effectiveStage} to fill gaps before advancing further`;
    }

    return { stage_id: effectiveStage, confidence, reasoning };
  }

  /**
   * Calculate readiness score for a stage based on prerequisites and progress
   */
  private calculateReadinessScore(
    stageId: number, 
    completedMilestones: string[], 
    previousAnalyses: StageAnalysis[]
  ): number {
    let score = 0;

    // Base score from current stage completion
    const expectedMilestones = this.getExpectedMilestonesForStage(stageId);
    const completionRatio = completedMilestones.length / Math.max(expectedMilestones.length, 1);
    score += completionRatio * 60; // Up to 60 points for current stage

    // Bonus for prerequisite stages
    if (stageId > 0) {
      const prerequisiteStages = previousAnalyses.filter(a => a.stage_id < stageId);
      const prerequisiteCompletion = prerequisiteStages.reduce((sum, stage) => 
        sum + stage.completion_percentage, 0) / Math.max(prerequisiteStages.length, 1);
      score += (prerequisiteCompletion / 100) * 40; // Up to 40 points for prerequisites
    } else {
      score += 40; // Stage 0 has no prerequisites
    }

    return Math.min(100, Math.round(score));
  }

  /**
   * Generate cross-stage insights about the user's journey pattern
   */
  private generateCrossStageInsights(analyses: StageAnalysis[]): string[] {
    const insights: string[] = [];

    // Check for skipped stages
    const completedStages = analyses.filter(a => a.is_stage_complete).map(a => a.stage_id);
    const hasSkippedStages = completedStages.some((stage, index) => 
      index > 0 && stage > completedStages[index - 1] + 1
    );

    if (hasSkippedStages) {
      insights.push("You've made progress in advanced stages - consider strengthening fundamentals in earlier stages");
    }

    // Check for broad progress
    const stagesWithProgress = analyses.filter(a => a.completion_percentage > 0).length;
    if (stagesWithProgress >= 3) {
      insights.push("Great breadth of exploration across multiple stages - you're building a comprehensive foundation");
    }

    // Check for deep focus
    const deepFocusStages = analyses.filter(a => a.completion_percentage >= 80).length;
    if (deepFocusStages >= 2) {
      insights.push("Strong depth in multiple areas - you're building expertise across the journey");
    }

    return insights;
  }

  /**
   * Generate personalized recommendations based on journey analysis
   */
  private generateRecommendations(analyses: StageAnalysis[], currentStage: number): string[] {
    const recommendations: string[] = [];
    const currentAnalysis = analyses.find(a => a.stage_id === currentStage);

    if (currentAnalysis) {
      // Recommendations for current stage
      if (currentAnalysis.missing_milestones.length > 0) {
        recommendations.push(`Complete ${currentAnalysis.missing_milestones.slice(0, 2).join(' and ')} in Stage ${currentStage}`);
      }

      // Check for gaps in earlier stages
      const earlierStages = analyses.filter(a => a.stage_id < currentStage && !a.is_stage_complete);
      if (earlierStages.length > 0) {
        const earliestGap = earlierStages[0];
        recommendations.push(`Strengthen foundation by completing ${earliestGap.missing_milestones[0]} in Stage ${earliestGap.stage_id}`);
      }

      // Look ahead to next stage if current is nearly complete
      if (currentAnalysis.completion_percentage >= 70) {
        const nextStage = analyses.find(a => a.stage_id === currentStage + 1);
        if (nextStage && nextStage.readiness_score >= 60) {
          recommendations.push(`Prepare for Stage ${nextStage.stage_id} by exploring ${nextStage.missing_milestones[0]}`);
        }
      }
    }

    return recommendations.slice(0, 3); // Limit to top 3 recommendations
  }

  /**
   * Identify critical gaps that need attention
   */
  private identifyGaps(analyses: StageAnalysis[], currentStage: number): string[] {
    const gaps: string[] = [];

    // Find stages with low completion that should be higher
    analyses.forEach(analysis => {
      if (analysis.stage_id <= currentStage && analysis.completion_percentage < 50) {
        gaps.push(`Stage ${analysis.stage_id}: Missing core milestones (${analysis.completion_percentage}% complete)`);
      }
    });

    return gaps;
  }

  /**
   * Get expected milestones for a stage from database or fallback to known milestones
   */
  private getExpectedMilestonesForStage(stageId: number): { id: string; required: boolean }[] {
    // Known milestone patterns based on actual data
    const knownMilestones: { [key: number]: { id: string; required: boolean }[] } = {
      0: [
        { id: 'problem_identified', required: true },
        { id: 'idea_validation', required: true },
        { id: 'target_audience', required: false },
        { id: 'market_research', required: false }
      ],
      1: [
        { id: 'concept_clarity', required: true },
        { id: 'milestone_planning', required: true },
        { id: 'team_building', required: false },
        { id: 'resource_planning', required: false }
      ],
      2: [
        { id: 'mvp_designed', required: true },
        { id: 'technical_challenges', required: true },
        { id: 'user_testing', required: false },
        { id: 'prototype_validation', required: false }
      ],
      3: [
        { id: 'product_launch', required: true },
        { id: 'user_feedback', required: true },
        { id: 'market_validation', required: false }
      ],
      4: [
        { id: 'scaling_strategy', required: true },
        { id: 'growth_metrics', required: true },
        { id: 'team_expansion', required: false }
      ]
    };

    return knownMilestones[stageId] || [];
  }

  /**
   * Default journey state for error cases
   */
  private getDefaultJourneyState(): AdaptiveJourneyState {
    return {
      current_effective_stage: 0,
      confidence_score: 50,
      reasoning: "Starting your journey",
      stage_analyses: [],
      completed_stages: [],
      next_recommended_actions: ["Complete your profile", "Define your problem"],
      gaps_to_address: [],
      cross_stage_insights: []
    };
  }

  /**
   * Store the adaptive journey analysis in the database for consistency
   */
  async storeJourneyAnalysis(userId: string, analysis: AdaptiveJourneyState): Promise<void> {
    try {
      // Check if user has recent stage data (within last hour) to avoid duplicates
      const { data: recentStage } = await supabase
        .from('user_dev_journey_stages')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString()) // Last hour
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      // Only insert if no recent stage data or if stage has changed
      if (!recentStage || recentStage.stage_id !== analysis.current_effective_stage) {
        const { error } = await supabase
          .from('user_dev_journey_stages')
          .insert({
            user_id: userId,
            stage_id: analysis.current_effective_stage,
            confidence_score: analysis.confidence_score,
            reasoning: analysis.reasoning,
            next_actions: analysis.next_recommended_actions,
            applicable_frameworks: [],
            stage_entered_at: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (error) throw error;
        console.log('✅ Adaptive journey analysis stored successfully');
        console.log(`📊 User ${userId} positioned at Stage ${analysis.current_effective_stage} (${analysis.confidence_score}% confidence)`);
      } else {
        console.log('⏭️ Skipping duplicate stage entry - recent analysis exists');
      }
    } catch (error) {
      console.error('Error storing journey analysis:', error);
    }
  }
}

export default new AdaptiveJourneyService();
