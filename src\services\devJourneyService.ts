// Dev Journey Service for managing stages, content chunks, and contextual guidance
export interface DevJourneyStage {
  id: number;
  name: string;
  title: string;
  description: string;
  characteristics: string[];
  supportNeeded: string[];
  frameworks?: Record<string, string>;
  cac_focus?: string;
  ai_impact?: string;
}

export interface DevJourneyChunk {
  stage_id: number;
  chunk_type: 'overview' | 'frameworks' | 'cac_lens' | 'ai_impact' | 'support';
  content: string;
  keywords: string[];
}

export interface UserStageAnalysis {
  current_stage: number;
  confidence_score: number;
  reasoning: string;
  next_actions: string[];
  applicable_frameworks: string[];
}

class DevJourneyService {
  // Define the 6 stages of the Dev Journey
  private stages: DevJourneyStage[] = [
    {
      id: 0,
      name: 'spark',
      title: 'Stage 0: Spark',
      description: 'Initial idea formation and problem exploration',
      characteristics: [
        'Vision is forming but remains unfocused',
        'Actively exploring what problem to solve',
        'May have several competing ideas',
        'No formal name, plan, or MVP yet'
      ],
      supportNeeded: [
        'Challenge the idea without judgement',
        'Idea validation from potential users',
        'Naming assistance and brand identity',
        'Problem definition frameworks',
        'Storytelling guidance to articulate vision'
      ],
      frameworks: {
        'Problem Validation': 'What\'s the pain? Who feels it? What are they using now? What\'s missing?',
        'Jobs To Be Done (JTBD)': 'Focus on what people are trying to do in their life. When I [situation], I want to [motivation], so I can [outcome].',
        'CAC Strategic Lens': 'How will potential customers discover your product? Will your solution require high-touch sales? Does the value justify acquisition costs?'
      },
      cac_focus: 'Consider theoretical CAC scenarios and acquisition channels early to shape product decisions',
      ai_impact: 'AI enables guided brainstorming, pattern recognition, quick visual mockups, test scenario creation, and research assistance to accelerate idea exploration'
    },
    {
      id: 1,
      name: 'formation',
      title: 'Stage 1: Formation',
      description: 'Team assembly and concept clarification',
      characteristics: [
        'Clear vision of problem and target users',
        'Team assembling or actively looking for collaborators',
        'Visual concepts like sketches or wireframes exist',
        'Well-defined product concept with capable team'
      ],
      supportNeeded: [
        'Milestone Planning',
        'Feedback Loops',
        'Team Building',
        'Technical Architecture',
        'Early Advisors',
        'AI Integration strategy'
      ],
      cac_focus: 'Begin mapping potential customer acquisition channels and estimate preliminary CAC ranges based on industry benchmarks',
      ai_impact: 'AI compresses weeks of competitive research into hours, creates design mockups from descriptions, and helps test concepts with users'
    },
    {
      id: 2,
      name: 'first_build',
      title: 'Stage 2: First Build',
      description: 'Prototype development and core feature implementation',
      characteristics: [
        'Actively developing initial version of product',
        'Building core functionality',
        'Making critical technical decisions',
        'Focus on working prototype that validates key hypotheses'
      ],
      supportNeeded: [
        'Bias toward building over planning',
        'Target-user proof and feedback',
        'Tech stack guidance',
        'First collaborator',
        'Advisor for course corrections',
        'Lightweight AI implementation'
      ],
      cac_focus: 'Build analytics and tracking capabilities, design small acquisition experiments, create preliminary CAC models',
      ai_impact: 'AI dramatically accelerates development through code generation, UI creation, and testing assistance while maintaining focus on core value'
    },
    {
      id: 3,
      name: 'ship',
      title: 'Stage 3: Ship',
      description: 'Product launch and initial user feedback',
      characteristics: [
        'Product is live and accessible to users',
        'Real people engaging with product',
        'Active feedback collection and analysis',
        'Rapid iteration based on user input'
      ],
      supportNeeded: [
        'Launch Support',
        'Marketing Guidance',
        'Bug Triage systems',
        'User Feedback Systems',
        'Crisis Management',
        'Acquisition Tracking'
      ],
      cac_focus: 'CAC becomes measurable for the first time. Track early metrics, prioritize learning over optimization, establish baseline benchmarks',
      ai_impact: 'AI speeds up feedback analysis, problem-solving, and personalized user experiences while maintaining human strategic oversight'
    },
    {
      id: 4,
      name: 'traction',
      title: 'Stage 4: Traction',
      description: 'User growth and engagement optimization',
      characteristics: [
        'Steady stream of new users',
        'Measurable patterns of repeat usage',
        'Early signs of product-market fit',
        'Active growth experimentation'
      ],
      supportNeeded: [
        'Growth Expertise',
        'Metrics Framework',
        'User Research',
        'Mentorship from successful entrepreneurs',
        'Revenue Modeling',
        'CAC Analysis Tools'
      ],
      cac_focus: 'CAC becomes a primary metric guiding resource allocation. Focus on reducing acquisition costs while maintaining quality growth',
      ai_impact: 'AI analyzes user behavior at scale, automates growth experiments, enables individual-level personalization, and optimizes CAC in real-time'
    },
    {
      id: 5,
      name: 'real_company',
      title: 'Stage 5: Real Company',
      description: 'Business model implementation and scaling',
      characteristics: [
        'Loyal user base with consistent growth',
        'Clear roadmap and sustainable business model',
        'Interest from investors',
        'Transition from experimentation to optimization'
      ],
      supportNeeded: [
        'Legal Structure',
        'Investor Relations',
        'Vision Planning',
        'Operational Systems'
      ],
      cac_focus: 'CAC:LTV ratio becomes fundamental business health indicator. Balance optimization with brand building investments',
      ai_impact: 'AI enables smart learning systems, intelligent process automation, and AI-generated data advantages for competitive edge'
    }
  ];

  // Create content chunks for each stage
  private chunks: DevJourneyChunk[] = [
    // Stage 0: Spark
    {
      stage_id: 0,
      chunk_type: 'overview',
      content: 'The Spark stage is where every great product begins—with the glimmer of an idea. You\'re exploring possibilities rather than certainties. Your vision is forming but remains fuzzy. You might have several competing ideas or variations. The problem you want to solve is still being defined. This exploratory phase is crucial for setting a strong foundation.',
      keywords: ['idea', 'exploration', 'vision', 'problem', 'opportunity', 'research', 'validation']
    },
    {
      stage_id: 0,
      chunk_type: 'frameworks',
      content: 'Problem Validation Framework: (1) Problem - What\'s the pain? (2) Affected People - Who feels it? (3) Existing Solutions - What are they using now? (4) Gap - What\'s missing? Jobs To Be Done (JTBD): Focus on what people are trying to do. Ask: "When I [situation], I want to [motivation], so I can [outcome]." This shifts thinking from features to customer outcomes.',
      keywords: ['problem validation', 'JTBD', 'customer outcomes', 'pain points', 'market research']
    },
    {
      stage_id: 0,
      chunk_type: 'cac_lens',
      content: 'CAC Strategic Lens for Spark: Consider acquisition channels, sales complexity, customer value alignment, market education needs, and network effects. Asking these questions early helps avoid ideas that might be technically feasible but commercially challenging.',
      keywords: ['CAC', 'acquisition channels', 'sales complexity', 'customer value', 'market education']
    },
    
    // Stage 1: Formation  
    {
      stage_id: 1,
      chunk_type: 'overview',
      content: 'Formation represents a critical transition from abstract ideas to concrete plans. You\'ve emerged with clear vision of the problem and users you want to serve. This clarity brings focus and enables assembling resources needed to transform vision into reality. You\'re moving from "what if" to "how we\'ll do it."',
      keywords: ['team assembly', 'concept clarification', 'planning', 'vision', 'focus']
    },
    {
      stage_id: 1,
      chunk_type: 'cac_lens',
      content: 'CAC considerations should focus on theoretical modeling. Begin mapping potential customer acquisition channels and estimate preliminary CAC ranges based on industry benchmarks. Create hypotheses about which channels might be most cost-effective for your specific user segments.',
      keywords: ['CAC modeling', 'acquisition channels', 'industry benchmarks', 'user segments']
    },

    // Stage 2: First Build
    {
      stage_id: 2,
      chunk_type: 'overview',
      content: 'First Build marks transition from planning to action. You\'re actively developing the initial version of your product. Abstract ideas transform into tangible software that can be tested and refined. Focus on building the minimum solution that proves your concept works.',
      keywords: ['prototype', 'development', 'MVP', 'building', 'core functionality']
    },
    {
      stage_id: 2,
      chunk_type: 'frameworks',
      content: 'Key Activities: (1) Build the core loop - ship simplest version (2) Wire it together - auth, DB, routing, AI endpoints (3) Fake the hard parts - use placeholders (4) Don\'t over-architect - this isn\'t your final system (5) If AI is core, make sure it belongs and serves a purpose users care about.',
      keywords: ['core loop', 'MVP', 'prototyping', 'AI integration', 'validation']
    },
    {
      stage_id: 2,
      chunk_type: 'cac_lens',
      content: 'During First Build, focus on laying foundation for efficient future acquisition. Build analytics and tracking capabilities, design small acquisition experiments, create preliminary CAC models, and target lowest-CAC customer segments for initial testing.',
      keywords: ['analytics', 'tracking', 'acquisition experiments', 'early adopters']
    },

    // Stage 3: Ship
    {
      stage_id: 3,
      chunk_type: 'overview',
      content: 'Ship represents a pivotal moment—your creation is now in hands of real users. You\'ve crossed the threshold from building in isolation to engaging with actual market. This stage brings vulnerability and tremendous opportunity as you collect authentic feedback and usage data.',
      keywords: ['launch', 'users', 'feedback', 'market engagement', 'iteration']
    },
    {
      stage_id: 3,
      chunk_type: 'cac_lens',
      content: 'CAC becomes measurable for the first time with real users. Initial CAC metrics are often higher than sustainable levels. Track these metrics carefully but prioritize learning over optimization. Focus on understanding which user segments convert at what cost.',
      keywords: ['CAC measurement', 'user segments', 'conversion', 'learning', 'baseline metrics']
    },

    // Stage 4: Traction
    {
      stage_id: 4,
      chunk_type: 'overview',
      content: 'Traction marks transition from having a live product to actively building momentum. With a stable product delivering value, focus shifts to expanding user base, deepening engagement, and exploring revenue potential. Data becomes your most valuable asset.',
      keywords: ['growth', 'momentum', 'engagement', 'revenue', 'data-driven']
    },
    {
      stage_id: 4,
      chunk_type: 'cac_lens',
      content: 'CAC becomes a primary metric guiding resource allocation. Focus on systematic testing of growth hypotheses, optimizing user experience, and experimenting with different acquisition channels while maintaining quality growth.',
      keywords: ['growth optimization', 'resource allocation', 'channel testing', 'quality growth']
    },

    // Stage 5: Real Company
    {
      stage_id: 5,
      chunk_type: 'overview',
      content: 'Real Company represents culmination of your journey—transitioning from experimental project to structured business with sustainable momentum. Having proven ability to attract and retain users, you\'re now formalizing operations and building foundation for long-term growth.',
      keywords: ['scaling', 'business model', 'operations', 'sustainable growth', 'structure']
    },
    {
      stage_id: 5,
      chunk_type: 'cac_lens',
      content: 'CAC:LTV ratio becomes critical KPI for proving business model viability. Monitor this ratio (aim for 1:3 or better), segment CAC analysis by channel, implement payback period tracking, and balance optimization with brand building investments.',
      keywords: ['CAC:LTV ratio', 'business model', 'channel segmentation', 'brand building']
    }
  ];

  // Map onboarding tracks to dev journey stages
  private trackToStageMapping = {
    'newbie': [0, 1], // Spark to Formation
    'builder': [2, 3], // First Build to Ship  
    'scaler': [4, 5]   // Traction to Real Company
  };

  // Get stage information
  getStage(stageId: number): DevJourneyStage | null {
    return this.stages.find(stage => stage.id === stageId) || null;
  }

  // Get all stages
  getAllStages(): DevJourneyStage[] {
    return this.stages;
  }

  // Get chunks for a specific stage
  getStageChunks(stageId: number, chunkTypes?: string[]): DevJourneyChunk[] {
    let chunks = this.chunks.filter(chunk => chunk.stage_id === stageId);
    
    if (chunkTypes) {
      chunks = chunks.filter(chunk => chunkTypes.includes(chunk.chunk_type));
    }
    
    return chunks;
  }

  // Get contextual content for AI based on user's situation
  getContextualContent(stageId: number, userSituation: {
    track?: string;
    problemDescription?: string;
    primaryGoal?: string;
    biggestChallenge?: string;
  }): string {
    const stage = this.getStage(stageId);
    const chunks = this.getStageChunks(stageId);
    
    if (!stage) return '';

    let context = `# ${stage.title}\n\n${stage.description}\n\n`;
    
    // Add relevant chunks based on user situation
    if (userSituation.biggestChallenge?.toLowerCase().includes('market') || 
        userSituation.primaryGoal === 'find_users') {
      const cacChunk = chunks.find(c => c.chunk_type === 'cac_lens');
      if (cacChunk) {
        context += `## Customer Acquisition Focus\n${cacChunk.content}\n\n`;
      }
    }

    if (userSituation.primaryGoal === 'learn' || 
        userSituation.biggestChallenge?.toLowerCase().includes('how')) {
      const frameworksChunk = chunks.find(c => c.chunk_type === 'frameworks');
      if (frameworksChunk) {
        context += `## Frameworks & Tools\n${frameworksChunk.content}\n\n`;
      }
    }

    // Add stage-specific support
    context += `## Support You Need\n${stage.supportNeeded.join('\n- ')}\n\n`;
    
    return context;
  }

  // Analyze user's current stage based on their profile and activities
  analyzeUserStage(userProfile: {
    track?: string;
    problemDescription?: string;
    solutionApproach?: string;
    primaryGoal?: string;
    technicalBackground?: string;
    recentUpdates?: string[];
    currentProjectStage?: string;
  }): UserStageAnalysis {
    let stageScores: { [key: number]: number } = {};
    
    // Initialize all stages with base score
    this.stages.forEach(stage => {
      stageScores[stage.id] = 0;
    });

    // Track-based scoring
    if (userProfile.track) {
      const likelyStages = this.trackToStageMapping[userProfile.track] || [0];
      likelyStages.forEach(stageId => {
        stageScores[stageId] += 30;
      });
    }

    // Goal-based scoring
    if (userProfile.primaryGoal) {
      switch (userProfile.primaryGoal) {
        case 'learn':
          stageScores[0] += 25; stageScores[1] += 15;
          break;
        case 'build_mvp':
          stageScores[1] += 20; stageScores[2] += 25;
          break;
        case 'find_users':
          stageScores[2] += 15; stageScores[3] += 25;
          break;
        case 'scale':
          stageScores[4] += 25; stageScores[5] += 20;
          break;
      }
    }

    // Content analysis scoring
    if (userProfile.problemDescription && userProfile.solutionApproach) {
      if (userProfile.problemDescription.length > 100 && userProfile.solutionApproach.length > 100) {
        stageScores[1] += 15; // Good Formation characteristics
      }
    }

    // Project stage scoring
    if (userProfile.currentProjectStage) {
      switch (userProfile.currentProjectStage) {
        case 'ideation':
          stageScores[0] += 20; stageScores[1] += 10;
          break;
        case 'building':
          stageScores[2] += 25;
          break;
        case 'testing':
          stageScores[3] += 25;
          break;
        case 'scaling':
          stageScores[4] += 20; stageScores[5] += 15;
          break;
      }
    }

    // Recent updates analysis
    if (userProfile.recentUpdates && userProfile.recentUpdates.length > 0) {
      const recentContent = userProfile.recentUpdates.join(' ').toLowerCase();
      
      if (recentContent.includes('shipped') || recentContent.includes('launched')) {
        stageScores[3] += 20;
      }
      if (recentContent.includes('users') || recentContent.includes('customers')) {
        stageScores[3] += 15; stageScores[4] += 10;
      }
      if (recentContent.includes('revenue') || recentContent.includes('paying')) {
        stageScores[4] += 20; stageScores[5] += 15;
      }
      if (recentContent.includes('prototype') || recentContent.includes('mvp')) {
        stageScores[2] += 20;
      }
    }

    // Find the highest scoring stage
    const currentStage = Object.entries(stageScores).reduce((a, b) => 
      stageScores[parseInt(a[0])] > stageScores[parseInt(b[0])] ? a : b
    );

    const stageId = parseInt(currentStage[0]);
    const confidence = Math.min(stageScores[stageId] / 100, 1);
    const stage = this.getStage(stageId)!;

    return {
      current_stage: stageId,
      confidence_score: Math.round(confidence * 100),
      reasoning: this.generateStageReasoning(stageId, userProfile),
      next_actions: stage.supportNeeded.slice(0, 3),
      applicable_frameworks: Object.keys(stage.frameworks || {})
    };
  }

  private generateStageReasoning(stageId: number, userProfile: any): string {
    const stage = this.getStage(stageId)!;
    const reasons = [];

    if (userProfile.track) {
      reasons.push(`You selected the ${userProfile.track} track`);
    }
    
    if (userProfile.primaryGoal) {
      reasons.push(`Your primary goal is to ${userProfile.primaryGoal.replace('_', ' ')}`);
    }

    if (userProfile.currentProjectStage) {
      reasons.push(`Your project is in ${userProfile.currentProjectStage} stage`);
    }

    return `Based on ${reasons.join(', ')}, you appear to be in the ${stage.title} stage. ${stage.description}`;
  }

  // Get relevant frameworks for current situation
  getRelevantFrameworks(stageId: number, situation: string): { [key: string]: string } {
    const stage = this.getStage(stageId);
    if (!stage?.frameworks) return {};

    const situationLower = situation.toLowerCase();
    const relevantFrameworks: { [key: string]: string } = {};

    // Context-aware framework selection
    if (situationLower.includes('problem') || situationLower.includes('idea') || situationLower.includes('validate')) {
      if (stage.frameworks['Problem Validation']) {
        relevantFrameworks['Problem Validation'] = stage.frameworks['Problem Validation'];
      }
      if (stage.frameworks['Jobs To Be Done (JTBD)']) {
        relevantFrameworks['Jobs To Be Done (JTBD)'] = stage.frameworks['Jobs To Be Done (JTBD)'];
      }
    }

    if (situationLower.includes('customer') || situationLower.includes('user') || situationLower.includes('market')) {
      if (stage.frameworks['CAC Strategic Lens']) {
        relevantFrameworks['CAC Strategic Lens'] = stage.frameworks['CAC Strategic Lens'];
      }
    }

    // If no specific match, return all frameworks for the stage
    if (Object.keys(relevantFrameworks).length === 0) {
      return stage.frameworks;
    }

    return relevantFrameworks;
  }
}

export const devJourneyService = new DevJourneyService();
export default devJourneyService; 