import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  Target, 
  Users, 
  DollarSign, 
  Calendar,
  CheckCircle,
  Brain,
  Award,
  Rocket,
  BarChart3,
  Lightbulb,
  Shield,
  Clock,
  ArrowRight,
  Star,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  FileText,
  Activity,
  TrendingDown,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';
import MarkdownText from './MarkdownText';
import { supabase } from '@/integrations/supabase/client';
import devJourneyService from '@/services/devJourneyService';
import stageProgressService from '@/services/stageProgressService';
import userMemoryService from '@/services/userMemoryService';
import geminiService from '@/services/geminiService';
import aiAnalysisCacheService from '@/services/aiAnalysisCache';
import dashboardCacheService from '@/services/dashboardCacheService';

interface ComprehensiveInvestorDashboardProps {
  userId: string;
  currentStage: number;
  stageConfidence: number;
  onStageUpdate?: () => void;
}

interface ComprehensiveData {
  executiveSummary: {
    projectName: string;
    problemStatement: string;
    solutionApproach: string;
    targetAudience: string;
    uniqueValueProposition: string;
    marketOpportunity: string;
    technicalFeasibility: string;
  };
  progressMetrics: {
    overallCompletion: number;
    currentStage: number;
    stagesCompleted: number;
    totalMilestones: number;
    completedMilestones: number;
    aiConfidenceAverage: number;
    projectViabilityScore: number;
    developmentVelocity: number;
    consistencyScore: number;
  };
  milestoneAchievements: Array<{
    id: string;
    title: string;
    stage: number;
    completedDate: string;
    aiConfidence: number;
    keyDeliverables: string[];
    validationEvidence: string;
    submissionContent: string;
    aiInsights: any;
  }>;
  stageBreakdown: Array<{
    stage: number;
    name: string;
    description: string;
    completionPercentage: number;
    milestones: Array<{
      id: string;
      title: string;
      completed: boolean;
      aiConfidence?: number;
      completedDate?: string;
    }>;
    isActive: boolean;
    isCompleted: boolean;
  }>;
  marketValidation: {
    userFeedback: string[];
    validationEvidence: string[];
    marketTraction: string;
    competitiveAdvantage: string;
  };
  technicalProgress: {
    architectureDecisions: string[];
    technicalMilestones: string[];
    codeQuality: string;
    scalabilityPlan: string;
  };
  fundingNeeds: {
    currentStage: string;
    nextMilestone: string;
    supportNeeded: string[];
    projectedTimeline: string;
    investmentOpportunity: string;
    riskAssessment: string[];
  };
  teamAndExecution: {
    founderBackground: string;
    executionCapability: string;
    learningVelocity: string;
    adaptability: string;
  };
  aiInsights?: {
    riskAssessment: {
      executionRisk: {
        level: 'Low' | 'Medium' | 'High';
        reasoning: string;
        confidence: number;
      };
      marketRisk: {
        level: 'Low' | 'Medium' | 'High';
        reasoning: string;
        confidence: number;
      };
      technicalRisk: {
        level: 'Low' | 'Medium' | 'High';
        reasoning: string;
        confidence: number;
      };
    };
    growthPotential: {
      marketPenetration: string;
      scalingCapability: string;
      trajectoryAnalysis: string;
      overallPrediction: string;
    };
    investmentReadiness: {
      score: number;
      reasoning: string;
      keyStrengths: string[];
      areasForImprovement: string[];
    };
    generatedAt: string;
    confidence: number;
  };
  aiProfessionalSections?: {
    executiveSummary?: {
      problemStatement: string;
      solutionApproach: string;
      targetAudience: string;
      uniqueValueProposition: string;
      marketOpportunity: string;
      technicalFeasibility: string;
    };
    marketValidation?: {
      validationSummary: string;
      userFeedbackSynthesis: string;
      marketTraction: string;
      competitiveAdvantage: string;
    };
    technicalProgress?: {
      architectureSummary: string;
      developmentProgress: string;
      codeQualityAssessment: string;
      scalabilityStrategy: string;
    };
    teamAndExecution?: {
      executionCapability: string;
      keyStrengths: string[];
      developmentVelocity: string;
      consistencyAndReliability: string;
    };
    fundingAndGrowth?: {
      fundingNeeds: string;
      growthStrategy: string;
      milestoneRoadmap: string;
      investmentOpportunity: string;
    };
    generatedAt?: string;
  };
}

const ComprehensiveInvestorDashboard: React.FC<ComprehensiveInvestorDashboardProps> = ({
  userId,
  currentStage,
  stageConfidence,
  onStageUpdate
}) => {
  const [dashboardData, setDashboardData] = useState<ComprehensiveData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['executive', 'metrics', 'ai-analysis']));
  const [refreshingInsights, setRefreshingInsights] = useState(false);

  useEffect(() => {
    // Clean up expired cache on mount
    dashboardCacheService.cleanupExpiredCache();
    loadComprehensiveData();
  }, [userId, currentStage]);

  const loadComprehensiveData = async () => {
    try {
      setIsLoading(true);

      // Get all data sources in parallel
      const [
        onboardingData,
        profileData,
        milestoneSubmissions,
        userContext,
        userMemories,
        allStageProgress
      ] = await Promise.all([
        supabase.from('onboarding_responses').select('*').eq('user_id', userId).order('created_at', { ascending: false }).limit(1),
        supabase.from('comprehensive_user_profiles').select('*').eq('user_id', userId).order('created_at', { ascending: false }).limit(1),
        supabase.from('milestone_submissions').select('*').eq('user_id', userId).order('created_at', { ascending: true }),
        userMemoryService.buildUserContext(userId),
        userMemoryService.getUserMemories(userId, 'all', 50),
        loadAllStageProgress()
      ]);

      // Create context for cache key generation
      const dataContext = {
        stage: currentStage,
        achievements: milestoneSubmissions.data?.length || 0,
        submissions: milestoneSubmissions.data?.length || 0,
        lastUpdate: milestoneSubmissions.data?.[0]?.created_at || new Date().toISOString()
      };

      // Try to get cached dashboard data first
      const cachedData = await dashboardCacheService.getCachedDashboardData(
        userId,
        currentStage,
        dataContext
      );

      if (cachedData) {
        console.log('📊 Using cached comprehensive dashboard data');
        setDashboardData(cachedData);
        return;
      }

      console.log('🔄 Generating fresh comprehensive dashboard data');

      // Process and structure the comprehensive data
      const processedData = await processComprehensiveData(
        onboardingData.data?.[0] || null,
        profileData.data?.[0] || null,
        milestoneSubmissions.data || [],
        userContext || {},
        userMemories || [],
        allStageProgress,
        currentStage,
        stageConfidence,
        userId
      );

      // Cache the processed data
      await dashboardCacheService.cacheDashboardData(
        userId,
        currentStage,
        processedData,
        dataContext,
        6 // Cache for 6 hours
      );

      setDashboardData(processedData);
    } catch (error) {
      console.error('Error loading comprehensive data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadAllStageProgress = async () => {
    const allStages = devJourneyService.getAllStages();
    const progressPromises = allStages.map(stage => 
      stageProgressService.getCurrentStageProgress(userId, stage.id)
    );
    return Promise.all(progressPromises);
  };

  const processComprehensiveData = async (
    onboarding: any,
    profile: any,
    submissions: any[],
    userContext: any,
    memories: any[],
    stageProgress: any[],
    stage: number,
    confidence: number,
    userId: string
  ): Promise<ComprehensiveData> => {
    // Extract project name intelligently
    const projectName = extractProjectName(onboarding, userContext, submissions) || 'Innovation Project';

    // Process milestone achievements with rich data
    const achievements = submissions
      .filter(s => s && (s.is_approved || s.completed || s.ai_feedback))
      .map(submission => {
        let aiConfidence = 75;
        let aiInsights = null;
        let keyDeliverables: string[] = [];
        
        try {
          if (submission.ai_insights) {
            if (typeof submission.ai_insights === 'string') {
              // Clean the string first (remove markdown if present)
              let cleanedInsights = submission.ai_insights.trim();
              if (cleanedInsights.startsWith('```json')) {
                cleanedInsights = cleanedInsights.replace(/^```json\s*/, '').replace(/\s*```$/, '');
              } else if (cleanedInsights.startsWith('```')) {
                cleanedInsights = cleanedInsights.replace(/^```\s*/, '').replace(/\s*```$/, '');
              }

              // Check if it looks like JSON before attempting to parse
              if (cleanedInsights.startsWith('{') || cleanedInsights.startsWith('[')) {
                try {
                  aiInsights = JSON.parse(cleanedInsights);
                } catch (parseError) {
                  console.warn('Error parsing AI insights JSON:', parseError, 'Raw data:', submission.ai_insights.substring(0, 100));
                  // If JSON parsing fails, treat as plain text and extract what we can
                  aiInsights = { confidence: 75, insights: submission.ai_insights };
                }
              } else {
                // For legacy data that doesn't look like JSON, create a basic insights object
                if (cleanedInsights.length > 10) { // Only if there's meaningful content
                  console.log('Converting legacy AI insights to structured format:', cleanedInsights.substring(0, 50) + '...');
                  aiInsights = {
                    confidence: 60,
                    insights: cleanedInsights,
                    legacy: true
                  };
                } else {
                  aiInsights = null;
                }
              }
            } else if (typeof submission.ai_insights === 'object') {
              // Already an object
              aiInsights = submission.ai_insights;
            }

            if (aiInsights) {
              aiConfidence = aiInsights.finalConfidence || aiInsights.confidence || 75;
              keyDeliverables = aiInsights.keyLearnings || aiInsights.nextSteps || aiInsights.recommendations || [];
            }
          }
        } catch (e) {
          console.warn('Error processing AI insights:', e);
          aiInsights = { confidence: 75, insights: 'Analysis available' };
        }

        return {
          id: submission.milestone_id,
          title: submission.metadata?.personalized_title || submission.milestone_id.replace(/_/g, ' '),
          stage: submission.stage_id,
          completedDate: submission.approval_date || submission.created_at,
          aiConfidence,
          keyDeliverables,
          validationEvidence: submission.submission_content?.substring(0, 300) + '...' || '',
          submissionContent: submission.submission_content || '',
          aiInsights
        };
      })
      .sort((a, b) => new Date(a.completedDate).getTime() - new Date(b.completedDate).getTime());

    // Calculate comprehensive metrics
    const totalStages = devJourneyService.getAllStages().length;
    const completedStages = stageProgress.filter(p => p && p.progress_percentage === 100).length;
    const overallCompletion = Math.round(((stage + 1) / totalStages) * 100);
    const avgConfidence = achievements.length > 0 
      ? Math.round(achievements.reduce((sum, a) => sum + a.aiConfidence, 0) / achievements.length)
      : confidence;

    // Calculate development velocity (milestones per week)
    const developmentVelocity = calculateDevelopmentVelocity(achievements);
    const consistencyScore = calculateConsistencyScore(submissions, memories);

    // Process stage breakdown with detailed progress
    const stageBreakdown = await processStageBreakdown(stageProgress, submissions, stage);

    // Extract market validation data
    const marketValidation = extractMarketValidation(onboarding, submissions, memories, profile);

    // Extract technical progress
    const technicalProgress = extractTechnicalProgress(submissions, memories, onboarding);

    // Generate funding needs and investment opportunity
    const fundingNeeds = generateFundingNeeds(onboarding, profile, achievements, stage, stageBreakdown);

    // Assess team and execution capability
    const teamAndExecution = assessTeamAndExecution(onboarding, profile, memories, achievements);

    // Prepare data structure for AI analysis
    const dashboardDataForAI = {
      progressMetrics: {
        overallCompletion,
        currentStage: stage,
        stagesCompleted: completedStages,
        totalMilestones: submissions.length,
        completedMilestones: achievements.length,
        aiConfidenceAverage: avgConfidence,
        projectViabilityScore: profile?.project_viability_score || 75,
        developmentVelocity,
        consistencyScore
      },
      milestoneAchievements: achievements,
      stageBreakdown,
      marketValidation,
      technicalProgress,
      fundingNeeds,
      teamAndExecution
    };

    // Generate AI insights using cached analysis
    let aiInsights = null;
    try {
      // Use the userId prop directly (it comes from authenticated user)
      const validUserId = userId && userId !== 'unknown' ? userId : null;

      if (validUserId) {
        // Create a stable content identifier for caching
        const completedMilestones = achievements.length;
        const totalSubmissions = submissions.length;
        const stageProgress = Math.floor((completedMilestones / Math.max(totalSubmissions, 1)) * 100);
        const contentKey = `stage_${stage}_milestones_${completedMilestones}_total_${totalSubmissions}_progress_${stageProgress}`;

        console.log('🔑 Investor insights cache key:', contentKey);

        const { result: insights, wasCached } = await aiAnalysisCacheService.getOrCreateAnalysis(
          validUserId,
          'investor_insights',
          contentKey,
          { dashboardData: dashboardDataForAI, userContext },
          async () => {
            return await geminiService.generateInvestorInsights(
              validUserId,
              dashboardDataForAI,
              userContext
            );
          },
          12 // Cache for 12 hours
        );

        aiInsights = insights;
        console.log('🧠 AI Investor Insights generated:', wasCached ? '(cached)' : '(fresh)', insights);
      } else {
        // Generate insights without caching if no valid user ID
        console.log('🧠 Generating AI Investor Insights without caching (no valid user ID)');
        aiInsights = await geminiService.generateInvestorInsights(
          'temp-user',
          dashboardDataForAI,
          userContext
        );
      }
    } catch (error) {
      console.error('Error generating AI insights:', error);
      // Continue without AI insights - the dashboard will fall back to static logic
    }

    // Generate AI professional sections using cached analysis
    let aiProfessionalSections = null;
    try {
      const validUserId = userId && userId !== 'unknown' ? userId : null;

      if (validUserId) {
        // Use the same stable content identifier for professional sections
        const completedMilestones = achievements.length;
        const totalSubmissions = submissions.length;
        const stageProgress = Math.floor((completedMilestones / Math.max(totalSubmissions, 1)) * 100);
        const professionalContentKey = `stage_${stage}_milestones_${completedMilestones}_total_${totalSubmissions}_progress_${stageProgress}`;

        console.log('🔑 Professional sections cache key:', professionalContentKey);

        const professionalData = {
          onboarding,
          profile,
          submissions,
          userContext,
          memories,
          achievements
        };

        const { result: sections, wasCached } = await aiAnalysisCacheService.getOrCreateAnalysis(
          validUserId,
          'professional_sections',
          professionalContentKey,
          professionalData,
          async () => {
            return await geminiService.generateProfessionalSections(
              validUserId,
              professionalData
            );
          },
          24 // Cache for 24 hours (longer since this is more stable content)
        );

        aiProfessionalSections = sections;
        console.log('🎨 AI Professional Sections generated:', wasCached ? '(cached)' : '(fresh)', sections);
      } else {
        // Generate sections without caching if no valid user ID
        console.log('🎨 Generating AI Professional Sections without caching (no valid user ID)');
        aiProfessionalSections = await geminiService.generateProfessionalSections(
          'temp-user',
          {
            onboarding,
            profile,
            submissions,
            userContext,
            memories,
            achievements
          }
        );
      }
    } catch (error) {
      console.error('Error generating AI professional sections:', error);
      // Continue without AI professional sections - the dashboard will fall back to static logic
    }

    return {
      executiveSummary: {
        projectName,
        problemStatement: aiProfessionalSections?.executiveSummary?.problemStatement || onboarding?.problem_description || 'Addressing a significant market need with innovative approach',
        solutionApproach: aiProfessionalSections?.executiveSummary?.solutionApproach || onboarding?.solution_approach || 'Comprehensive solution leveraging modern technology',
        targetAudience: aiProfessionalSections?.executiveSummary?.targetAudience || onboarding?.target_audience || 'Well-defined target market segment',
        uniqueValueProposition: aiProfessionalSections?.executiveSummary?.uniqueValueProposition || onboarding?.unique_value_proposition || 'Unique competitive advantage in the market',
        marketOpportunity: aiProfessionalSections?.executiveSummary?.marketOpportunity || profile?.market_opportunity || onboarding?.success_metrics || 'Significant market opportunity with measurable impact',
        technicalFeasibility: aiProfessionalSections?.executiveSummary?.technicalFeasibility || profile?.technical_feasibility || 'Proven technical approach with validated architecture'
      },
      progressMetrics: {
        overallCompletion,
        currentStage: stage,
        stagesCompleted: completedStages,
        totalMilestones: submissions.length,
        completedMilestones: achievements.length,
        aiConfidenceAverage: avgConfidence,
        projectViabilityScore: profile?.project_viability_score || 75,
        developmentVelocity,
        consistencyScore
      },
      milestoneAchievements: achievements,
      stageBreakdown,
      marketValidation,
      technicalProgress,
      fundingNeeds,
      teamAndExecution,
      aiInsights,
      aiProfessionalSections
    };
  };

  const extractProjectName = (onboarding: any, userContext: any, submissions: any[]): string => {
    // Try multiple sources for project name
    if (onboarding?.solution_approach) {
      const words = onboarding.solution_approach.split(' ').slice(0, 3);
      const name = words.join(' ').replace(/[^a-zA-Z0-9\s]/g, '');
      if (name.length > 3) return name;
    }
    
    if (userContext?.project_info?.name) {
      return userContext.project_info.name;
    }

    // Extract from milestone submissions
    const titleSubmission = submissions.find(s => s.milestone_id.includes('problem') || s.milestone_id.includes('solution'));
    if (titleSubmission?.submission_content) {
      const firstLine = titleSubmission.submission_content.split('\n')[0];
      if (firstLine.length < 50) return firstLine.replace(/[^a-zA-Z0-9\s]/g, '');
    }
    
    return 'Innovation Project';
  };

  const calculateDevelopmentVelocity = (achievements: any[]): number => {
    if (achievements.length < 2) return 0;
    
    const firstDate = new Date(achievements[0].completedDate);
    const lastDate = new Date(achievements[achievements.length - 1].completedDate);
    const weeksDiff = Math.max(1, (lastDate.getTime() - firstDate.getTime()) / (7 * 24 * 60 * 60 * 1000));
    
    return Math.round((achievements.length / weeksDiff) * 10) / 10; // milestones per week
  };

  const calculateConsistencyScore = (submissions: any[], memories: any[]): number => {
    // Calculate based on submission frequency and memory updates
    const recentSubmissions = submissions.filter(s => {
      const submissionDate = new Date(s.created_at || s.submission_date);
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      return submissionDate >= thirtyDaysAgo;
    });

    const recentMemories = memories.filter(m => {
      const memoryDate = new Date(m.created_at);
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      return memoryDate >= thirtyDaysAgo;
    });

    const totalActivity = recentSubmissions.length + recentMemories.length;
    return Math.min(100, Math.round((totalActivity / 30) * 100)); // Activity per day as percentage
  };

  const processStageBreakdown = async (stageProgress: any[], submissions: any[], currentStage: number) => {
    const allStages = devJourneyService.getAllStages();
    
    return allStages.map((stage, index) => {
      const progress = stageProgress[index];
      const stageMilestones = stageProgressService.getStageMilestones(stage.id);
      const stageSubmissions = submissions.filter(s => s.stage_id === stage.id);
      
      const milestones = stageMilestones.map(milestone => {
        const submission = stageSubmissions.find(s => s.milestone_id === milestone.id);
        let aiConfidence = undefined;
        
        if (submission?.ai_insights) {
          try {
            const insights = JSON.parse(submission.ai_insights);
            aiConfidence = insights.finalConfidence || insights.confidence;
          } catch (e) {
            // Ignore parsing errors
          }
        }
        
        return {
          id: milestone.id,
          title: submission?.metadata?.personalized_title || milestone.title,
          completed: submission?.is_approved || submission?.completed || false,
          aiConfidence,
          completedDate: submission?.approval_date || submission?.created_at
        };
      });

      const completedMilestones = milestones.filter(m => m.completed).length;
      const completionPercentage = stageMilestones.length > 0 
        ? Math.round((completedMilestones / stageMilestones.length) * 100) 
        : 0;

      return {
        stage: stage.id,
        name: stage.name,
        description: stage.description,
        completionPercentage,
        milestones,
        isActive: stage.id === currentStage,
        isCompleted: completionPercentage === 100
      };
    });
  };

  const extractMarketValidation = (onboarding: any, submissions: any[], memories: any[], profile: any) => {
    const validationSubmissions = submissions.filter(s => 
      s.milestone_id.includes('validation') || 
      s.milestone_id.includes('user') || 
      s.milestone_id.includes('market')
    );

    const userFeedback = validationSubmissions
      .map(s => s.submission_content)
      .filter(content => content && content.length > 50)
      .slice(0, 3);

    const validationEvidence = validationSubmissions
      .map(s => s.ai_feedback)
      .filter(feedback => feedback && feedback.length > 20)
      .slice(0, 3);

    return {
      userFeedback,
      validationEvidence,
      marketTraction: onboarding?.success_metrics || 'Building initial market traction',
      competitiveAdvantage: onboarding?.unique_value_proposition || 'Unique market positioning'
    };
  };

  const extractTechnicalProgress = (submissions: any[], memories: any[], onboarding: any) => {
    const technicalSubmissions = submissions.filter(s => 
      s.milestone_id.includes('mvp') || 
      s.milestone_id.includes('technical') || 
      s.milestone_id.includes('build') ||
      s.milestone_id.includes('architecture')
    );

    const architectureDecisions = technicalSubmissions
      .map(s => s.submission_content)
      .filter(content => content && content.includes('architecture'))
      .slice(0, 3);

    const technicalMilestones = technicalSubmissions
      .map(s => s.milestone_id.replace(/_/g, ' '))
      .slice(0, 5);

    return {
      architectureDecisions,
      technicalMilestones,
      codeQuality: 'Maintaining high code quality standards',
      scalabilityPlan: onboarding?.solution_approach || 'Scalable architecture design'
    };
  };

  const generateFundingNeeds = (onboarding: any, profile: any, achievements: any[], stage: number, stageBreakdown: any[]) => {
    const currentStageInfo = devJourneyService.getStage(stage);
    const currentStageName = currentStageInfo ? `Stage ${currentStageInfo.id}: ${currentStageInfo.title.replace('Stage ' + currentStageInfo.id + ': ', '')}` : 'Development Stage';
    const nextStage = devJourneyService.getStage(stage + 1);
    const supportNeeded = profile?.recommended_next_steps || [
      'Technical development acceleration',
      'Market validation expansion', 
      'Team building and scaling',
      'Go-to-market strategy execution'
    ];

    const riskAssessment = [
      'Market adoption timeline',
      'Technical execution complexity',
      'Competitive landscape evolution',
      'Resource allocation optimization'
    ];

    return {
      currentStage: currentStageName,
      nextMilestone: nextStage?.name || 'Market Launch',
      supportNeeded,
      projectedTimeline: calculateProjectedTimeline(achievements, stage),
      investmentOpportunity: generateInvestmentOpportunity(onboarding, profile, achievements),
      riskAssessment
    };
  };

  const assessTeamAndExecution = (onboarding: any, profile: any, memories: any[], achievements: any[]) => {
    const executionVelocity = achievements.length > 0 ? 'High execution velocity demonstrated' : 'Building execution momentum';
    const learningRate = memories.length > 10 ? 'Rapid learning and adaptation' : 'Steady learning progress';
    
    return {
      founderBackground: onboarding?.technical_background || 'Strong technical and domain expertise',
      executionCapability: executionVelocity,
      learningVelocity: learningRate,
      adaptability: 'Demonstrated ability to iterate based on feedback'
    };
  };

  const calculateProjectedTimeline = (achievements: any[], stage: number): string => {
    const velocity = achievements.length / Math.max(1, stage + 1);
    const remainingStages = 6 - stage;
    const estimatedWeeks = Math.ceil(remainingStages / Math.max(0.5, velocity)) * 3;
    return `${estimatedWeeks} weeks to next major milestone`;
  };

  const generateInvestmentOpportunity = (onboarding: any, profile: any, achievements: any[]): string => {
    const viabilityScore = profile?.project_viability_score || 75;
    const progressEvidence = achievements.length;
    
    return `Validated opportunity with ${viabilityScore}% viability score and ${progressEvidence} completed milestones demonstrating execution capability.`;
  };

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  const refreshAIInsights = async () => {
    if (!userId || refreshingInsights) return;

    setRefreshingInsights(true);
    try {
      console.log('🔄 Refreshing AI insights - invalidating cache...');

      // Invalidate both investor insights and professional sections cache
      await aiAnalysisCacheService.invalidateCache(userId, 'investor_insights');
      await aiAnalysisCacheService.invalidateCache(userId, 'professional_sections');

      // Also invalidate dashboard cache to force regeneration
      await dashboardCacheService.invalidateDashboardCache(userId, currentStage);

      console.log('✅ Cache invalidated, reloading dashboard data...');

      // Reload the dashboard data which will regenerate AI insights
      await loadComprehensiveData();

      console.log('✅ AI insights refreshed successfully');
    } catch (error) {
      console.error('❌ Error refreshing AI insights:', error);
    } finally {
      setRefreshingInsights(false);
    }
  };



  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-12">
        <div className="text-center">
          <BarChart3 className="h-12 w-12 animate-pulse text-purple-400 mx-auto mb-4" />
          <p className="text-accent-white/70">Generating comprehensive investor dashboard...</p>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="text-center p-12">
        <p className="text-accent-white/70">Unable to generate dashboard. Please ensure you have completed your onboarding.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-accent-white mb-2">
            {dashboardData.executiveSummary.projectName}
          </h1>
          <p className="text-accent-white/70">
            AI-Powered Investment Analysis Dashboard
          </p>
        </div>
        <div>
          <Button
            onClick={refreshAIInsights}
            disabled={refreshingInsights}
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white border-0"
          >
            {refreshingInsights ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh AI Insights
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Executive Summary & Key Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Executive Summary */}
        <div className="lg:col-span-2">
          <Card className="bg-gradient-to-br from-purple-500/10 to-blue-500/10 border-purple-400/30">
            <CardHeader
              className="cursor-pointer"
              onClick={() => toggleSection('executive')}
            >
              <CardTitle className="text-accent-white flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-purple-400" />
                  Executive Summary
                </div>
                {expandedSections.has('executive') ?
                  <ChevronUp className="h-5 w-5 text-accent-white/40" /> :
                  <ChevronDown className="h-5 w-5 text-accent-white/40" />
                }
              </CardTitle>
            </CardHeader>
            <AnimatePresence>
              {expandedSections.has('executive') && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium text-purple-400 mb-2">Problem Statement</h4>
                      <div className="text-sm text-accent-white/80">
                        <MarkdownText content={dashboardData.executiveSummary.problemStatement} />
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-blue-400 mb-2">Solution Approach</h4>
                      <div className="text-sm text-accent-white/80">
                        <MarkdownText content={dashboardData.executiveSummary.solutionApproach} />
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-green-400 mb-2">Unique Value Proposition</h4>
                      <div className="text-sm text-accent-white/80">
                        <MarkdownText content={dashboardData.executiveSummary.uniqueValueProposition} />
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-yellow-400 mb-2">Market Opportunity</h4>
                      <div className="text-sm text-accent-white/80">
                        <MarkdownText content={dashboardData.executiveSummary.marketOpportunity} />
                      </div>
                    </div>
                  </CardContent>
                </motion.div>
              )}
            </AnimatePresence>
          </Card>
        </div>

        {/* Key Metrics */}
        <Card className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border-green-400/30">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-green-400" />
              Key Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <div className="text-center p-3 bg-green-500/5 rounded border border-green-400/20">
                  <div className="text-2xl font-bold text-green-400">
                    {dashboardData.progressMetrics.projectViabilityScore}%
                  </div>
                  <div className="text-xs text-accent-white/60">Viability Score</div>
                </div>
                <div className="text-center p-3 bg-blue-500/5 rounded border border-blue-400/20">
                  <div className="text-2xl font-bold text-blue-400">
                    {dashboardData.progressMetrics.overallCompletion}%
                  </div>
                  <div className="text-xs text-accent-white/60">Journey Complete</div>
                </div>
                <div className="text-center p-3 bg-purple-500/5 rounded border border-purple-400/20">
                  <div className="text-2xl font-bold text-purple-400">
                    {dashboardData.progressMetrics.completedMilestones}
                  </div>
                  <div className="text-xs text-accent-white/60">Milestones</div>
                </div>
                <div className="text-center p-3 bg-yellow-500/5 rounded border border-yellow-400/20">
                  <div className="text-2xl font-bold text-yellow-400">
                    {dashboardData.progressMetrics.aiConfidenceAverage}%
                  </div>
                  <div className="text-xs text-accent-white/60">AI Confidence</div>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm text-accent-white/70 mb-1">
                    <span>Development Velocity</span>
                    <span>{dashboardData.progressMetrics.developmentVelocity}/week</span>
                  </div>
                  <Progress value={Math.min(100, dashboardData.progressMetrics.developmentVelocity * 20)} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm text-accent-white/70 mb-1">
                    <span>Consistency Score</span>
                    <span>{dashboardData.progressMetrics.consistencyScore}%</span>
                  </div>
                  <Progress value={dashboardData.progressMetrics.consistencyScore} className="h-2" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Milestone Achievements & Evidence */}
      <Card className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 border-green-400/30">
        <CardHeader
          className="cursor-pointer"
          onClick={() => toggleSection('achievements')}
        >
          <CardTitle className="text-accent-white flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Award className="h-5 w-5 text-green-400" />
              Milestone Achievements & Validation Evidence
            </div>
            {expandedSections.has('achievements') ?
              <ChevronUp className="h-5 w-5 text-accent-white/40" /> :
              <ChevronDown className="h-5 w-5 text-accent-white/40" />
            }
          </CardTitle>
        </CardHeader>
        <AnimatePresence>
          {expandedSections.has('achievements') && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <CardContent>
                <div className="space-y-4">
                  {dashboardData.milestoneAchievements.length > 0 ? (
                    dashboardData.milestoneAchievements.map((achievement, index) => (
                      <motion.div
                        key={achievement.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="p-4 bg-green-500/5 border border-green-400/20 rounded-lg"
                      >
                        <div className="flex items-start gap-4">
                          <div className="flex-shrink-0 w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center">
                            <CheckCircle className="h-5 w-5 text-green-400" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-3">
                              <h4 className="font-medium text-accent-white">{achievement.title}</h4>
                              <div className="flex items-center gap-2">
                                <Badge className="bg-purple-500/20 text-purple-200 text-xs">
                                  Stage {achievement.stage}
                                </Badge>
                              </div>
                            </div>

                            <div className="space-y-3">
                              <div>
                                <h5 className="text-sm font-medium text-green-400 mb-1">Submission Content:</h5>
                                <div className="text-sm text-accent-white/80 bg-accent-white/5 p-3 rounded border border-accent-white/10">
                                  <MarkdownText content={achievement.validationEvidence} />
                                </div>
                              </div>

                              {achievement.keyDeliverables.length > 0 && (
                                <div>
                                  <h5 className="text-sm font-medium text-blue-400 mb-2">Key Deliverables:</h5>
                                  <div className="flex flex-wrap gap-2">
                                    {achievement.keyDeliverables.map((deliverable, idx) => (
                                      <Badge key={idx} variant="outline" className="text-xs bg-blue-500/10 border-blue-400/30">
                                        {deliverable}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {achievement.aiInsights && (
                                <div>
                                  <h5 className="text-sm font-medium text-purple-400 mb-2">AI Strategic Analysis:</h5>
                                  <div className="space-y-2">
                                    <div className="text-sm text-accent-white/80 bg-purple-500/5 p-3 rounded border border-purple-400/20">
                                      <div className="font-medium text-purple-300 mb-1">Key Learnings:</div>
                                      {achievement.aiInsights.keyLearnings?.join(', ') || 'Milestone demonstrates strong execution capability and market understanding'}
                                    </div>
                                    {achievement.aiInsights.nextSteps && (
                                      <div className="text-sm text-accent-white/80 bg-blue-500/5 p-3 rounded border border-blue-400/20">
                                        <div className="font-medium text-blue-300 mb-1">Strategic Next Steps:</div>
                                        {achievement.aiInsights.nextSteps.join(', ')}
                                      </div>
                                    )}
                                    <div className="text-sm text-accent-white/80 bg-green-500/5 p-3 rounded border border-green-400/20">
                                      <div className="font-medium text-green-300 mb-1">Investment Implications:</div>
                                      This milestone validates {achievement.stage <= 2 ? 'early-stage execution capability and market validation' : 'advanced development progress and scalability potential'}, indicating strong ROI potential for investors.
                                    </div>
                                  </div>
                                </div>
                              )}

                              <div className="flex items-center gap-4 text-xs text-accent-white/50">
                                <div className="flex items-center gap-1">
                                  <Calendar className="h-3 w-3" />
                                  Completed: {new Date(achievement.completedDate).toLocaleDateString()}
                                </div>
                                <div className="flex items-center gap-1">
                                  <Brain className="h-3 w-3" />
                                  AI Validated
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-accent-white/60">
                      <Award className="h-12 w-12 mx-auto mb-4 text-accent-white/40" />
                      <p>No milestone achievements yet. Complete milestones to see validation evidence here.</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>



      {/* AI Investment Analysis */}
      <Card className="bg-gradient-to-br from-indigo-500/10 to-purple-500/10 border-indigo-400/30">
        <CardHeader
          className="cursor-pointer"
          onClick={() => toggleSection('ai-analysis')}
        >
          <CardTitle className="text-accent-white flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-indigo-400" />
              AI Investment Analysis & Risk Assessment
            </div>
            {expandedSections.has('ai-analysis') ?
              <ChevronUp className="h-5 w-5 text-accent-white/40" /> :
              <ChevronDown className="h-5 w-5 text-accent-white/40" />
            }
          </CardTitle>
        </CardHeader>
        <AnimatePresence>
          {expandedSections.has('ai-analysis') && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <CardContent className="space-y-6">
                {/* Investment Readiness Score */}
                <div className="p-4 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-lg border border-indigo-400/20">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-indigo-400">Investment Readiness Score</h4>
                    <div className="text-2xl font-bold text-indigo-400">
                      {dashboardData.aiInsights?.investmentReadiness?.score ||
                       Math.round((dashboardData.progressMetrics.projectViabilityScore + dashboardData.progressMetrics.aiConfidenceAverage) / 2)}%
                    </div>
                  </div>
                  <p className="text-sm text-accent-white/80">
                    {dashboardData.aiInsights?.investmentReadiness?.reasoning ||
                     `Based on AI analysis of project progress, market validation, and execution capability,
                      this venture demonstrates ${dashboardData.progressMetrics.projectViabilityScore > 75 ? 'high' : 'moderate'} investment readiness
                      with strong potential for ${dashboardData.progressMetrics.overallCompletion > 50 ? 'near-term' : 'medium-term'} returns.`}
                  </p>
                  {dashboardData.aiInsights?.investmentReadiness && (
                    <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div>
                        <h6 className="text-xs font-medium text-emerald-400 mb-1">Key Strengths</h6>
                        <ul className="text-xs text-accent-white/70 space-y-1">
                          {dashboardData.aiInsights.investmentReadiness.keyStrengths.map((strength, idx) => (
                            <li key={idx}>• {strength}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h6 className="text-xs font-medium text-yellow-400 mb-1">Areas for Improvement</h6>
                        <ul className="text-xs text-accent-white/70 space-y-1">
                          {dashboardData.aiInsights.investmentReadiness.areasForImprovement.map((area, idx) => (
                            <li key={idx}>• {area}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}
                </div>

                {/* Market Opportunity Analysis */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-emerald-500/5 rounded-lg border border-emerald-400/20">
                    <h5 className="font-medium text-emerald-400 mb-2">Market Opportunity</h5>
                    <p className="text-sm text-accent-white/80">
                      {dashboardData.aiInsights?.growthPotential?.marketPenetration ||
                       `AI analysis indicates ${dashboardData.milestoneAchievements.length > 3 ? 'validated' : 'emerging'} market opportunity
                        with ${dashboardData.progressMetrics.consistencyScore > 70 ? 'strong' : 'developing'} execution momentum.
                        Target market shows ${dashboardData.marketValidation.userFeedback.length > 0 ? 'positive validation signals' : 'early-stage potential'}.`}
                    </p>
                  </div>

                  <div className="p-4 bg-yellow-500/5 rounded-lg border border-yellow-400/20">
                    <h5 className="font-medium text-yellow-400 mb-2">Competitive Advantage</h5>
                    <p className="text-sm text-accent-white/80">
                      Technical feasibility assessment shows {dashboardData.technicalProgress.technicalMilestones.length > 2 ? 'strong' : 'developing'}
                      differentiation potential. AI confidence in execution suggests
                      {dashboardData.progressMetrics.aiConfidenceAverage > 75 ? 'sustainable competitive moats' : 'emerging competitive positioning'}.
                    </p>
                  </div>
                </div>

                {/* Risk Assessment */}
                <div className="p-4 bg-red-500/5 rounded-lg border border-red-400/20">
                  <h5 className="font-medium text-red-400 mb-3">AI Risk Assessment</h5>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div className="text-center">
                      <div className={`text-lg font-bold ${
                        dashboardData.aiInsights?.riskAssessment?.executionRisk?.level === 'Low' ? 'text-green-400' :
                        dashboardData.aiInsights?.riskAssessment?.executionRisk?.level === 'High' ? 'text-red-400' : 'text-yellow-400'
                      }`}>
                        {dashboardData.aiInsights?.riskAssessment?.executionRisk?.level ||
                         (dashboardData.progressMetrics.developmentVelocity > 1 ? 'Low' : 'Medium')}
                      </div>
                      <div className="text-xs text-accent-white/60">Execution Risk</div>
                      {dashboardData.aiInsights?.riskAssessment?.executionRisk?.confidence && (
                        <div className="text-xs text-accent-white/40 mt-1">
                          {dashboardData.aiInsights.riskAssessment.executionRisk.confidence}% confidence
                        </div>
                      )}
                    </div>
                    <div className="text-center">
                      <div className={`text-lg font-bold ${
                        dashboardData.aiInsights?.riskAssessment?.marketRisk?.level === 'Low' ? 'text-green-400' :
                        dashboardData.aiInsights?.riskAssessment?.marketRisk?.level === 'High' ? 'text-red-400' : 'text-yellow-400'
                      }`}>
                        {dashboardData.aiInsights?.riskAssessment?.marketRisk?.level ||
                         (dashboardData.marketValidation.userFeedback.length > 1 ? 'Low' : 'Medium')}
                      </div>
                      <div className="text-xs text-accent-white/60">Market Risk</div>
                      {dashboardData.aiInsights?.riskAssessment?.marketRisk?.confidence && (
                        <div className="text-xs text-accent-white/40 mt-1">
                          {dashboardData.aiInsights.riskAssessment.marketRisk.confidence}% confidence
                        </div>
                      )}
                    </div>
                    <div className="text-center">
                      <div className={`text-lg font-bold ${
                        dashboardData.aiInsights?.riskAssessment?.technicalRisk?.level === 'Low' ? 'text-green-400' :
                        dashboardData.aiInsights?.riskAssessment?.technicalRisk?.level === 'High' ? 'text-red-400' : 'text-yellow-400'
                      }`}>
                        {dashboardData.aiInsights?.riskAssessment?.technicalRisk?.level ||
                         (dashboardData.technicalProgress.technicalMilestones.length > 2 ? 'Low' : 'Medium')}
                      </div>
                      <div className="text-xs text-accent-white/60">Technical Risk</div>
                      {dashboardData.aiInsights?.riskAssessment?.technicalRisk?.confidence && (
                        <div className="text-xs text-accent-white/40 mt-1">
                          {dashboardData.aiInsights.riskAssessment.technicalRisk.confidence}% confidence
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Risk Assessment Details */}
                  {dashboardData.aiInsights?.riskAssessment && (
                    <div className="mt-4 space-y-2">
                      <div className="text-xs text-accent-white/80">
                        <strong>Execution:</strong> {dashboardData.aiInsights.riskAssessment.executionRisk.reasoning}
                      </div>
                      <div className="text-xs text-accent-white/80">
                        <strong>Market:</strong> {dashboardData.aiInsights.riskAssessment.marketRisk.reasoning}
                      </div>
                      <div className="text-xs text-accent-white/80">
                        <strong>Technical:</strong> {dashboardData.aiInsights.riskAssessment.technicalRisk.reasoning}
                      </div>
                    </div>
                  )}
                </div>

                {/* Growth Potential */}
                <div className="p-4 bg-blue-500/5 rounded-lg border border-blue-400/20">
                  <h5 className="font-medium text-blue-400 mb-2">AI Growth Potential Prediction</h5>
                  <p className="text-sm text-accent-white/80">
                    {dashboardData.aiInsights?.growthPotential?.overallPrediction ||
                     `Based on current trajectory analysis, the project shows
                      ${dashboardData.progressMetrics.overallCompletion > 60 ? 'high' : 'moderate'} growth potential with
                      projected ${dashboardData.progressMetrics.developmentVelocity > 1.5 ? 'accelerated' : 'steady'} scaling capability.
                      AI models predict ${dashboardData.progressMetrics.consistencyScore > 80 ? 'strong' : 'developing'} market penetration potential
                      based on execution consistency and validation metrics.`}
                  </p>

                  {/* Additional AI Growth Insights */}
                  {dashboardData.aiInsights?.growthPotential && (
                    <div className="mt-3 space-y-2">
                      <div className="text-xs text-accent-white/80">
                        <strong>Scaling Capability:</strong> {dashboardData.aiInsights.growthPotential.scalingCapability}
                      </div>
                      <div className="text-xs text-accent-white/80">
                        <strong>Trajectory Analysis:</strong> {dashboardData.aiInsights.growthPotential.trajectoryAnalysis}
                      </div>
                    </div>
                  )}

                  {/* AI Confidence Indicator */}
                  {dashboardData.aiInsights?.confidence && (
                    <div className="mt-3 flex items-center justify-between">
                      <span className="text-xs text-accent-white/60">AI Analysis Confidence</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-16 h-2 bg-accent-white/20 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-blue-400 transition-all duration-300"
                            style={{ width: `${dashboardData.aiInsights.confidence}%` }}
                          />
                        </div>
                        <span className="text-xs text-blue-400 font-medium">
                          {dashboardData.aiInsights.confidence}%
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>

      {/* Market Validation & Technical Progress */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Market Validation */}
        <Card className="bg-gradient-to-br from-emerald-500/10 to-teal-500/10 border-emerald-400/30">
          <CardHeader
            className="cursor-pointer"
            onClick={() => toggleSection('market')}
          >
            <CardTitle className="text-accent-white flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-emerald-400" />
                Market Validation & Traction
              </div>
              {expandedSections.has('market') ?
                <ChevronUp className="h-5 w-5 text-accent-white/40" /> :
                <ChevronDown className="h-5 w-5 text-accent-white/40" />
              }
            </CardTitle>
          </CardHeader>
          <AnimatePresence>
            {expandedSections.has('market') && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <CardContent className="space-y-4">
                  {/* AI-Enhanced Market Validation Summary */}
                  {dashboardData.aiProfessionalSections?.marketValidation && (
                    <div>
                      <h4 className="font-medium text-emerald-400 mb-3">Market Validation Summary</h4>
                      <div className="text-sm text-accent-white/80 mb-4 p-3 bg-emerald-500/5 rounded border border-emerald-400/20">
                        <MarkdownText content={dashboardData.aiProfessionalSections.marketValidation.validationSummary} />
                      </div>
                    </div>
                  )}

                  <div>
                    <h4 className="font-medium text-emerald-400 mb-3">Target Audience</h4>
                    <div className="text-sm text-accent-white/80 mb-4">
                      <MarkdownText content={dashboardData.executiveSummary.targetAudience} />
                    </div>

                    <div className="flex items-center gap-2 p-3 bg-emerald-500/5 rounded-lg border border-emerald-400/20">
                      <Shield className="h-4 w-4 text-emerald-400" />
                      <span className="text-sm text-emerald-400 font-medium">Validated Market Need</span>
                    </div>
                  </div>

                  {/* AI-Enhanced User Feedback Synthesis */}
                  {dashboardData.aiProfessionalSections?.marketValidation?.userFeedbackSynthesis && (
                    <div>
                      <h4 className="font-medium text-teal-400 mb-3">User Feedback Insights</h4>
                      <div className="p-3 bg-teal-500/5 rounded border border-teal-400/20">
                        <div className="text-sm text-accent-white/80">
                          <MarkdownText content={dashboardData.aiProfessionalSections.marketValidation.userFeedbackSynthesis} />
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Original User Feedback (if available and no AI synthesis) */}
                  {!dashboardData.aiProfessionalSections?.marketValidation?.userFeedbackSynthesis &&
                   dashboardData.marketValidation.userFeedback.length > 0 && (
                    <div>
                      <h4 className="font-medium text-teal-400 mb-3">User Feedback & Validation</h4>
                      <div className="space-y-2">
                        {dashboardData.marketValidation.userFeedback.slice(0, 2).map((feedback, idx) => (
                          <div key={idx} className="p-3 bg-teal-500/5 rounded border border-teal-400/20">
                            <div className="text-sm text-accent-white/80">
                              <MarkdownText content={feedback.substring(0, 200) + '...'} />
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div>
                    <h4 className="font-medium text-blue-400 mb-3">Market Opportunity</h4>
                    <div className="text-sm text-accent-white/80">
                      <MarkdownText content={
                        dashboardData.aiProfessionalSections?.marketValidation?.marketTraction ||
                        dashboardData.marketValidation.marketTraction
                      } />
                    </div>
                  </div>
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>

        {/* Technical Progress */}
        <Card className="bg-gradient-to-br from-blue-500/10 to-indigo-500/10 border-blue-400/30">
          <CardHeader
            className="cursor-pointer"
            onClick={() => toggleSection('technical')}
          >
            <CardTitle className="text-accent-white flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-blue-400" />
                Technical Progress & Architecture
              </div>
              {expandedSections.has('technical') ?
                <ChevronUp className="h-5 w-5 text-accent-white/40" /> :
                <ChevronDown className="h-5 w-5 text-accent-white/40" />
              }
            </CardTitle>
          </CardHeader>
          <AnimatePresence>
            {expandedSections.has('technical') && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <CardContent className="space-y-4">
                  {/* AI-Enhanced Technical Summary */}
                  {dashboardData.aiProfessionalSections?.technicalProgress && (
                    <div>
                      <h4 className="font-medium text-blue-400 mb-3">Technical Architecture Overview</h4>
                      <div className="text-sm text-accent-white/80 mb-4 p-3 bg-blue-500/5 rounded border border-blue-400/20">
                        <MarkdownText content={dashboardData.aiProfessionalSections.technicalProgress.architectureSummary} />
                      </div>
                    </div>
                  )}

                  <div>
                    <h4 className="font-medium text-blue-400 mb-3">Technical Feasibility</h4>
                    <div className="text-sm text-accent-white/80 mb-3">
                      <MarkdownText content={dashboardData.executiveSummary.technicalFeasibility} />
                    </div>
                  </div>

                  {/* AI-Enhanced Development Progress */}
                  {dashboardData.aiProfessionalSections?.technicalProgress?.developmentProgress && (
                    <div>
                      <h4 className="font-medium text-indigo-400 mb-3">Development Progress</h4>
                      <div className="text-sm text-accent-white/80 mb-4 p-3 bg-indigo-500/5 rounded border border-indigo-400/20">
                        <MarkdownText content={dashboardData.aiProfessionalSections.technicalProgress.developmentProgress} />
                      </div>
                    </div>
                  )}

                  {/* Original Technical Milestones (if no AI summary) */}
                  {!dashboardData.aiProfessionalSections?.technicalProgress?.developmentProgress &&
                   dashboardData.technicalProgress.technicalMilestones.length > 0 && (
                    <div>
                      <h4 className="font-medium text-indigo-400 mb-3">Technical Milestones Achieved</h4>
                      <div className="space-y-2">
                        {dashboardData.technicalProgress.technicalMilestones.map((milestone, idx) => (
                          <div key={idx} className="flex items-center gap-2 p-2 bg-indigo-500/5 rounded border border-indigo-400/20">
                            <CheckCircle className="h-4 w-4 text-indigo-400 flex-shrink-0" />
                            <span className="text-sm text-accent-white/80 capitalize">{milestone}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div>
                    <h4 className="font-medium text-cyan-400 mb-3">Scalability & Architecture</h4>
                    <div className="text-sm text-accent-white/80">
                      <MarkdownText content={
                        dashboardData.aiProfessionalSections?.technicalProgress?.scalabilityStrategy ||
                        dashboardData.technicalProgress.scalabilityPlan
                      } />
                    </div>
                  </div>
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>
      </div>

      {/* Investment Opportunity & Team Execution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Investment Opportunity */}
        <Card className="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 border-yellow-400/30">
          <CardHeader
            className="cursor-pointer"
            onClick={() => toggleSection('investment')}
          >
            <CardTitle className="text-accent-white flex items-center justify-between">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-yellow-400" />
                Investment Opportunity & Funding Needs
              </div>
              {expandedSections.has('investment') ?
                <ChevronUp className="h-5 w-5 text-accent-white/40" /> :
                <ChevronDown className="h-5 w-5 text-accent-white/40" />
              }
            </CardTitle>
          </CardHeader>
          <AnimatePresence>
            {expandedSections.has('investment') && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <CardContent className="space-y-4">
                  {/* AI-Enhanced Investment Opportunity */}
                  <div className="p-4 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-lg border border-yellow-400/20">
                    <div className="flex items-center gap-2 mb-3">
                      <Star className="h-5 w-5 text-yellow-400" />
                      <span className="font-medium text-accent-white">Investment Opportunity</span>
                    </div>
                    <p className="text-sm text-accent-white/80">
                      {dashboardData.aiProfessionalSections?.fundingAndGrowth?.investmentOpportunity ||
                       dashboardData.fundingNeeds.investmentOpportunity}
                    </p>
                  </div>

                  {/* AI-Enhanced Growth Strategy */}
                  {dashboardData.aiProfessionalSections?.fundingAndGrowth?.growthStrategy && (
                    <div>
                      <h4 className="font-medium text-yellow-400 mb-3">Growth Strategy</h4>
                      <div className="text-sm text-accent-white/80 p-3 bg-yellow-500/5 rounded border border-yellow-400/20">
                        <MarkdownText content={dashboardData.aiProfessionalSections.fundingAndGrowth.growthStrategy} />
                      </div>
                    </div>
                  )}

                  {/* AI-Enhanced Funding Needs */}
                  {dashboardData.aiProfessionalSections?.fundingAndGrowth?.fundingNeeds && (
                    <div>
                      <h4 className="font-medium text-orange-400 mb-3">Funding Requirements</h4>
                      <div className="text-sm text-accent-white/80 p-3 bg-orange-500/5 rounded border border-orange-400/20">
                        <MarkdownText content={dashboardData.aiProfessionalSections.fundingAndGrowth.fundingNeeds} />
                      </div>
                    </div>
                  )}

                  <div>
                    <h4 className="font-medium text-orange-400 mb-3">Current Status & Next Steps</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between p-2 bg-orange-500/5 rounded">
                        <span className="text-sm text-accent-white/80">Current Stage:</span>
                        <span className="text-sm font-medium text-orange-400">{dashboardData.fundingNeeds.currentStage}</span>
                      </div>
                      <div className="flex items-center justify-between p-2 bg-orange-500/5 rounded">
                        <span className="text-sm text-accent-white/80">Next Milestone:</span>
                        <span className="text-sm font-medium text-orange-400">{dashboardData.fundingNeeds.nextMilestone}</span>
                      </div>
                      <div className="flex items-center justify-between p-2 bg-orange-500/5 rounded">
                        <span className="text-sm text-accent-white/80">Timeline:</span>
                        <span className="text-sm font-medium text-orange-400">{dashboardData.fundingNeeds.projectedTimeline}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-red-400 mb-3">Support & Investment Needs</h4>
                    <div className="space-y-2">
                      {dashboardData.fundingNeeds.supportNeeded.map((need, index) => (
                        <div key={index} className="flex items-center gap-3 p-2 bg-red-500/5 rounded">
                          <ArrowRight className="h-4 w-4 text-red-400 flex-shrink-0" />
                          <span className="text-accent-white/80 text-sm">{need}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>

        {/* Team & Execution */}
        <Card className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border-purple-400/30">
          <CardHeader
            className="cursor-pointer"
            onClick={() => toggleSection('team')}
          >
            <CardTitle className="text-accent-white flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Rocket className="h-5 w-5 text-purple-400" />
                Team & Execution Capability
              </div>
              {expandedSections.has('team') ?
                <ChevronUp className="h-5 w-5 text-accent-white/40" /> :
                <ChevronDown className="h-5 w-5 text-accent-white/40" />
              }
            </CardTitle>
          </CardHeader>
          <AnimatePresence>
            {expandedSections.has('team') && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <CardContent className="space-y-4">
                  {/* AI-Enhanced Execution Capability Summary */}
                  {dashboardData.aiProfessionalSections?.teamAndExecution && (
                    <div>
                      <h4 className="font-medium text-purple-400 mb-3">Execution Capability Assessment</h4>
                      <div className="text-sm text-accent-white/80 mb-4 p-3 bg-purple-500/5 rounded border border-purple-400/20">
                        <MarkdownText content={dashboardData.aiProfessionalSections.teamAndExecution.executionCapability} />
                      </div>
                    </div>
                  )}

                  <div>
                    <h4 className="font-medium text-purple-400 mb-3">Founder Background</h4>
                    <div className="text-sm text-accent-white/80 p-3 bg-purple-500/5 rounded border border-purple-400/20">
                      <MarkdownText content={dashboardData.teamAndExecution.founderBackground} />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h5 className="font-medium text-pink-400 mb-2">Execution Capability</h5>
                      <div className="text-sm text-accent-white/80 p-3 bg-pink-500/5 rounded border border-pink-400/20">
                        {dashboardData.aiProfessionalSections?.teamAndExecution?.executionCapability ||
                         dashboardData.teamAndExecution.executionCapability}
                      </div>
                    </div>
                    <div>
                      <h5 className="font-medium text-indigo-400 mb-2">Development Velocity</h5>
                      <div className="text-sm text-accent-white/80 p-3 bg-indigo-500/5 rounded border border-indigo-400/20">
                        {dashboardData.aiProfessionalSections?.teamAndExecution?.developmentVelocity ||
                         dashboardData.teamAndExecution.learningVelocity}
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-cyan-400 mb-3">Consistency & Reliability</h4>
                    <div className="text-sm text-accent-white/80 p-3 bg-cyan-500/5 rounded border border-cyan-400/20">
                      {dashboardData.aiProfessionalSections?.teamAndExecution?.consistencyAndReliability ||
                       dashboardData.teamAndExecution.adaptability}
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-3 mt-4">
                    <div className="text-center p-3 bg-purple-500/5 rounded border border-purple-400/20">
                      <div className="text-lg font-bold text-purple-400">
                        {dashboardData.progressMetrics.developmentVelocity}
                      </div>
                      <div className="text-xs text-accent-white/60">Milestones/Week</div>
                    </div>
                    <div className="text-center p-3 bg-pink-500/5 rounded border border-pink-400/20">
                      <div className="text-lg font-bold text-pink-400">
                        {dashboardData.progressMetrics.consistencyScore}%
                      </div>
                      <div className="text-xs text-accent-white/60">Consistency</div>
                    </div>
                    <div className="text-center p-3 bg-indigo-500/5 rounded border border-indigo-400/20">
                      <div className="text-lg font-bold text-indigo-400">
                        {dashboardData.progressMetrics.aiConfidenceAverage}%
                      </div>
                      <div className="text-xs text-accent-white/60">AI Confidence</div>
                    </div>
                  </div>
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>
      </div>


    </div>
  );
};

export default ComprehensiveInvestorDashboard;
