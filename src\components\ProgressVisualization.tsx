import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  Users, 
  Target, 
  Clock,
  CheckCircle,
  AlertTriangle,
  Star,
  Activity
} from 'lucide-react';

interface UserJourneyTimelineProps {
  stages: {
    stage: number;
    name: string;
    userCount: number;
    avgDays: number;
    completionRate: number;
  }[];
}

export const UserJourneyTimeline: React.FC<UserJourneyTimelineProps> = ({ stages }) => {
  const maxUsers = Math.max(...stages.map(s => s.userCount));

  return (
    <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
      <CardHeader>
        <CardTitle className="text-xl font-bold text-accent-white flex items-center gap-2">
          <Target className="h-6 w-6 text-sauce-red" />
          User Journey Timeline
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {stages.map((stage, index) => (
            <motion.div
              key={stage.stage}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="relative"
            >
              {/* Timeline connector */}
              {index < stages.length - 1 && (
                <div className="absolute left-6 top-12 w-0.5 h-8 bg-accent-white/30" />
              )}
              
              <div className="flex items-start gap-4">
                {/* Stage indicator */}
                <div className="flex-shrink-0 w-12 h-12 rounded-full bg-sauce-red/20 border-2 border-sauce-red flex items-center justify-center">
                  <span className="text-sauce-red font-bold">{stage.stage}</span>
                </div>
                
                {/* Stage content */}
                <div className="flex-1 bg-accent-white/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-accent-white">{stage.name}</h3>
                    <Badge variant="outline" className="text-accent-white border-accent-white/30">
                      {stage.userCount} users
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 mb-3">
                    <div className="text-center">
                      <p className="text-lg font-bold text-accent-white">{stage.avgDays}</p>
                      <p className="text-xs text-accent-white/60">Avg Days</p>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-bold text-accent-white">{stage.completionRate}%</p>
                      <p className="text-xs text-accent-white/60">Completion</p>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-bold text-accent-white">{Math.round((stage.userCount / maxUsers) * 100)}%</p>
                      <p className="text-xs text-accent-white/60">Of Total</p>
                    </div>
                  </div>
                  
                  {/* Progress bar */}
                  <div className="w-full bg-accent-white/20 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-sauce-red to-cheese-gold h-2 rounded-full transition-all duration-500"
                      style={{ width: `${(stage.userCount / maxUsers) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

interface EngagementHeatmapProps {
  data: {
    day: string;
    hour: number;
    engagement: number;
    userCount: number;
  }[];
}

export const EngagementHeatmap: React.FC<EngagementHeatmapProps> = ({ data }) => {
  const maxEngagement = Math.max(...data.map(d => d.engagement));
  const hours = Array.from({ length: 24 }, (_, i) => i);
  const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

  const getEngagementColor = (engagement: number) => {
    const intensity = engagement / maxEngagement;
    if (intensity > 0.8) return 'bg-green-500';
    if (intensity > 0.6) return 'bg-yellow-500';
    if (intensity > 0.4) return 'bg-orange-500';
    if (intensity > 0.2) return 'bg-red-500';
    return 'bg-gray-500';
  };

  return (
    <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
      <CardHeader>
        <CardTitle className="text-xl font-bold text-accent-white flex items-center gap-2">
          <Activity className="h-6 w-6 text-sauce-red" />
          Engagement Heatmap
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="grid grid-cols-25 gap-1 text-xs text-accent-white/60">
            <div></div>
            {hours.map(hour => (
              <div key={hour} className="text-center">
                {hour % 6 === 0 ? hour : ''}
              </div>
            ))}
          </div>
          
          {days.map(day => (
            <div key={day} className="grid grid-cols-25 gap-1">
              <div className="text-xs text-accent-white/60 flex items-center">
                {day}
              </div>
              {hours.map(hour => {
                const dataPoint = data.find(d => d.day === day && d.hour === hour);
                const engagement = dataPoint?.engagement || 0;
                
                return (
                  <div
                    key={`${day}-${hour}`}
                    className={`w-3 h-3 rounded-sm ${getEngagementColor(engagement)} opacity-80 hover:opacity-100 transition-opacity cursor-pointer`}
                    title={`${day} ${hour}:00 - ${engagement.toFixed(1)}% engagement (${dataPoint?.userCount || 0} users)`}
                  />
                );
              })}
            </div>
          ))}
        </div>
        
        <div className="flex items-center justify-between mt-4 text-xs text-accent-white/60">
          <span>Less engaged</span>
          <div className="flex gap-1">
            <div className="w-3 h-3 bg-gray-500 rounded-sm" />
            <div className="w-3 h-3 bg-red-500 rounded-sm" />
            <div className="w-3 h-3 bg-orange-500 rounded-sm" />
            <div className="w-3 h-3 bg-yellow-500 rounded-sm" />
            <div className="w-3 h-3 bg-green-500 rounded-sm" />
          </div>
          <span>More engaged</span>
        </div>
      </CardContent>
    </Card>
  );
};

interface ProgressVelocityChartProps {
  users: {
    id: string;
    name: string;
    track: string;
    stageProgression: {
      stage: number;
      date: string;
      daysToReach: number;
    }[];
  }[];
}

export const ProgressVelocityChart: React.FC<ProgressVelocityChartProps> = ({ users }) => {
  const getTrackColor = (track: string) => {
    switch (track) {
      case 'newbie': return 'text-green-400 border-green-400';
      case 'builder': return 'text-blue-400 border-blue-400';
      case 'scaler': return 'text-purple-400 border-purple-400';
      default: return 'text-gray-400 border-gray-400';
    }
  };

  const maxDays = Math.max(...users.flatMap(u => u.stageProgression.map(s => s.daysToReach)));

  return (
    <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
      <CardHeader>
        <CardTitle className="text-xl font-bold text-accent-white flex items-center gap-2">
          <TrendingUp className="h-6 w-6 text-sauce-red" />
          Progress Velocity
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {users.slice(0, 10).map((user) => (
            <div key={user.id} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-accent-white">{user.name}</span>
                  <Badge className={`text-xs ${getTrackColor(user.track)}`}>
                    {user.track}
                  </Badge>
                </div>
                <span className="text-xs text-accent-white/60">
                  Stage {user.stageProgression[user.stageProgression.length - 1]?.stage || 0}
                </span>
              </div>
              
              <div className="flex items-center gap-1">
                {user.stageProgression.map((stage, index) => (
                  <div key={stage.stage} className="flex items-center">
                    <div
                      className="h-2 bg-gradient-to-r from-sauce-red to-cheese-gold rounded transition-all duration-300"
                      style={{ 
                        width: `${Math.max(10, (stage.daysToReach / maxDays) * 100)}px`,
                        opacity: 0.8 + (index * 0.1)
                      }}
                      title={`Stage ${stage.stage}: ${stage.daysToReach} days`}
                    />
                    {index < user.stageProgression.length - 1 && (
                      <div className="w-1 h-1 bg-accent-white/40 rounded-full mx-1" />
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

interface RiskIndicatorProps {
  riskFactors: {
    factor: string;
    severity: 'low' | 'medium' | 'high';
    userCount: number;
    description: string;
  }[];
}

export const RiskIndicator: React.FC<RiskIndicatorProps> = ({ riskFactors }) => {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'text-red-400 bg-red-500/20 border-red-500/30';
      case 'medium': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      case 'low': return 'text-green-400 bg-green-500/20 border-green-500/30';
      default: return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high': return <AlertTriangle className="h-4 w-4" />;
      case 'medium': return <Clock className="h-4 w-4" />;
      case 'low': return <CheckCircle className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  return (
    <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
      <CardHeader>
        <CardTitle className="text-xl font-bold text-accent-white flex items-center gap-2">
          <AlertTriangle className="h-6 w-6 text-sauce-red" />
          Risk Factors
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {riskFactors.map((risk, index) => (
            <motion.div
              key={risk.factor}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`p-3 rounded-lg border ${getSeverityColor(risk.severity)}`}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  {getSeverityIcon(risk.severity)}
                  <span className="font-medium">{risk.factor}</span>
                </div>
                <Badge variant="outline" className="text-xs">
                  {risk.userCount} users
                </Badge>
              </div>
              <p className="text-sm opacity-80">{risk.description}</p>
            </motion.div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default {
  UserJourneyTimeline,
  EngagementHeatmap,
  ProgressVelocityChart,
  RiskIndicator
};
