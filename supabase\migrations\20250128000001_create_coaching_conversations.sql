-- Create coaching_conversations table for storing AI coaching session data
CREATE TABLE IF NOT EXISTS coaching_conversations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  milestone_id TEXT NOT NULL, -- References milestone_submissions.id
  conversation_id TEXT NOT NULL UNIQUE,
  conversation_data JSONB NOT NULL, -- Stores the full conversation state
  final_confidence INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_coaching_conversations_user_id ON coaching_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_coaching_conversations_milestone_id ON coaching_conversations(milestone_id);
CREATE INDEX IF NOT EXISTS idx_coaching_conversations_conversation_id ON coaching_conversations(conversation_id);
CREATE INDEX IF NOT EXISTS idx_coaching_conversations_created_at ON coaching_conversations(created_at);

-- Enable RLS
ALTER TABLE coaching_conversations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own coaching conversations" ON coaching_conversations
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own coaching conversations" ON coaching_conversations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own coaching conversations" ON coaching_conversations
  FOR UPDATE USING (auth.uid() = user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_coaching_conversations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER update_coaching_conversations_updated_at
  BEFORE UPDATE ON coaching_conversations
  FOR EACH ROW
  EXECUTE FUNCTION update_coaching_conversations_updated_at();

-- Grant permissions
GRANT ALL ON coaching_conversations TO authenticated;
GRANT USAGE ON SEQUENCE coaching_conversations_id_seq TO authenticated;
