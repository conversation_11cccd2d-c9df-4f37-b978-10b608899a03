import React from 'react';
import { Check, X, Zap, <PERSON>rk<PERSON>, Star } from 'lucide-react';
import { motion } from 'framer-motion';
import { useScrollAnimation, scrollVariants, staggerContainer } from '@/hooks/useScrollAnimation';
import { useIsMobile } from '@/hooks/use-mobile';

const comparisons = [
  {
    category: 'Focus',
    is: '10-week builder sprint for next-gen founders',
    isnt: 'Another hackathon or pitch contest',
    icon: Zap,
    color: 'from-blue-400 to-purple-500'
  },
  {
    category: 'Purpose',
    is: 'Launchpad for shipping real products & forming teams',
    isnt: 'Cash grab or startup lottery',
    icon: Star,
    color: 'from-green-400 to-blue-500'
  },
  {
    category: 'Funding',
    is: 'Small grants available on needs basis to cover essential development costs',
    isnt: 'Traditional fund or guaranteed investment',
    icon: Sparkles,
    color: 'from-yellow-400 to-orange-500'
  },
  {
    category: 'Value',
    is: 'Full "Pie Fi Stack" including mentorship, workspace, legal, credits',
    isnt: 'Just writing checks; another accelerator clone',
    icon: Zap,
    color: 'from-purple-400 to-pink-500'
  },
  {
    category: 'Culture',
    is: 'Public building, squad energy, peer learning',
    isnt: 'Solo, secretive, or zero-sum',
    icon: Star,
    color: 'from-teal-400 to-blue-500'
  },
  {
    category: 'Outcome',
    is: 'Tangible products, career-launching, demo day',
    isnt: '"Participation trophy" because this is about shipping',
    icon: Sparkles,
    color: 'from-rose-400 to-red-500'
  }
];

const WhatPieFireIs = () => {
  const { ref, isInView } = useScrollAnimation();
  const isMobile = useIsMobile();

  return (
    <section className="relative py-16 sm:py-24 lg:py-32 overflow-hidden bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black" ref={ref}>
      {/* Animated background particles - reduced on mobile */}
      <div className="absolute inset-0">
        {(isMobile ? [...Array(15)] : [...Array(30)]).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={!isMobile ? {
              y: [-20, 20, -20],
              opacity: [0.2, 0.8, 0.2],
              scale: [0.5, 1.5, 0.5],
            } : undefined}
            transition={!isMobile ? {
              duration: 4 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            } : undefined}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={scrollVariants}
          className="text-center mb-8 sm:mb-12 lg:mb-20"
        >
          <motion.div 
            className="inline-flex items-center gap-2 px-4 py-2 sm:px-6 sm:py-3 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 rounded-full border border-sauce-red/20 mb-4 sm:mb-6"
            whileHover={!isMobile ? { scale: 1.05 } : undefined}
          >
            <Sparkles className="w-3 h-3 sm:w-4 sm:h-4 text-sauce-red" />
            <span className="text-sauce-red font-semibold text-sm sm:text-base">Clear Expectations</span>
          </motion.div>
          
          <h2 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-black text-accent-white mb-3 sm:mb-4 lg:mb-6 px-2">
            What Pie Fi{" "}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-pink-500">
              Is
            </span>
            {" "}/ {" "}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-cheese-gold to-orange-500">
              Isn't
            </span>
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-crust-beige/80 max-w-3xl mx-auto px-4">
            Crystal clear expectations, no surprises
          </p>
        </motion.div>
        
        <motion.div 
          className="grid gap-4 sm:gap-6 lg:gap-8 max-w-6xl mx-auto"
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={staggerContainer}
        >
          {comparisons.map((comparison, index) => (
            <motion.div 
              key={index} 
              className="group relative"
              variants={scrollVariants}
              whileHover={!isMobile ? { scale: 1.02, y: -5 } : undefined}
              transition={{ type: "spring", stiffness: 300 }}
            >
              {/* Glow effect - desktop only */}
              {!isMobile && (
                <div className={`absolute -inset-1 bg-gradient-to-r ${comparison.color} rounded-2xl sm:rounded-3xl blur-xl opacity-0 group-hover:opacity-60 transition-opacity duration-500`} />
              )}
              
              {/* Main card */}
              <div className="relative bg-accent-white/10 backdrop-blur-md rounded-2xl sm:rounded-3xl border border-accent-white/20 overflow-hidden">
                <div className="grid md:grid-cols-12 gap-0">
                  {/* Category */}
                  <div className="md:col-span-3 bg-gradient-to-br from-accent-white/10 to-transparent p-4 sm:p-6 lg:p-8 flex flex-col items-center justify-center border-b md:border-b-0 md:border-r border-accent-white/20">
                    <motion.div 
                      className={`p-2 sm:p-3 lg:p-4 rounded-xl sm:rounded-2xl bg-gradient-to-r ${comparison.color} shadow-lg group-hover:shadow-xl transition-shadow duration-300 mb-2 sm:mb-3 lg:mb-4`}
                      whileHover={!isMobile ? { rotate: 360, scale: 1.1 } : undefined}
                      transition={{ duration: 0.6 }}
                    >
                      <comparison.icon className="h-5 w-5 sm:h-6 sm:w-6 lg:h-8 lg:w-8 text-white" />
                    </motion.div>
                    <h3 className="text-lg sm:text-xl font-bold text-accent-white text-center">{comparison.category}</h3>
                  </div>
                  
                  {/* IS */}
                  <div className="md:col-span-4 p-4 sm:p-6 lg:p-8 border-b md:border-b-0 md:border-r border-accent-white/20">
                    <div className="flex items-start space-x-3 sm:space-x-4 h-full">
                      <motion.div 
                        className="p-2 sm:p-3 bg-green-500/20 rounded-xl sm:rounded-2xl flex-shrink-0 mt-1"
                        whileHover={!isMobile ? { scale: 1.1, rotate: 5 } : undefined}
                      >
                        <Check className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 text-green-400" />
                      </motion.div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-bold text-green-400 mb-2 sm:mb-3 text-xs sm:text-sm uppercase tracking-wider">What It IS</h4>
                        <p className="text-accent-white/90 leading-relaxed text-sm sm:text-base">{comparison.is}</p>
                      </div>
                    </div>
                  </div>
                  
                  {/* ISN'T */}
                  <div className="md:col-span-5 p-4 sm:p-6 lg:p-8">
                    <div className="flex items-start space-x-3 sm:space-x-4 h-full">
                      <motion.div 
                        className="p-2 sm:p-3 bg-red-500/20 rounded-xl sm:rounded-2xl flex-shrink-0 mt-1"
                        whileHover={!isMobile ? { scale: 1.1, rotate: -5 } : undefined}
                      >
                        <X className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 text-red-400" />
                      </motion.div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-bold text-red-400 mb-2 sm:mb-3 text-xs sm:text-sm uppercase tracking-wider">What It ISN'T</h4>
                        <p className="text-crust-beige/80 leading-relaxed text-sm sm:text-base">{comparison.isnt}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        <motion.div 
          className="mt-8 sm:mt-12 lg:mt-20 relative"
          initial={{ opacity: 0, y: 50, scale: 0.9 }}
          animate={isInView ? { opacity: 1, y: 0, scale: 1 } : { opacity: 0, y: 50, scale: 0.9 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <div className="relative bg-gradient-to-r from-oven-black/80 via-purple-900/50 to-oven-black/80 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-12 border border-sauce-red/30 shadow-2xl overflow-hidden">
            {/* Background animation - desktop only */}
            {!isMobile && (
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10"
                animate={{
                  opacity: [0.5, 0.8, 0.5],
                  scale: [1, 1.02, 1]
                }}
                transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
              />
            )}
            
            <div className="relative z-10 text-center">
              <motion.h3 
                className="text-2xl sm:text-3xl lg:text-4xl font-bold text-accent-white mb-4 sm:mb-6"
                whileHover={!isMobile ? { scale: 1.05 } : undefined}
              >
                The Bottom Line
              </motion.h3>
              <motion.p 
                className="text-xl sm:text-2xl lg:text-3xl font-black text-transparent bg-clip-text bg-gradient-to-r from-sauce-red via-cheese-gold to-sauce-red px-2"
                whileHover={!isMobile ? { scale: 1.02 } : undefined}
              >
                10 products. 10 weeks. 1 Demo Day.
              </motion.p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default WhatPieFireIs;
