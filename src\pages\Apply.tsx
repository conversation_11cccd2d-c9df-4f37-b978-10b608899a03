import React, { useState } from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Send, Users, Target, Lightbulb, CheckCircle, AlertCircle, Clock, Sparkles, BookOpen, ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';
import { useScrollAnimation, scrollVariants, staggerContainer } from '@/hooks/useScrollAnimation';
import ApplicationFormComponent from '@/components/shared/ApplicationFormComponent';

const Apply = () => {
  const { ref: heroRef, isInView: heroInView } = useScrollAnimation();
  const { ref: formRef, isInView: formInView } = useScrollAnimation({
    amount: 0.05,  // More lenient threshold for form section
    margin: "100px 0px -100px 0px"  // Start earlier, keep visible longer
  });
  const { ref: processRef, isInView: processInView } = useScrollAnimation();
  const [isSubmitted, setIsSubmitted] = useState(false);

  const applicationProcess = [
    {
      step: '1',
      title: 'Submit Application',
      description: 'Complete the application form with your details and project ideas.',
      icon: Send,
      color: 'from-blue-400 to-blue-600'
    },
    {
      step: '2',
      title: 'Initial Review',
      description: 'Our team reviews applications and selects candidates for the program.',
      icon: Target,
      color: 'from-green-400 to-green-600'
    },
    {
      step: '3',
      title: 'Final Selection',
      description: 'We select the final 10 teams and begin team formation.',
      icon: CheckCircle,
      color: 'from-red-400 to-red-600'
    }
  ];

  const requirements = [
    'Open to all motivated builders who want to create impact',
    'Commitment to the full 10-week program',
    'Willingness to work collaboratively and in public',
    'Bay Area based and able to attend Santa Cruz events'
  ];

  if (isSubmitted) {
    return (
      <div className="bg-oven-black text-accent-white min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow flex items-center justify-center">
          <motion.div 
            className="text-center max-w-2xl mx-auto px-4"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <motion.div
              className="w-24 h-24 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-8"
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <CheckCircle className="h-12 w-12 text-white" />
            </motion.div>
            
            <h1 className="text-4xl font-bold mb-6">Application Submitted!</h1>
            <p className="text-xl text-crust-beige/80 mb-8">
              Thank you for applying to Pie Fi. We'll review your application and get back to you within 2 weeks.
            </p>
            
            <Button 
              onClick={() => window.location.href = '/'}
              className="bg-gradient-to-r from-sauce-red to-sauce-red/90 hover:from-sauce-red/90 hover:to-sauce-red text-accent-white font-bold px-8 py-4 rounded-2xl"
            >
              Back to Home
            </Button>
          </motion.div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="bg-oven-black text-accent-white min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black" ref={heroRef}>
          {/* Animated background */}
          <div className="absolute inset-0">
            {[...Array(30)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [-30, 30, -30],
                  x: [-20, 20, -20],
                  opacity: [0.2, 0.8, 0.2],
                  scale: [0.5, 1.2, 0.5],
                }}
                transition={{
                  duration: 6 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2,
                }}
              />
            ))}
          </div>

          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div 
                className="inline-flex items-center gap-2 px-4 py-2 sm:px-6 sm:py-3 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 rounded-full border border-sauce-red/20 mb-6 sm:mb-8"
                whileHover={{ scale: 1.05 }}
              >
                <Sparkles className="w-3 h-3 sm:w-4 sm:h-4 text-sauce-red" />
                <span className="text-sauce-red font-semibold text-sm sm:text-base">Ready to Build?</span>
              </motion.div>
              
              <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black mb-6 sm:mb-8 px-2">
                Apply to{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                  Pie Fi
                </span>
              </h1>
              
              <p className="text-lg sm:text-xl lg:text-2xl text-crust-beige/90 max-w-4xl mx-auto mb-8 sm:mb-12 px-4 leading-relaxed">
                Join Santa Cruz's most intensive startup accelerator. 10 weeks to build, ship, and demo your product.
              </p>
              
              <motion.a
                href="/ten-in-ten"
                className="inline-flex items-center gap-2 sm:gap-3 px-6 py-3 sm:px-8 sm:py-4 bg-gradient-to-r from-cheese-gold/20 to-sauce-red/20 backdrop-blur-sm text-accent-white font-bold text-base sm:text-lg rounded-xl sm:rounded-2xl border border-cheese-gold/30 hover:border-cheese-gold/50 transition-all duration-300"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <BookOpen className="w-4 h-4 sm:w-5 sm:h-5" />
                Learn More About the Program
                <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
              </motion.a>
            </motion.div>
          </div>
        </section>

        {/* Application Process */}
        <section className="relative py-32 overflow-visible bg-gradient-to-br from-crust-beige via-accent-white to-crust-beige" ref={processRef}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              animate="visible"
              variants={scrollVariants}
              className="text-center mb-20"
            >
              <h2 className="text-5xl font-black text-oven-black mb-6">
                Application{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                  Process
                </span>
              </h2>
              <p className="text-xl text-oven-black/70 max-w-3xl mx-auto">
                Three simple steps to join the Pie Fi community
              </p>
            </motion.div>

            <motion.div 
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
              variants={staggerContainer}
              initial="hidden"
              animate="visible"
            >
              {applicationProcess.map((step, index) => (
                <motion.div 
                  key={step.step}
                  className="group relative"
                  variants={scrollVariants}
                  whileHover={{ y: -10, scale: 1.05 }}
                >
                  {/* Glow effect */}
                  <div className={`absolute -inset-1 bg-gradient-to-r ${step.color} rounded-3xl blur-xl opacity-0 group-hover:opacity-40 transition-opacity duration-500`} />
                  
                  {/* Main card */}
                  <div className="relative bg-accent-white/90 backdrop-blur-md rounded-3xl p-8 border border-oven-black/10 shadow-xl text-center h-full">
                    <motion.div 
                      className={`w-16 h-16 bg-gradient-to-r ${step.color} rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg`}
                      whileHover={{ rotate: 360, scale: 1.1 }}
                      transition={{ duration: 0.6 }}
                    >
                      <span className="text-white font-bold text-xl">{step.step}</span>
                    </motion.div>
                    
                    <h3 className="text-xl font-bold text-oven-black mb-4">{step.title}</h3>
                    <p className="text-oven-black/70 leading-relaxed">{step.description}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>

            {/* Requirements */}
            <motion.div 
              className="bg-gradient-to-r from-sauce-red/10 via-cheese-gold/10 to-sauce-red/10 backdrop-blur-md border border-sauce-red/20 rounded-3xl p-8"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
            >
              <div className="flex items-center gap-3 mb-6">
                <AlertCircle className="h-6 w-6 text-sauce-red" />
                <h3 className="text-2xl font-bold text-oven-black">Requirements</h3>
              </div>
              
              <div className="grid md:grid-cols-2 gap-4">
                {requirements.map((req, index) => (
                  <motion.div 
                    key={index}
                    className="flex items-center space-x-3"
                    whileHover={{ x: 5 }}
                  >
                    <div className="w-2 h-2 bg-sauce-red rounded-full" />
                    <span className="text-oven-black/80">{req}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </section>

        {/* Application Form */}
        <section id="application-form" className="relative py-32 overflow-visible bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black" ref={formRef}>
          <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              animate="visible"
              variants={scrollVariants}
              className="text-center mb-16"
            >
              <h2 className="text-5xl sm:text-6xl font-black text-accent-white mb-6">
                Submit Your{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                  Application
                </span>
              </h2>
              <p className="text-xl text-crust-beige/80 max-w-3xl mx-auto">
                Tell us about yourself and your builder journey
              </p>
            </motion.div>

            <div className="relative">
              <ApplicationFormComponent 
                onSuccess={() => setIsSubmitted(true)}
              />
            </div>


          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Apply;
