// Activity Tracking Service for comprehensive user activity monitoring
import { supabase } from '@/integrations/supabase/client';
import { supabaseAdmin } from '@/integrations/supabase/admin-client';

// Activity type definitions matching database schema
export type ActivityType = 
  // Authentication
  | 'login' | 'logout' | 'signup'
  // Navigation
  | 'page_visit' | 'tab_change' | 'route_change'
  // Onboarding
  | 'onboarding_start' | 'onboarding_step' | 'onboarding_complete'
  // Dev Journey
  | 'milestone_view' | 'milestone_submit' | 'stage_progress'
  // AI Interaction
  | 'ai_analysis_request' | 'ai_insight_view' | 'refresh_analysis'
  // Profile
  | 'profile_update' | 'profile_view'
  // Daily Updates
  | 'daily_update_submit' | 'daily_update_view'
  // Help
  | 'help_request' | 'help_view'
  // Admin
  | 'admin_login' | 'admin_action';

export interface ActivityData {
  // Core activity information
  action?: string;
  target?: string;
  value?: any;
  
  // Context information
  component?: string;
  feature?: string;
  
  // User journey context
  stage_id?: number;
  milestone_id?: string;
  track?: string;
  
  // Technical context
  duration_ms?: number;
  error?: string;
  success?: boolean;
  
  // Additional metadata
  [key: string]: any;
}

export interface ActivityLogEntry {
  id?: string;
  user_id: string;
  activity_type: ActivityType;
  activity_data: ActivityData;
  session_id?: string;
  ip_address?: string;
  user_agent?: string;
  page_url?: string;
  referrer?: string;
  created_at?: string;
  metadata?: Record<string, any>;
}

export interface ActivityTypeDefinition {
  id: string;
  activity_type: ActivityType;
  display_name: string;
  description: string;
  category: string;
  color_code: string;
  icon_name: string;
  is_active: boolean;
}

class ActivityTrackingService {
  private sessionId: string;
  private isEnabled: boolean = true;
  private batchQueue: ActivityLogEntry[] = [];
  private batchTimeout: NodeJS.Timeout | null = null;
  private readonly BATCH_SIZE = 10;
  private readonly BATCH_DELAY = 2000; // 2 seconds

  constructor() {
    this.sessionId = this.generateSessionId();
    this.initializeService();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private initializeService(): void {
    // Listen for page unload to flush any remaining activities
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.flushBatch();
      });

      // Listen for visibility changes to track session activity
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
          this.trackActivity('page_visit', {
            action: 'page_focus',
            page_url: window.location.href
          });
        }
      });
    }
  }

  /**
   * Main method to track user activities
   */
  async trackActivity(
    activityType: ActivityType,
    activityData: ActivityData = {},
    userId?: string
  ): Promise<void> {
    if (!this.isEnabled) return;

    try {
      // Get current user if not provided
      const currentUserId = userId || await this.getCurrentUserId();
      if (!currentUserId) {
        console.warn('ActivityTrackingService: No user ID available for activity tracking');
        return;
      }

      const activityEntry: ActivityLogEntry = {
        user_id: currentUserId,
        activity_type: activityType,
        activity_data: {
          ...activityData,
          timestamp: new Date().toISOString()
        },
        session_id: this.sessionId,
        page_url: typeof window !== 'undefined' ? window.location.href : undefined,
        referrer: typeof window !== 'undefined' ? document.referrer : undefined,
        user_agent: typeof window !== 'undefined' ? navigator.userAgent : undefined,
        metadata: {
          client_timestamp: Date.now(),
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        }
      };

      // Add to batch queue for efficient processing
      this.batchQueue.push(activityEntry);

      // Process batch if it reaches the size limit
      if (this.batchQueue.length >= this.BATCH_SIZE) {
        await this.flushBatch();
      } else {
        // Set timeout to process batch after delay
        this.scheduleBatchFlush();
      }

    } catch (error) {
      console.error('ActivityTrackingService: Error tracking activity:', error);
      // Don't throw error to avoid breaking user experience
    }
  }

  /**
   * Batch processing for efficient database writes
   */
  private async flushBatch(): Promise<void> {
    if (this.batchQueue.length === 0) return;

    const batch = [...this.batchQueue];
    this.batchQueue = [];

    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.batchTimeout = null;
    }

    try {
      // Use admin client for reliable writes
      const { error } = await supabaseAdmin
        .from('user_activity_logs')
        .insert(batch);

      if (error) {
        console.error('ActivityTrackingService: Error inserting activity batch:', error);
        // Re-queue failed activities for retry
        this.batchQueue.unshift(...batch);
      }
    } catch (error) {
      console.error('ActivityTrackingService: Error flushing activity batch:', error);
    }
  }

  private scheduleBatchFlush(): void {
    if (this.batchTimeout) return;

    this.batchTimeout = setTimeout(async () => {
      await this.flushBatch();
    }, this.BATCH_DELAY);
  }

  private async getCurrentUserId(): Promise<string | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      return user?.id || null;
    } catch (error) {
      console.error('ActivityTrackingService: Error getting current user:', error);
      return null;
    }
  }

  /**
   * Convenience methods for common activities
   */

  // Authentication activities
  async trackLogin(userId: string, method: string = 'email'): Promise<void> {
    await this.trackActivity('login', {
      action: 'user_login',
      method,
      success: true
    }, userId);
  }

  async trackLogout(userId: string): Promise<void> {
    await this.trackActivity('logout', {
      action: 'user_logout'
    }, userId);
    // Flush any remaining activities before logout
    await this.flushBatch();
  }

  // Navigation activities
  async trackPageVisit(pageName: string, additionalData: ActivityData = {}): Promise<void> {
    await this.trackActivity('page_visit', {
      action: 'page_view',
      target: pageName,
      ...additionalData
    });
  }

  async trackTabChange(fromTab: string, toTab: string): Promise<void> {
    await this.trackActivity('tab_change', {
      action: 'tab_switch',
      from: fromTab,
      to: toTab
    });
  }

  // Onboarding activities
  async trackOnboardingStep(step: string, stepData: any = {}): Promise<void> {
    await this.trackActivity('onboarding_step', {
      action: 'step_completed',
      target: step,
      step_data: stepData
    });
  }

  async trackOnboardingComplete(track: string): Promise<void> {
    await this.trackActivity('onboarding_complete', {
      action: 'onboarding_finished',
      track,
      success: true
    });
  }

  // Dev Journey activities
  async trackMilestoneSubmission(stageId: number, milestoneId: string, content: string): Promise<void> {
    await this.trackActivity('milestone_submit', {
      action: 'milestone_submitted',
      stage_id: stageId,
      milestone_id: milestoneId,
      content_length: content.length
    });
  }

  async trackStageProgress(fromStage: number, toStage: number): Promise<void> {
    await this.trackActivity('stage_progress', {
      action: 'stage_advanced',
      from_stage: fromStage,
      to_stage: toStage
    });
  }

  // AI Interaction activities
  async trackAIAnalysisRequest(analysisType: string, context: any = {}): Promise<void> {
    await this.trackActivity('ai_analysis_request', {
      action: 'ai_analysis_requested',
      analysis_type: analysisType,
      context
    });
  }

  async trackRefreshAnalysis(analysisType: string): Promise<void> {
    await this.trackActivity('refresh_analysis', {
      action: 'analysis_refreshed',
      analysis_type: analysisType
    });
  }

  // Profile activities
  async trackProfileUpdate(updatedFields: string[]): Promise<void> {
    await this.trackActivity('profile_update', {
      action: 'profile_updated',
      updated_fields: updatedFields
    });
  }

  // Daily Update activities
  async trackDailyUpdateSubmit(contentLength: number, hasAIInsights: boolean): Promise<void> {
    await this.trackActivity('daily_update_submit', {
      action: 'daily_update_submitted',
      content_length: contentLength,
      has_ai_insights: hasAIInsights
    });
  }

  // Admin activities
  async trackAdminAction(action: string, target: string, additionalData: ActivityData = {}): Promise<void> {
    await this.trackActivity('admin_action', {
      action,
      target,
      ...additionalData
    });
  }

  /**
   * Service management methods
   */
  
  enable(): void {
    this.isEnabled = true;
  }

  disable(): void {
    this.isEnabled = false;
  }

  async forceFlush(): Promise<void> {
    await this.flushBatch();
  }

  getSessionId(): string {
    return this.sessionId;
  }

  /**
   * Admin methods for retrieving activity data
   */
  
  async getRecentActivities(limit: number = 100): Promise<ActivityLogEntry[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('user_activity_logs')
        .select(`
          *,
          users!inner(full_name, email, track)
        `)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('ActivityTrackingService: Error fetching recent activities:', error);
      return [];
    }
  }

  async getActivityTypeDefinitions(): Promise<ActivityTypeDefinition[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('activity_type_definitions')
        .select('*')
        .eq('is_active', true)
        .order('category', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('ActivityTrackingService: Error fetching activity types:', error);
      return [];
    }
  }
}

// Export singleton instance
export const activityTrackingService = new ActivityTrackingService();
export default activityTrackingService;
