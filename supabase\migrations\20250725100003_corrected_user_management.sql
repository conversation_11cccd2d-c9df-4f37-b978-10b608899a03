-- Corrected User Management Functions Based on Actual Schema
-- This migration creates functions that work with your existing database structure

-- Drop existing functions and views with cascade
DROP FUNCTION IF EXISTS get_users_for_management CASCADE;
DROP FUNCTION IF EXISTS get_user_comprehensive_profile CASCADE;
DROP MATERIALIZED VIEW IF EXISTS user_progress_summary CASCADE;
DROP VIEW IF EXISTS user_cohort_analytics CASCADE;
DROP VIEW IF EXISTS stage_progression_analytics CASCADE;
DROP VIEW IF EXISTS user_progress_analytics CASCADE;

-- 1. Create a simplified user progress view based on actual tables
CREATE OR REPLACE VIEW user_progress_analytics AS
WITH user_stage_metrics AS (
  SELECT 
    user_id,
    stage_id as current_stage,
    confidence_score as current_confidence,
    stage_entered_at as current_stage_entered_at,
    EXTRACT(EPOCH FROM (NOW() - stage_entered_at)) / (24.0 * 3600.0) as days_in_current_stage,
    ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn
  FROM user_dev_journey_stages
),

user_milestone_metrics AS (
  SELECT 
    user_id,
    COUNT(*)::INTEGER as total_milestones_submitted,
    COUNT(CASE WHEN is_approved = true THEN 1 END)::INTEGER as approved_milestones,
    COUNT(DISTINCT stage_id)::INTEGER as stages_with_submissions,
    MAX(submission_date) as last_milestone_date,
    AVG(LENGTH(submission_content))::INTEGER as avg_submission_length
  FROM milestone_submissions
  GROUP BY user_id
),

user_daily_update_metrics AS (
  SELECT 
    user_id,
    COUNT(*)::INTEGER as total_daily_updates,
    MAX(created_at) as last_update_date,
    AVG(sentiment_score) as avg_sentiment,
    AVG(LENGTH(content))::INTEGER as avg_update_length,
    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END)::INTEGER as updates_last_7_days,
    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '30 days' THEN 1 END)::INTEGER as updates_last_30_days
  FROM daily_updates
  GROUP BY user_id
)

SELECT 
  u.id as user_id,
  u.full_name,
  u.email,
  u.track,
  u.onboarding_completed,
  u.created_at as user_created_at,
  
  -- Stage metrics
  COALESCE(usm.current_stage, 0) as current_stage,
  COALESCE(usm.current_confidence, 50) as current_confidence,
  usm.current_stage_entered_at,
  COALESCE(usm.days_in_current_stage, 0) as days_in_current_stage,
  
  -- Milestone metrics
  COALESCE(umm.total_milestones_submitted, 0) as total_milestones_submitted,
  COALESCE(umm.approved_milestones, 0) as approved_milestones,
  COALESCE(umm.stages_with_submissions, 0) as stages_with_submissions,
  umm.last_milestone_date,
  COALESCE(umm.avg_submission_length, 0) as avg_submission_length,
  
  -- Daily update metrics
  COALESCE(udum.total_daily_updates, 0) as total_daily_updates,
  udum.last_update_date,
  COALESCE(udum.avg_sentiment, 0.5) as avg_sentiment,
  COALESCE(udum.avg_update_length, 0) as avg_update_length,
  COALESCE(udum.updates_last_7_days, 0) as updates_last_7_days,
  COALESCE(udum.updates_last_30_days, 0) as updates_last_30_days,
  
  -- Calculated progress indicators
  CASE 
    WHEN u.onboarding_completed AND COALESCE(usm.current_stage, 0) > 0 THEN 'Active'
    WHEN u.onboarding_completed AND COALESCE(usm.current_stage, 0) = 0 THEN 'Onboarded - No Progress'
    WHEN NOT COALESCE(u.onboarding_completed, false) THEN 'Onboarding In Progress'
    ELSE 'Unknown'
  END as progress_status,
  
  -- Engagement score (0-100)
  LEAST(100, GREATEST(0, 
    (COALESCE(udum.updates_last_7_days, 0) * 15) +
    (COALESCE(umm.total_milestones_submitted, 0) * 10) +
    (COALESCE(usm.current_stage, 0) * 15) +
    (CASE WHEN udum.last_update_date >= NOW() - INTERVAL '3 days' THEN 20 ELSE 0 END) +
    (CASE WHEN umm.last_milestone_date >= NOW() - INTERVAL '7 days' THEN 15 ELSE 0 END) +
    (CASE WHEN COALESCE(u.onboarding_completed, false) THEN 25 ELSE 0 END)
  ))::INTEGER as engagement_score,
  
  -- Risk indicators
  CASE 
    WHEN NOT COALESCE(u.onboarding_completed, false) AND u.created_at < NOW() - INTERVAL '3 days' THEN 'High'
    WHEN COALESCE(u.onboarding_completed, false) AND COALESCE(usm.days_in_current_stage, 0) > 14 THEN 'High'
    WHEN COALESCE(udum.updates_last_7_days, 0) = 0 AND udum.last_update_date < NOW() - INTERVAL '7 days' THEN 'Medium'
    WHEN COALESCE(usm.current_confidence, 50) < 30 THEN 'Medium'
    ELSE 'Low'
  END as risk_level,
  
  -- Success indicators
  CASE 
    WHEN COALESCE(usm.current_stage, 0) >= 4 AND COALESCE(umm.approved_milestones, 0) >= 3 THEN 'High Performer'
    WHEN COALESCE(usm.current_stage, 0) >= 2 AND COALESCE(udum.avg_sentiment, 0) > 0.7 THEN 'On Track'
    WHEN COALESCE(u.onboarding_completed, false) AND COALESCE(udum.updates_last_7_days, 0) >= 3 THEN 'Engaged'
    ELSE 'Needs Attention'
  END as success_category

FROM users u
LEFT JOIN user_stage_metrics usm ON u.id = usm.user_id AND usm.rn = 1
LEFT JOIN user_milestone_metrics umm ON u.id = umm.user_id
LEFT JOIN user_daily_update_metrics udum ON u.id = udum.user_id;

-- 2. Create function to get users for management with proper types
CREATE OR REPLACE FUNCTION get_users_for_management(
  limit_count INTEGER DEFAULT 50,
  offset_count INTEGER DEFAULT 0,
  filter_track VARCHAR DEFAULT NULL,
  filter_risk_level VARCHAR DEFAULT NULL,
  filter_success_category VARCHAR DEFAULT NULL,
  search_term VARCHAR DEFAULT NULL
)
RETURNS TABLE (
  user_id UUID,
  full_name VARCHAR,
  email VARCHAR,
  track VARCHAR,
  created_at TIMESTAMP WITH TIME ZONE,
  onboarding_completed BOOLEAN,
  current_stage INTEGER,
  engagement_score INTEGER,
  risk_level TEXT,
  success_category TEXT,
  days_since_last_activity INTEGER,
  last_update_date TIMESTAMP WITH TIME ZONE,
  total_milestones_submitted INTEGER,
  problem_description TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    upa.user_id,
    upa.full_name,
    upa.email,
    upa.track,
    upa.user_created_at as created_at,
    upa.onboarding_completed,
    upa.current_stage,
    upa.engagement_score,
    upa.risk_level,
    upa.success_category,
    COALESCE(
      (EXTRACT(EPOCH FROM (NOW() - GREATEST(upa.last_update_date, upa.last_milestone_date))) / (24 * 3600))::INTEGER,
      (EXTRACT(EPOCH FROM (NOW() - upa.user_created_at)) / (24 * 3600))::INTEGER
    ) as days_since_last_activity,
    upa.last_update_date,
    upa.total_milestones_submitted,
    or_data.problem_description
  FROM user_progress_analytics upa
  LEFT JOIN onboarding_responses or_data ON upa.user_id = or_data.user_id
  WHERE 
    (filter_track IS NULL OR upa.track = filter_track) AND
    (filter_risk_level IS NULL OR upa.risk_level = filter_risk_level) AND
    (filter_success_category IS NULL OR upa.success_category = filter_success_category) AND
    (search_term IS NULL OR 
     upa.full_name ILIKE '%' || search_term || '%' OR 
     upa.email ILIKE '%' || search_term || '%' OR
     or_data.problem_description ILIKE '%' || search_term || '%')
  ORDER BY 
    CASE upa.risk_level WHEN 'High' THEN 1 WHEN 'Medium' THEN 2 ELSE 3 END,
    upa.engagement_score DESC,
    upa.user_created_at DESC
  LIMIT limit_count OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;

-- 3. Create function to get comprehensive user profile with AI insights
CREATE OR REPLACE FUNCTION get_user_comprehensive_profile(target_user_id UUID)
RETURNS TABLE (
  -- Basic user info
  user_id UUID,
  full_name VARCHAR,
  email VARCHAR,
  track VARCHAR,
  created_at TIMESTAMP WITH TIME ZONE,
  onboarding_completed BOOLEAN,

  -- Progress metrics
  current_stage INTEGER,
  current_confidence INTEGER,
  days_in_current_stage NUMERIC,
  engagement_score INTEGER,
  risk_level TEXT,
  success_category TEXT,
  progress_status TEXT,

  -- Onboarding data
  problem_description TEXT,
  solution_approach TEXT,
  target_audience TEXT,
  technical_background CHARACTER VARYING,
  primary_goal CHARACTER VARYING,
  biggest_challenge TEXT,

  -- Activity metrics
  total_daily_updates INTEGER,
  updates_last_7_days INTEGER,
  last_update_date TIMESTAMP WITH TIME ZONE,
  total_milestones_submitted INTEGER,
  approved_milestones INTEGER,
  last_milestone_date TIMESTAMP WITH TIME ZONE,

  -- AI-Generated Comprehensive Profile Analysis
  profile_summary TEXT,
  strengths TEXT[],
  knowledge_gaps TEXT[],
  personality_traits JSONB,
  project_viability_score INTEGER,
  market_opportunity_assessment TEXT,
  technical_feasibility_analysis TEXT,
  recommended_next_steps TEXT[],
  potential_blockers TEXT[],
  optimal_learning_path JSONB,
  mentor_matching_criteria JSONB,
  resource_preferences JSONB,
  communication_strategy JSONB,
  personalized_goals JSONB,
  success_probability_factors JSONB,
  risk_factors JSONB,
  profile_confidence_score INTEGER,

  -- AI Conversation Data
  ai_satisfaction_score INTEGER,
  areas_needing_clarification TEXT[],
  key_insights_extracted JSONB,
  conversation_completed BOOLEAN,
  total_questions_asked INTEGER,

  -- User Memory & Learning Analytics
  total_memory_entries BIGINT,
  interaction_memories BIGINT,
  progress_memories BIGINT,
  insight_memories BIGINT,
  context_memories BIGINT,

  -- Recent activity
  recent_daily_updates JSONB,
  recent_milestones JSONB,
  recent_memory_insights JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    -- Basic user info
    upa.user_id,
    upa.full_name,
    upa.email,
    upa.track,
    upa.user_created_at as created_at,
    upa.onboarding_completed,

    -- Progress metrics
    upa.current_stage,
    upa.current_confidence,
    upa.days_in_current_stage,
    upa.engagement_score,
    upa.risk_level,
    upa.success_category,
    upa.progress_status,

    -- Onboarding data
    or_data.problem_description,
    or_data.solution_approach,
    or_data.target_audience,
    or_data.technical_background,
    or_data.primary_goal,
    or_data.biggest_challenge,

    -- Activity metrics
    upa.total_daily_updates,
    upa.updates_last_7_days,
    upa.last_update_date,
    upa.total_milestones_submitted,
    upa.approved_milestones,
    upa.last_milestone_date,

    -- AI-Generated Comprehensive Profile Analysis
    cup.profile_summary,
    cup.strengths,
    cup.knowledge_gaps,
    cup.personality_traits,
    cup.project_viability_score,
    cup.market_opportunity_assessment,
    cup.technical_feasibility_analysis,
    cup.recommended_next_steps,
    cup.potential_blockers,
    cup.optimal_learning_path,
    cup.mentor_matching_criteria,
    cup.resource_preferences,
    cup.communication_strategy,
    cup.personalized_goals,
    cup.success_probability_factors,
    cup.risk_factors,
    cup.profile_confidence_score,

    -- AI Conversation Data
    afc.ai_satisfaction_score,
    afc.areas_needing_clarification,
    afc.key_insights_extracted,
    afc.conversation_completed,
    afc.total_questions_asked,

    -- User Memory & Learning Analytics
    COALESCE(memory_stats.total_entries, 0) as total_memory_entries,
    COALESCE(memory_stats.interaction_count, 0) as interaction_memories,
    COALESCE(memory_stats.progress_count, 0) as progress_memories,
    COALESCE(memory_stats.insight_count, 0) as insight_memories,
    COALESCE(memory_stats.context_count, 0) as context_memories,

    -- Recent activity (as JSON)
    COALESCE(
      (SELECT jsonb_agg(
        jsonb_build_object(
          'date', du.created_at,
          'content', LEFT(du.content, 200),
          'sentiment_score', du.sentiment_score
        ) ORDER BY du.created_at DESC
      ) FROM daily_updates du WHERE du.user_id = target_user_id LIMIT 5),
      '[]'::jsonb
    ) as recent_daily_updates,

    COALESCE(
      (SELECT jsonb_agg(
        jsonb_build_object(
          'date', ms.submission_date,
          'stage_id', ms.stage_id,
          'milestone_id', ms.milestone_id,
          'is_approved', ms.is_approved,
          'content', LEFT(ms.submission_content, 200)
        ) ORDER BY ms.submission_date DESC
      ) FROM milestone_submissions ms WHERE ms.user_id = target_user_id LIMIT 5),
      '[]'::jsonb
    ) as recent_milestones,

    -- Recent memory insights
    COALESCE(
      (SELECT jsonb_agg(
        jsonb_build_object(
          'date', ume.created_at,
          'type', ume.memory_type,
          'content', ume.content,
          'metadata', ume.metadata
        ) ORDER BY ume.created_at DESC
      ) FROM user_memory_entries ume
       WHERE ume.user_id = target_user_id
       AND ume.memory_type IN ('insight', 'progress')
       LIMIT 10),
      '[]'::jsonb
    ) as recent_memory_insights

  FROM user_progress_analytics upa
  LEFT JOIN onboarding_responses or_data ON upa.user_id = or_data.user_id
  LEFT JOIN comprehensive_user_profiles cup ON upa.user_id = cup.user_id
  LEFT JOIN ai_followup_conversations afc ON upa.user_id = afc.user_id
  LEFT JOIN (
    SELECT
      user_id,
      COUNT(*) as total_entries,
      COUNT(CASE WHEN memory_type = 'interaction' THEN 1 END) as interaction_count,
      COUNT(CASE WHEN memory_type = 'progress' THEN 1 END) as progress_count,
      COUNT(CASE WHEN memory_type = 'insight' THEN 1 END) as insight_count,
      COUNT(CASE WHEN memory_type = 'context' THEN 1 END) as context_count
    FROM user_memory_entries
    GROUP BY user_id
  ) memory_stats ON upa.user_id = memory_stats.user_id
  WHERE upa.user_id = target_user_id;
END;
$$ LANGUAGE plpgsql;

-- 4. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_track ON users USING btree(track);
CREATE INDEX IF NOT EXISTS idx_users_onboarding_completed ON users USING btree(onboarding_completed);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users USING btree(created_at);

CREATE INDEX IF NOT EXISTS idx_user_dev_journey_stages_user_id ON user_dev_journey_stages USING btree(user_id);
CREATE INDEX IF NOT EXISTS idx_user_dev_journey_stages_stage_id ON user_dev_journey_stages USING btree(stage_id);
CREATE INDEX IF NOT EXISTS idx_user_dev_journey_stages_created_at ON user_dev_journey_stages USING btree(created_at);

CREATE INDEX IF NOT EXISTS idx_milestone_submissions_user_id ON milestone_submissions USING btree(user_id);
CREATE INDEX IF NOT EXISTS idx_milestone_submissions_stage_id ON milestone_submissions USING btree(stage_id);
CREATE INDEX IF NOT EXISTS idx_milestone_submissions_submission_date ON milestone_submissions USING btree(submission_date);

CREATE INDEX IF NOT EXISTS idx_daily_updates_user_id ON daily_updates USING btree(user_id);
CREATE INDEX IF NOT EXISTS idx_daily_updates_created_at ON daily_updates USING btree(created_at);

CREATE INDEX IF NOT EXISTS idx_onboarding_responses_user_id ON onboarding_responses USING btree(user_id);

-- 5. Add comments
COMMENT ON VIEW user_progress_analytics IS 'User progress metrics based on actual database schema';
COMMENT ON FUNCTION get_users_for_management IS 'Returns filtered and paginated user list for management interface';
COMMENT ON FUNCTION get_user_comprehensive_profile(UUID) IS 'Returns comprehensive user profile for detailed management view';