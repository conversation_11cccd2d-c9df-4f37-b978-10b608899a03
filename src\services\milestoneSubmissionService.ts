import { supabase } from '@/integrations/supabase/client';
import geminiService from './geminiService';
import userMemoryService from './userMemoryService';
import milestoneDefinitionService from './milestoneDefinitionService';
import stageProgressService from './stageProgressService';
import { toast } from 'sonner';

export interface MilestoneSubmissionData {
  userId: string;
  stageId: number;
  milestoneId: string;
  content: string;
  stageTitle?: string;
  milestoneTitle?: string;
  milestoneDescription?: string;
  projectContext?: string;
}

export interface MilestoneSubmissionResult {
  success: boolean;
  submissionId?: string;
  aiAnalysis?: any;
  submissionData?: any;
  error?: string;
}

class MilestoneSubmissionService {
  /**
   * Unified milestone submission with AI analysis and error handling
   */
  async submitMilestone(data: MilestoneSubmissionData): Promise<MilestoneSubmissionResult> {
    const {
      userId,
      stageId,
      milestoneId,
      content,
      stageTitle = `Stage ${stageId}`,
      milestoneTitle = milestoneId,
      milestoneDescription = `${stageTitle} milestone: ${milestoneId}`,
      projectContext = ''
    } = data;

    if (!content?.trim()) {
      return {
        success: false,
        error: 'Please provide content for your milestone submission'
      };
    }

    try {
      console.log(`📝 Submitting milestone: ${milestoneId} for stage ${stageId}`);

      // Use UPSERT to handle both new submissions and updates
      const { data: submissionData, error: submissionError } = await supabase
        .from('milestone_submissions')
        .upsert({
          user_id: userId,
          stage_id: stageId,
          milestone_id: milestoneId,
          submission_content: content,
          submission_date: new Date().toISOString(),
          is_approved: false // Reset approval status on resubmission
        }, {
          onConflict: 'user_id,stage_id,milestone_id'
        })
        .select()
        .single();

      if (submissionError) {
        throw submissionError;
      }

      console.log(`✅ Milestone submitted successfully: ${submissionData.id}`);

      // Trigger AI analysis of the submission
      let aiAnalysis = null;
      try {
        console.log('🤖 Analyzing milestone submission with AI...');
        
        // Get user context for analysis
        const userContext = await userMemoryService.buildUserContext(userId);

        // Get detailed milestone definition and context
        const milestoneContext = milestoneDefinitionService.getMilestoneContext(milestoneId);
        const enhancedDescription = milestoneContext.definition
          ? `${milestoneContext.definition.description}\n\n${milestoneContext.contextPrompt}`
          : milestoneDescription;

        // Analyze the submission with AI using enhanced context
        aiAnalysis = await geminiService.analyzeMilestoneSubmission(
          content,
          {
            milestoneTitle: milestoneContext.definition?.title || milestoneTitle,
            milestoneDescription: enhancedDescription,
            stageId,
            stageTitle,
            userContext,
            projectContext,
            milestoneDefinition: milestoneContext.definition
          }
        );

        if (aiAnalysis) {
          // Determine if milestone should be auto-approved based on AI confidence
          const shouldAutoApprove = aiAnalysis.confidence >= 85 || aiAnalysis.finalConfidence >= 85;

          // Update the submission with AI feedback and approval status
          await supabase
            .from('milestone_submissions')
            .update({
              ai_feedback: aiAnalysis.consolidatedFeedback || aiAnalysis.coachingInsight || aiAnalysis.insights,
              ai_insights: JSON.stringify({
                followUpQuestions: aiAnalysis.followUpQuestions || [],
                keyLearnings: aiAnalysis.keyLearnings || [],
                nextSteps: aiAnalysis.nextSteps || [],
                finalConfidence: aiAnalysis.finalConfidence || aiAnalysis.confidence || 75
              }),
              is_approved: shouldAutoApprove,
              completed: shouldAutoApprove,
              approval_date: shouldAutoApprove ? new Date().toISOString() : null
            })
            .eq('id', submissionData.id);

          console.log(`${shouldAutoApprove ? '✅ Auto-approved' : '⏳ Pending review'} milestone ${milestoneId} with ${aiAnalysis.confidence || aiAnalysis.finalConfidence}% confidence`);

          // Store AI insights in user memory
          await userMemoryService.storeInsight(
            userId,
            aiAnalysis.insights || aiAnalysis.coachingInsight || 'Milestone completed successfully',
            'milestone_analysis',
            aiAnalysis.confidence || 75
          );

          // Store progress in comprehensive profile
          await userMemoryService.storeProgress(
            userId,
            'milestone_completion',
            {
              milestone_id: milestoneId,
              milestone_title: milestoneTitle,
              user_submission: content,
              ai_analysis: aiAnalysis,
              stage_context: {
                stage_id: stageId,
                stage_title: stageTitle
              }
            },
            stageId
          );

          console.log('✅ Milestone submission analyzed and feedback stored');

          // Recalculate stage progress after milestone completion
          if (shouldAutoApprove) {
            try {
              await stageProgressService.getCurrentStageProgress(userId, stageId);
              console.log('📊 Stage progress recalculated after milestone completion');
            } catch (progressError) {
              console.error('Error recalculating stage progress:', progressError);
            }
          }
        }
      } catch (aiError) {
        console.error('Error analyzing milestone submission:', aiError);
        // Continue even if AI analysis fails - don't fail the entire submission
      }

      return {
        success: true,
        submissionId: submissionData.id,
        aiAnalysis,
        submissionData: submissionData // Include the full submission data for immediate UI updates
      };

    } catch (error) {
      console.error('Error submitting milestone:', error);
      
      // Provide specific error messages based on error type
      let errorMessage = 'An unexpected error occurred. Please try again.';
      
      if (error instanceof Error) {
        if (error.message.includes('duplicate key')) {
          errorMessage = 'Milestone already exists. Please try refreshing the page.';
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else if (error.message.includes('unauthorized') || error.message.includes('permission')) {
          errorMessage = 'You do not have permission to submit this milestone.';
        } else {
          errorMessage = `Failed to submit milestone: ${error.message}`;
        }
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Fetch milestone submissions for a user and stage
   */
  async getMilestoneSubmissions(userId: string, stageId: number) {
    try {
      const { data, error } = await supabase
        .from('milestone_submissions')
        .select('*')
        .eq('user_id', userId)
        .eq('stage_id', stageId)
        .order('submission_date', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching milestone submissions:', error);
      return [];
    }
  }

  /**
   * Get a specific milestone submission
   */
  async getMilestoneSubmission(userId: string, stageId: number, milestoneId: string) {
    try {
      const { data, error } = await supabase
        .from('milestone_submissions')
        .select('*')
        .eq('user_id', userId)
        .eq('stage_id', stageId)
        .eq('milestone_id', milestoneId)
        .maybeSingle();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching milestone submission:', error);
      return null;
    }
  }
}

const milestoneSubmissionService = new MilestoneSubmissionService();
export default milestoneSubmissionService;
