import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Pizza, Sparkles, Rocket, ArrowLeft } from 'lucide-react';
import { motion } from 'framer-motion';

const Register = () => {
  const navigate = useNavigate();
  const { signUp } = useAuth();
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [accessCode, setAccessCode] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validate access code
    const requiredAccessCode = import.meta.env.VITE_ACCESS_CODE;
    if (!requiredAccessCode) {
      setError('Access code validation is not configured. Please contact support.');
      return;
    }

    if (accessCode.trim().toUpperCase() !== requiredAccessCode.toUpperCase()) {
      setError('Invalid access code.');
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (password.length < 6) {
      setError('Password should be at least 6 characters');
      return;
    }

    setLoading(true);

    try {
      const { data, error } = await signUp(email, password, fullName);
      
      if (error) {
        setError(error.message);
        return;
      }
      
      if (data?.user) {
        if (data.user.email_confirmed_at) {
          setSuccessMessage('Account created and confirmed! Redirecting to onboarding...');
          setTimeout(() => {
            navigate('/onboarding');
          }, 2000);
        } else {
          setSuccessMessage('Account created successfully! Please check your email to confirm your account before continuing.');
        }
        
        setEmail('');
        setPassword('');
        setConfirmPassword('');
        setFullName('');
        setAccessCode('');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-oven-black relative overflow-hidden flex items-center justify-center">
      {/* Animated Background Elements - matching Hero design */}
      <div className="absolute inset-0">
        <motion.div 
          className="absolute top-1/5 left-1/5 w-[700px] h-[700px] rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 blur-3xl"
          animate={{
            scale: [1, 1.15, 1],
            rotate: [0, -180, -360],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        
        <motion.div 
          className="absolute bottom-1/5 right-1/5 w-[600px] h-[600px] rounded-full bg-gradient-to-r from-sauce-red/15 to-cheese-gold/15 blur-3xl"
          animate={{
            scale: [1.1, 1, 1.1],
            x: [0, -40, 0],
            y: [0, 20, 0],
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Floating geometric shapes */}
        {[...Array(10)].map((_, i) => (
          <motion.div
            key={i}
            className={`absolute w-3 h-3 ${
              i % 4 === 0 ? 'bg-cheese-gold/40' : 
              i % 4 === 1 ? 'bg-sauce-red/40' : 
              i % 4 === 2 ? 'bg-purple-400/40' : 'bg-pink-400/40'
            } rounded-full`}
            style={{
              left: `${15 + (i * 8)}%`,
              top: `${20 + (i * 6)}%`,
            }}
            animate={{
              y: [-25, 25, -25],
              x: [-12, 12, -12],
              opacity: [0.2, 0.8, 0.2],
              scale: [0.8, 1.2, 0.8],
            }}
            transition={{
              duration: 5 + i * 0.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.3,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 w-full max-w-lg px-6">
        <motion.div
          initial={{ opacity: 0, y: 30, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8 }}
        >
          {/* Logo and Title Section */}
          <div className="text-center mb-8">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="mb-6"
            >
              <motion.div
                animate={{ 
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.05, 1]
                }}
                transition={{ duration: 4, repeat: Infinity }}
                className="flex justify-center mb-4"
              >
                <Pizza className="h-16 w-16 text-cheese-gold" />
              </motion.div>
              <h1 className="text-4xl md:text-5xl font-black text-transparent bg-clip-text bg-gradient-to-r from-sauce-red via-cheese-gold to-sauce-red tracking-tight mb-2"
                  style={{ filter: "drop-shadow(0 4px 12px rgba(255, 71, 71, 0.3))" }}>
                Pie Fi
              </h1>
              <motion.p 
                className="text-lg text-accent-white/80"
                animate={{ opacity: [0.7, 1, 0.7] }}
                transition={{ duration: 2.5, repeat: Infinity }}
              >
                Start building your future 🚀
              </motion.p>
            </motion.div>
          </div>

          {/* Register Card */}
          <Card className="bg-gradient-to-br from-accent-white/10 to-accent-white/5 backdrop-blur-sm border border-accent-white/20 shadow-2xl">
            <CardHeader className="space-y-1 pb-6">
              <motion.div
                className="inline-flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500/10 to-pink-500/10 backdrop-blur-sm border border-purple-500/20 rounded-full text-purple-400 font-semibold text-sm mb-4 mx-auto"
              >
                <Sparkles className="w-4 h-4" />
                Join the builder community
                <Sparkles className="w-4 h-4" />
              </motion.div>
              
              <CardTitle className="text-2xl text-center text-accent-white font-bold">
                Create Your Account
              </CardTitle>
              <CardDescription className="text-center text-accent-white/70 text-base">
                Begin your entrepreneurial journey with Pie Fi
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-5">
                <div className="space-y-2">
                  <Label htmlFor="fullName" className="text-accent-white font-medium">
                    Full Name
                  </Label>
                  <Input
                    id="fullName"
                    type="text"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    placeholder="John Doe"
                    required
                    className="h-12 bg-oven-black/50 border-accent-white/30 text-accent-white placeholder:text-accent-white/50 focus:border-cheese-gold/50 focus:ring-cheese-gold/20 text-base"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="accessCode" className="text-accent-white font-medium">
                    Access Code
                  </Label>
                  <Input
                    id="accessCode"
                    type="text"
                    value={accessCode}
                    onChange={(e) => setAccessCode(e.target.value)}
                    placeholder="Enter access code"
                    required
                    className="h-12 bg-oven-black/50 border-accent-white/30 text-accent-white placeholder:text-accent-white/50 focus:border-cheese-gold/50 focus:ring-cheese-gold/20 text-base"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-accent-white font-medium">
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    required
                    className="h-12 bg-oven-black/50 border-accent-white/30 text-accent-white placeholder:text-accent-white/50 focus:border-cheese-gold/50 focus:ring-cheese-gold/20 text-base"
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="password" className="text-accent-white font-medium">
                      Password
                    </Label>
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      className="h-12 bg-oven-black/50 border-accent-white/30 text-accent-white focus:border-cheese-gold/50 focus:ring-cheese-gold/20 text-base"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword" className="text-accent-white font-medium">
                      Confirm Password
                    </Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      required
                      className="h-12 bg-oven-black/50 border-accent-white/30 text-accent-white focus:border-cheese-gold/50 focus:ring-cheese-gold/20 text-base"
                    />
                  </div>
                </div>
                
                {error && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <Alert className="bg-sauce-red/10 border-sauce-red/20 border">
                      <AlertDescription className="text-sauce-red">
                        {error}
                      </AlertDescription>
                    </Alert>
                  </motion.div>
                )}

                {successMessage && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <Alert className="bg-green-500/10 border-green-500/20 border">
                      <AlertDescription className="text-green-400">
                        {successMessage}
                      </AlertDescription>
                    </Alert>
                  </motion.div>
                )}

                <motion.div 
                  whileHover={{ scale: 1.02 }} 
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    type="submit"
                    disabled={loading}
                    className="w-full h-12 bg-gradient-to-r from-sauce-red to-cheese-gold hover:from-sauce-red/90 hover:to-cheese-gold/90 text-white font-semibold text-lg shadow-lg transition-all duration-300"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="mr-3 h-5 w-5 animate-spin" />
                        Creating your account...
                      </>
                    ) : (
                      <>
                        <Rocket className="mr-3 h-5 w-5" />
                        Create Account
                      </>
                    )}
                  </Button>
                </motion.div>
              </form>

              {/* Sign In Link */}
              <div className="mt-8 text-center">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-accent-white/20" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-4 bg-gradient-to-r from-accent-white/5 to-accent-white/10 text-accent-white/60 rounded-full">
                      Already building with us?
                    </span>
                  </div>
                </div>
                
                <motion.div 
                  whileHover={{ scale: 1.05 }}
                  className="mt-4 space-y-2"
                >
                  <Link
                    to="/login"
                    className="inline-flex items-center gap-2 text-cheese-gold hover:text-cheese-gold/80 font-semibold transition-colors group"
                  >
                    <ArrowLeft className="h-4 w-4 group-hover:-translate-x-1 transition-transform" />
                    Sign in to your account
                  </Link>
                  
                  {/* Debug: Add logout option for testing */}
                  <div className="text-center">
                    <button
                      onClick={async () => {
                        const { useAuth } = await import('@/hooks/useAuth');
                        // This is a bit hacky but works for debugging
                        window.location.href = '/login?logout=true';
                      }}
                      className="text-xs text-accent-white/40 hover:text-accent-white/60 transition-colors"
                    >
                      Already logged in? Click to logout and create new account
                    </button>
                  </div>
                </motion.div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Info */}
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 0.8 }}
            className="mt-6 text-center"
          >
            <p className="text-sm text-accent-white/60 leading-relaxed">
              By creating an account, you're joining Santa Cruz's most ambitious builder community.
              <br />
              Ready to turn your ideas into reality? 🍕
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default Register; 