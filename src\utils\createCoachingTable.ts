import { supabase } from '@/integrations/supabase/client';

/**
 * Utility function to create the coaching_conversations table if it doesn't exist
 * This can be called manually if the migration hasn't been applied yet
 */
export async function createCoachingConversationsTable() {
  try {
    console.log('🔧 Attempting to create coaching_conversations table...');

    // Try to create table using direct SQL query
    const { error } = await supabase
      .from('coaching_conversations')
      .select('id')
      .limit(1);

    // If table doesn't exist, we'll get a specific error
    if (error && error.code === '42P01') {
      console.log('📋 Table does not exist, but we cannot create it via client. Using fallback approach.');
      return false; // Table doesn't exist and we can't create it
    }

    if (error) {
      console.warn('Unknown error checking table:', error);
      return false;
    }

    console.log('✅ coaching_conversations table already exists');
    return true;
  } catch (error) {
    console.error('Failed to check/create coaching_conversations table:', error);
    return false;
  }
}

/**
 * Check if the coaching_conversations table exists
 */
export async function checkCoachingTableExists(): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('coaching_conversations')
      .select('id')
      .limit(1);

    if (error && error.code === '42P01') {
      // Table doesn't exist
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Initialize coaching table if needed
 */
export async function initializeCoachingTable(): Promise<boolean> {
  const exists = await checkCoachingTableExists();
  
  if (!exists) {
    console.log('📋 coaching_conversations table not found, creating...');
    return await createCoachingConversationsTable();
  }
  
  console.log('✅ coaching_conversations table already exists');
  return true;
}
