import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/hooks/useAuth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pizza, Sparkles, Brain, Target, Loader2, CheckCircle, MessageCircle, User } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import geminiService from '@/services/geminiService';
import devJourneyService from '@/services/devJourneyService';
import userMemoryService from '@/services/userMemoryService';
import onboardingToJourneyService from '@/services/onboardingToJourneyService';
import MarkdownText from '@/components/MarkdownText';

// Types for our streamlined onboarding system
interface StructuredResponses {
  track: string;
  problemDescription: string;
  solutionApproach: string;
  targetAudience: string;
  uniqueValueProposition: string;
  technicalBackground: 'beginner' | 'intermediate' | 'expert';
  previousExperience: string;
  technicalSkills: string;
  primaryGoal: 'learn' | 'build_mvp' | 'find_users' | 'scale';
  successMetrics: string;
}

const Onboarding = () => {
  const navigate = useNavigate();
  const { user, updateUserProfile } = useAuth();

  // Phase tracking
  const [currentPhase, setCurrentPhase] = useState<'track-selection' | 'structured' | 'ai-dive' | 'generating'>('track-selection');
  const [phaseProgress, setPhaseProgress] = useState(0);

  // Phase 1: Track Selection & Structured Questions
  const [selectedTrack, setSelectedTrack] = useState<string>('');
  const [structuredResponses, setStructuredResponses] = useState<Partial<StructuredResponses>>({});
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);

  // Phase 2: AI Deep Dive
  const [conversationHistory, setConversationHistory] = useState<Array<{role: 'user' | 'ai', content: string}>>([]);
  const [currentAIResponse, setCurrentAIResponse] = useState('');
  const [aiSatisfactionScore, setAiSatisfactionScore] = useState(0);
  const [areasNeedingClarification, setAreasNeedingClarification] = useState<string[]>([]);

  // Loading states
  const [loading, setLoading] = useState(false);
  const [userLoading, setUserLoading] = useState(true);

  // User loading effect
  useEffect(() => {
    if (user) {
      console.log('User loaded in onboarding:', user);
      setUserLoading(false);
    }
  }, [user]);

  // Track definitions mapped to Dev Journey stages
  const tracks = {
    newbie: {
      title: "Track 1 – Newbie",
      description: "No—or very rough—idea. No users, no code.",
      devJourneyStages: "Stages 0-1: Spark → Formation",
      icon: "💡",
      bgPattern: 'from-purple-500/10 to-pink-500/10',
      borderColor: 'border-purple-500/20',
      focusAreas: ["Problem discovery", "Market research", "Idea validation", "Learning fundamentals"]
    },
    builder: {
      title: "Track 2 – Builder", 
      description: "Clear problem & solution sketch. Maybe a prototype or small user test.",
      devJourneyStages: "Stages 2-3: First Build → Ship",
      icon: "🔨",
      bgPattern: 'from-sauce-red/10 to-cheese-gold/10',
      borderColor: 'border-sauce-red/20',
      focusAreas: ["MVP development", "User testing", "Product-market fit", "Technical execution"]
    },
    scaler: {
      title: "Track 3 – Scaler",
      description: "Working product + early adopters; shipping weekly.",
      devJourneyStages: "Stages 4-5: Traction → Real Company",
      icon: "🚀", 
      bgPattern: 'from-orange-500/10 to-red-500/10',
      borderColor: 'border-orange-500/20',
      focusAreas: ["Growth optimization", "Scaling systems", "Market expansion", "Revenue growth"]
    }
  };

  // Streamlined structured questions - more concise while getting crucial info
  const structuredQuestions = [
    // Core Problem & Solution (4 questions)
    {
      id: 'problemDescription',
      category: 'Core Concept',
      question: 'What problem are you solving?',
      description: 'Describe the main pain point or opportunity you\'re addressing.',
      type: 'textarea',
      required: true
    },
    {
      id: 'solutionApproach', 
      category: 'Core Concept',
      question: 'How will you solve it?',
      description: 'Describe your solution approach, key features, or methodology.',
      type: 'textarea',
      required: true
    },
    {
      id: 'targetAudience',
      category: 'Core Concept', 
      question: 'Who is this for?',
      description: 'Be specific about your target users or customers.',
      type: 'textarea',
      required: true
    },
    {
      id: 'uniqueValueProposition',
      category: 'Core Concept',
      question: 'What makes this unique?',
      description: 'What differentiates your approach from existing solutions?',
      type: 'textarea',
      required: true
    },

    // Background & Skills (3 questions)
    {
      id: 'technicalBackground',
      category: 'Background & Skills',
      question: 'What\'s your technical background?',
      type: 'select',
      options: [
        { value: 'beginner', label: 'Beginner - Learning or new to tech' },
        { value: 'intermediate', label: 'Intermediate - Some experience' },
        { value: 'expert', label: 'Expert - Strong technical skills' }
      ],
      required: true
    },
    {
      id: 'previousExperience',
      category: 'Background & Skills',
      question: 'Relevant experience you have',
      description: 'Previous projects, work, education, or skills that relate to this venture.',
      type: 'textarea',
      required: true
    },
    {
      id: 'technicalSkills',
      category: 'Background & Skills', 
      question: 'Technical skills (if any)',
      description: 'Programming languages, tools, or other technical abilities.',
      type: 'input',
      placeholder: 'e.g., Python, React, Data Analysis (or leave blank)',
      required: false
    },

    // Goals (2 questions)
    {
      id: 'primaryGoal',
      category: 'Goals',
      question: 'Primary goal for the next 10 weeks',
      type: 'select',
      options: [
        { value: 'learn', label: 'Learn and explore - figure out what to build' },
        { value: 'build_mvp', label: 'Build an MVP or prototype' },
        { value: 'find_users', label: 'Find and validate with early users' },
        { value: 'scale', label: 'Scale and grow existing product' }
      ],
      required: true
    },
    {
      id: 'successMetrics',
      category: 'Goals',
      question: 'How will you measure success?',
      description: 'What specific outcomes would indicate you\'re making progress?',
      type: 'textarea',
      required: true
    }
  ];

  // Handle track selection
  const handleTrackSelection = (track: string) => {
    setSelectedTrack(track);
    setStructuredResponses({ ...structuredResponses, track });
    setCurrentPhase('structured');
    setPhaseProgress(5); // Start structured questions
  };

  // Handle structured question responses
  const handleStructuredResponse = (questionId: string, value: any) => {
    setStructuredResponses({
      ...structuredResponses,
      [questionId]: value
    });
  };

  // Navigate through structured questions
  const nextStructuredQuestion = () => {
    if (currentQuestionIndex < structuredQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setPhaseProgress(5 + ((currentQuestionIndex + 1) / structuredQuestions.length) * 40);
    } else {
      // Start AI deep dive phase
      setCurrentPhase('ai-dive');
      setPhaseProgress(50);
      startAIDeepDive();
    }
  };

  const prevStructuredQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      setPhaseProgress(5 + (currentQuestionIndex / structuredQuestions.length) * 40);
    } else {
      setCurrentPhase('track-selection');
      setPhaseProgress(0);
    }
  };

  // Start AI deep dive conversation
  const startAIDeepDive = async () => {
    setLoading(true);
    try {
      const response = await geminiService.conductFollowupConversation(
        structuredResponses as any,
        conversationHistory
      );
      
      setConversationHistory([
        ...conversationHistory,
        { role: 'ai', content: response.question }
      ]);
      setAiSatisfactionScore(response.satisfactionScore);
      setAreasNeedingClarification(response.areasNeedingClarification);
      
      console.log('AI Deep Dive started:', response);
    } catch (error) {
      console.error('Error starting AI deep dive:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle AI conversation
  const handleAIConversationResponse = async () => {
    if (!currentAIResponse.trim()) return;

    setLoading(true);
    
    // Add user response to conversation
    const newHistory = [
      ...conversationHistory,
      { role: 'user' as const, content: currentAIResponse }
    ];
    setConversationHistory(newHistory);
    setCurrentAIResponse('');

    try {
      const response = await geminiService.conductFollowupConversation(
        structuredResponses as any,
        newHistory
      );

      setAiSatisfactionScore(response.satisfactionScore);
      setAreasNeedingClarification(response.areasNeedingClarification);
      setPhaseProgress(50 + (response.satisfactionScore / 100) * 35);

      if (response.isComplete) {
        // Move to profile generation phase
        setCurrentPhase('generating');
        setPhaseProgress(90);
        await generateComprehensiveProfile(newHistory);
      } else {
        // Continue conversation
        setConversationHistory([
          ...newHistory,
          { role: 'ai', content: response.question }
        ]);
      }
    } catch (error) {
      console.error('Error in AI conversation:', error);
    } finally {
      setLoading(false);
    }
  };

  // Generate comprehensive profile and complete onboarding
  const generateComprehensiveProfile = async (finalConversation: Array<{role: 'user' | 'ai', content: string}>) => {
    try {
      setPhaseProgress(95);
      
      // Generate comprehensive profile
      const profile = await geminiService.generateComprehensiveProfile(
        structuredResponses as any,
        finalConversation
      );

      console.log('Generated comprehensive profile:', profile);

      // Save structured responses to database
      const { data: onboardingResponse, error: onboardingError } = await supabase
        .from('onboarding_responses')
        .insert({
          user_id: user!.id,
          track: selectedTrack,
          problem_description: structuredResponses.problemDescription,
          solution_approach: structuredResponses.solutionApproach,
          target_audience: structuredResponses.targetAudience,
          unique_value_proposition: structuredResponses.uniqueValueProposition,
          technical_background: structuredResponses.technicalBackground,
          previous_experience: structuredResponses.previousExperience,
          technical_skills: structuredResponses.technicalSkills,
          available_time_per_week: 10, // Default value since we removed this question
          primary_goal: structuredResponses.primaryGoal,
          success_metrics: structuredResponses.successMetrics,
          biggest_challenge: null, // Removed this field
          learning_style: 'hands-on', // Default value
          preferred_communication: 'weekly', // Default value
          collaboration_preference: 'community' // Default value
        })
        .select()
        .single();

      if (onboardingError) throw onboardingError;

      // Save AI conversation
      const { data: aiConversation, error: conversationError } = await supabase
        .from('ai_followup_conversations')
        .insert({
          user_id: user!.id,
          onboarding_response_id: onboardingResponse.id,
          conversation_data: finalConversation,
          ai_satisfaction_score: aiSatisfactionScore,
          areas_needing_clarification: areasNeedingClarification,
          key_insights_extracted: {},
          total_questions_asked: finalConversation.filter(msg => msg.role === 'ai').length,
          conversation_completed: true,
          completion_reason: 'satisfied'
        })
        .select()
        .single();

      if (conversationError) throw conversationError;

      // Save comprehensive profile
      const { error: profileError } = await supabase
        .from('comprehensive_user_profiles')
        .insert({
          user_id: user!.id,
          onboarding_response_id: onboardingResponse.id,
          followup_conversation_id: aiConversation.id,
          profile_summary: profile.profileSummary,
          strengths: profile.strengths,
          knowledge_gaps: profile.knowledgeGaps,
          personality_traits: profile.personalityTraits,
          project_viability_score: profile.projectViabilityScore,
          market_opportunity_assessment: profile.marketOpportunityAssessment,
          technical_feasibility_analysis: profile.technicalFeasibilityAnalysis,
          recommended_next_steps: profile.recommendedNextSteps,
          potential_blockers: profile.potentialBlockers,
          optimal_learning_path: profile.optimalLearningPath,
          mentor_matching_criteria: profile.mentorMatchingCriteria,
          resource_preferences: profile.resourcePreferences,
          communication_strategy: profile.communicationStrategy,
          personalized_goals: profile.personalizedGoals,
          success_probability_factors: profile.successProbabilityFactors,
          risk_factors: profile.riskFactors,
          preferred_dashboard_layout: profile.preferredDashboardLayout,
          key_metrics_to_track: profile.keyMetricsToTrack,
          notification_preferences: profile.notificationPreferences,
          profile_confidence_score: profile.profileConfidenceScore
        });

      if (profileError) throw profileError;

      // Store dev journey stage analysis
      if ((profile as any).dev_journey_stage !== undefined) {
        await supabase
          .from('user_dev_journey_stages')
          .insert({
            user_id: user!.id,
            stage_id: (profile as any).dev_journey_stage,
            confidence_score: (profile as any).stage_confidence,
            reasoning: (profile as any).stage_reasoning,
            next_actions: profile.recommendedNextSteps?.slice(0, 3) || [],
            applicable_frameworks: devJourneyService.getStage((profile as any).dev_journey_stage)?.frameworks ? 
              Object.keys(devJourneyService.getStage((profile as any).dev_journey_stage)!.frameworks!) : []
          });

        // Store initial user context
        await userMemoryService.storeMemory({
          user_id: user!.id,
          memory_type: 'context',
          content: {
            onboarding_completed: true,
            initial_stage: (profile as any).dev_journey_stage,
            track: selectedTrack,
            profile_summary: profile.profileSummary
          },
          metadata: {
            stage_id: (profile as any).dev_journey_stage,
            confidence_score: (profile as any).stage_confidence,
            source: 'onboarding_completion'
          }
        });
      }

      // Mark user onboarding as complete
      await supabase
        .from('users')
        .update({
          onboarding_completed: true,
          track: selectedTrack
        })
        .eq('id', user!.id);

      // Update user state
      updateUserProfile({
        onboarding_completed: true,
        track: selectedTrack as any
      });

      // Automatically populate dev journey milestones
      console.log('🎯 Auto-populating dev journey from onboarding...');
      try {
        const journeyResult = await onboardingToJourneyService.populateJourneyFromOnboarding(user!.id);
        if (journeyResult.success) {
          console.log(`✅ Journey auto-populated: ${journeyResult.milestonesPopulated} milestones for stage ${journeyResult.stageAssigned}`);
        } else {
          console.warn(`⚠️ Journey auto-population failed: ${journeyResult.message}`);
        }
      } catch (error) {
        console.error('Error auto-populating journey:', error);
        // Continue anyway - don't block onboarding completion
      }

      setPhaseProgress(100);

      // Navigate to dashboard
      setTimeout(() => {
        navigate('/dashboard');
      }, 1000);

    } catch (error) {
      console.error('Error generating comprehensive profile:', error);
      // Still mark as complete and navigate
      updateUserProfile({ 
        onboarding_completed: true, 
        track: selectedTrack as any 
      });
      navigate('/dashboard');
    }
  };

  // Show loading while user is being loaded
  if (userLoading || !user) {
    return (
      <div className="min-h-screen bg-oven-black flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-cheese-gold" />
      </div>
    );
  }

  const currentQuestion = structuredQuestions[currentQuestionIndex];
  const currentTrack = selectedTrack ? tracks[selectedTrack as keyof typeof tracks] : null;

  return (
    <div className="min-h-screen bg-oven-black relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-64 h-64 bg-sauce-red/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-cheese-gold/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Header */}
        <div className="p-6">
          <div className="flex items-center justify-between max-w-6xl mx-auto">
            <div className="flex items-center gap-3">
              <motion.div 
                animate={{ rotate: [0, 5, -5, 0] }} 
                transition={{ duration: 3, repeat: Infinity }}
              >
                <Pizza className="h-8 w-8 text-cheese-gold" />
              </motion.div>
              <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-sauce-red via-cheese-gold to-sauce-red">
                Pie Fi Onboarding
              </h1>
            </div>
            
            {/* Progress */}
            <div className="flex items-center gap-4">
              <div className="text-sm text-accent-white/70">
                {currentPhase === 'track-selection' && 'Choose Your Track'}
                {currentPhase === 'structured' && `Question ${currentQuestionIndex + 1} of ${structuredQuestions.length}`}
                {currentPhase === 'ai-dive' && `AI Deep Dive (${aiSatisfactionScore}% complete)`}
                {currentPhase === 'generating' && 'Generating Your Profile'}
              </div>
              <div className="w-32">
                <Progress value={phaseProgress} className="h-2" />
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex items-center justify-center p-6">
          <div className="w-full max-w-4xl">
            <AnimatePresence mode="wait">
              {/* Phase 1: Track Selection */}
              {currentPhase === 'track-selection' && (
                <motion.div
                  key="track-selection"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -30 }}
                  transition={{ duration: 0.6 }}
                >
                  <div className="text-center mb-12">
                    <h2 className="text-4xl font-bold text-accent-white mb-4">
                      Choose Your Builder Track
                    </h2>
                    <p className="text-xl text-accent-white/70">
                      Where are you in your entrepreneurial journey?
                    </p>
                  </div>

                  <div className="grid md:grid-cols-3 gap-6">
                    {Object.entries(tracks).map(([key, track]) => (
                      <motion.div
                        key={key}
                        whileHover={{ scale: 1.02, y: -5 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Card 
                          className={`bg-gradient-to-br ${track.bgPattern} backdrop-blur-sm border ${track.borderColor} shadow-2xl cursor-pointer hover:shadow-3xl transition-all duration-300`}
                          onClick={() => handleTrackSelection(key)}
                        >
                          <CardHeader className="text-center pb-4">
                            <div className="text-4xl mb-3">{track.icon}</div>
                            <CardTitle className="text-xl text-accent-white font-bold mb-2">
                              {track.title}
                            </CardTitle>
                            <p className="text-accent-white/80 text-sm">
                              {track.description}
                            </p>
                            <p className="text-accent-white/60 text-xs mt-2">
                              {track.devJourneyStages}
                            </p>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-2">
                              <p className="text-xs text-accent-white/60 font-semibold">Focus Areas:</p>
                              <div className="space-y-1">
                                {track.focusAreas.map((area, idx) => (
                                  <div key={idx} className="flex items-center gap-2">
                                    <CheckCircle className="h-3 w-3 text-cheese-gold" />
                                    <span className="text-xs text-accent-white/80">{area}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Phase 2: Structured Questions */}
              {currentPhase === 'structured' && currentQuestion && (
                <motion.div
                  key={`structured-${currentQuestionIndex}`}
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -50 }}
                  transition={{ duration: 0.4 }}
                >
                  <Card className={`bg-gradient-to-br ${currentTrack?.bgPattern} backdrop-blur-sm border ${currentTrack?.borderColor} shadow-2xl max-w-3xl mx-auto`}>
                    <CardHeader>
                      <div className="flex items-center gap-3 mb-4">
                        <Badge variant="outline" className="text-xs">
                          {currentQuestion.category}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {currentTrack?.title}
                        </Badge>
                      </div>
                      
                      <CardTitle className="text-2xl text-accent-white">
                        {currentQuestion.question}
                        {currentQuestion.required && <span className="text-sauce-red ml-1">*</span>}
                      </CardTitle>
                      
                      {currentQuestion.description && (
                        <p className="text-accent-white/70 text-sm">
                          {currentQuestion.description}
                        </p>
                      )}
                    </CardHeader>
                    
                    <CardContent className="space-y-6">
                      {/* Question Input */}
                      <div>
                        {currentQuestion.type === 'textarea' && (
                          <Textarea
                            value={structuredResponses[currentQuestion.id as keyof StructuredResponses] as string || ''}
                            onChange={(e) => handleStructuredResponse(currentQuestion.id, e.target.value)}
                            placeholder="Share your thoughts..."
                            className="bg-oven-black/50 border-accent-white/30 text-accent-white min-h-[120px]"
                          />
                        )}
                        
                        {currentQuestion.type === 'input' && (
                          <Input
                            value={structuredResponses[currentQuestion.id as keyof StructuredResponses] as string || ''}
                            onChange={(e) => handleStructuredResponse(currentQuestion.id, e.target.value)}
                            placeholder={currentQuestion.placeholder}
                            className="bg-oven-black/50 border-accent-white/30 text-accent-white"
                          />
                        )}
                        

                        
                        {currentQuestion.type === 'select' && (
                          <Select
                            value={structuredResponses[currentQuestion.id as keyof StructuredResponses] as string || ''}
                            onValueChange={(value) => handleStructuredResponse(currentQuestion.id, value)}
                          >
                            <SelectTrigger className="bg-oven-black/50 border-accent-white/30 text-accent-white">
                              <SelectValue placeholder="Select an option..." />
                            </SelectTrigger>
                            <SelectContent>
                              {currentQuestion.options?.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      </div>

                      {/* Navigation */}
                      <div className="flex justify-between pt-4">
                        <Button
                          variant="outline"
                          onClick={prevStructuredQuestion}
                          disabled={currentQuestionIndex === 0 && currentPhase === 'structured'}
                        >
                          Previous
                        </Button>
                        
                        <Button
                          onClick={nextStructuredQuestion}
                          disabled={currentQuestion.required && !structuredResponses[currentQuestion.id as keyof StructuredResponses]}
                          className="bg-gradient-to-r from-sauce-red to-cheese-gold"
                        >
                          {currentQuestionIndex === structuredQuestions.length - 1 ? 'Start AI Deep Dive' : 'Next'}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}

              {/* Phase 3: AI Deep Dive */}
              {currentPhase === 'ai-dive' && (
                <motion.div
                  key="ai-dive"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6 }}
                >
                  <Card className={`bg-gradient-to-br ${currentTrack?.bgPattern} backdrop-blur-sm border ${currentTrack?.borderColor} shadow-2xl max-w-4xl mx-auto`}>
                    <CardHeader>
                      <div className="flex items-center gap-3 mb-4">
                        <Brain className="h-6 w-6 text-cheese-gold" />
                        <div className="flex items-center gap-3">
                          <Badge variant="outline" className="text-xs">
                            AI Deep Dive
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {aiSatisfactionScore}% Complete
                          </Badge>
                        </div>
                      </div>
                      
                      <CardTitle className="text-2xl text-accent-white">
                        Building Your Comprehensive Profile
                      </CardTitle>
                      
                      <p className="text-accent-white/70">
                        Our AI mentor wants to understand you better to create the perfect personalized experience.
                      </p>
                      
                      {areasNeedingClarification.length > 0 && (
                        <div className="mt-4">
                          <p className="text-sm text-accent-white/60 mb-2">Areas being explored:</p>
                          <div className="flex flex-wrap gap-2">
                            {areasNeedingClarification.map((area, idx) => (
                              <Badge key={idx} variant="secondary" className="text-xs">
                                {area}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardHeader>
                    
                    <CardContent className="space-y-6">
                      {/* Conversation History */}
                      <div className="max-h-80 overflow-y-auto space-y-4 bg-gradient-to-b from-oven-black/30 to-oven-black/60 rounded-xl p-6 border border-accent-white/10 backdrop-blur-sm">
                        {conversationHistory.map((message, idx) => (
                          <motion.div 
                            key={idx} 
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: idx * 0.1 }}
                            className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                          >
                            <div className={`max-w-[85%] rounded-2xl p-4 ${
                              message.role === 'user' 
                                ? 'bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 border border-sauce-red/30' 
                                : 'bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-500/20'
                            }`}>
                              <div className="flex items-center gap-2 mb-2">
                                {message.role === 'user' ? (
                                  <div className="w-6 h-6 rounded-full bg-sauce-red/30 flex items-center justify-center">
                                    <User className="h-3 w-3" />
                                  </div>
                                ) : (
                                  <div className="w-6 h-6 rounded-full bg-cheese-gold/30 flex items-center justify-center">
                                    <Brain className="h-3 w-3 text-cheese-gold" />
                                  </div>
                                )}
                                <span className="text-xs font-semibold text-accent-white/90">
                                  {message.role === 'user' ? 'You' : 'AI Mentor'}
                                </span>
                              </div>
                              <MarkdownText className="text-sm text-accent-white/90 leading-relaxed">
                                {message.content}
                              </MarkdownText>
                            </div>
                          </motion.div>
                        ))}
                        
                        {/* Loading indicator */}
                        {loading && (
                          <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex gap-3 justify-start"
                          >
                            <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-500/20 rounded-2xl p-4">
                              <div className="flex items-center gap-2 mb-2">
                                <div className="w-6 h-6 rounded-full bg-cheese-gold/30 flex items-center justify-center">
                                  <Brain className="h-3 w-3 text-cheese-gold" />
                                </div>
                                <span className="text-xs font-semibold text-accent-white/90">AI Mentor</span>
                              </div>
                              <div className="flex items-center gap-2 text-accent-white/70">
                                <Loader2 className="h-4 w-4 animate-spin" />
                                <span className="text-sm">Thinking deeply about your response...</span>
                              </div>
                            </div>
                          </motion.div>
                        )}
                      </div>

                      {/* Current Response Input */}
                      <div className="space-y-4">
                        <Textarea
                          value={currentAIResponse}
                          onChange={(e) => setCurrentAIResponse(e.target.value)}
                          placeholder="Share your thoughts..."
                          className="bg-oven-black/50 border-accent-white/30 text-accent-white min-h-[100px]"
                        />
                        
                        <div className="flex justify-between items-center">
                          <p className="text-xs text-accent-white/60">
                            The AI will ask questions until it feels confident about your profile
                          </p>
                          
                          <Button
                            onClick={handleAIConversationResponse}
                            disabled={!currentAIResponse.trim() || loading}
                            className="bg-gradient-to-r from-sauce-red to-cheese-gold hover:from-sauce-red/90 hover:to-cheese-gold/90"
                          >
                            {loading ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                AI is thinking...
                              </>
                            ) : (
                              <>
                                <MessageCircle className="h-4 w-4 mr-2" />
                                Send Response
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}

              {/* Phase 4: Generating Profile */}
              {currentPhase === 'generating' && (
                <motion.div
                  key="generating"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6 }}
                >
                  <Card className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 backdrop-blur-sm border border-purple-500/20 shadow-2xl max-w-2xl mx-auto">
                    <CardContent className="text-center py-12">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                        className="mb-6"
                      >
                        <Sparkles className="h-16 w-16 text-cheese-gold mx-auto" />
                      </motion.div>
                      
                      <h2 className="text-3xl font-bold text-accent-white mb-4">
                        Generating Your Profile
                      </h2>
                      
                      <p className="text-accent-white/70 mb-6">
                        Creating your personalized dashboard experience based on our conversation...
                      </p>
                      
                      <div className="space-y-2">
                        <div className="flex items-center justify-center gap-2 text-sm text-accent-white/60">
                          <CheckCircle className="h-4 w-4 text-green-400" />
                          Analyzing your responses
                        </div>
                        <div className="flex items-center justify-center gap-2 text-sm text-accent-white/60">
                          <CheckCircle className="h-4 w-4 text-green-400" />
                          Building comprehensive profile
                        </div>
                        <div className="flex items-center justify-center gap-2 text-sm text-accent-white/60">
                          <Loader2 className="h-4 w-4 animate-spin text-cheese-gold" />
                          Personalizing your experience
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Onboarding; 