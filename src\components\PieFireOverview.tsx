import React from 'react';
import { Building2, Users, Pizza, Zap, TrendingUp, Lightbulb, Sparkles, Star, ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';
import { useScrollAnimation, scrollVariants, staggerContainer } from '@/hooks/useScrollAnimation';
import { useIsMobile } from '@/hooks/use-mobile';
import { Button } from '@/components/ui/button';

const highlights = [
  {
    icon: Building2,
    title: "Builder Clubhouse",
    description: "Where ideas become products",
    gradient: "from-blue-400 to-purple-500"
  },
  {
    icon: Users,
    title: "Real Community", 
    description: "Students & founders building together",
    gradient: "from-green-400 to-blue-500"
  },
  {
    icon: Pizza,
    title: "Pizza Restaurant",
    description: "Part accelerator, all community",
    gradient: "from-orange-400 to-red-500"
  }
];

const gameChangers = [
  {
    icon: TrendingUp,
    title: "Funding isn't the bottleneck anymore",
    description: "It's orientation, momentum, and proximity to the right people"
  },
  {
    icon: Zap,
    title: "AI has flattened the build process",
    description: "Anyone can ship. Distribution starts on Day 1"
  },
  {
    icon: Lightbulb,
    title: "Builders need real reps, not pitch prep",
    description: "Ship in public, learn fast, grow from a place that knows how"
  }
];

const PieFireOverview = () => {
  // Separate scroll animations for different sections
  const { ref: heroRef, isInView: heroInView } = useScrollAnimation();
  const { ref: pillarsRef, isInView: pillarsInView } = useScrollAnimation();
  const { ref: whyNowRef, isInView: whyNowInView } = useScrollAnimation();
  const { ref: ctaRef, isInView: ctaInView } = useScrollAnimation();
  const isMobile = useIsMobile();

  const handleLearnMoreClick = () => {
    window.location.href = '/ten-in-ten';
  };

  const handleFamilyFriendsClick = () => {
    window.location.href = '/family-friends';
  };

  return (
    <section className="relative py-12 sm:py-20 lg:py-32 overflow-hidden bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black">
      {/* Dramatic Background Effects */}
      <div className="absolute inset-0">
        {/* Large gradient orbs for impact */}
        <motion.div 
          className="absolute top-0 right-0 w-[600px] h-[600px] rounded-full bg-gradient-to-br from-sauce-red/20 to-cheese-gold/20 blur-3xl"
          animate={!isMobile ? {
            scale: [1, 1.3, 1],
            opacity: [0.3, 0.6, 0.3],
          } : {
            opacity: [0.4, 0.6, 0.4],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div 
          className="absolute bottom-0 left-0 w-[500px] h-[500px] rounded-full bg-gradient-to-br from-purple-500/15 to-blue-500/15 blur-3xl"
          animate={!isMobile ? {
            scale: [1.2, 1, 1.2],
            opacity: [0.2, 0.5, 0.2],
          } : {
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />

        {/* Floating particles - reduced on mobile */}
        {(isMobile ? [...Array(8)] : [...Array(25)]).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-3 h-3 bg-gradient-to-r from-sauce-red/60 to-cheese-gold/60 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={!isMobile ? {
              y: [-20, 20, -20],
              x: [-10, 10, -10],
              opacity: [0.4, 0.9, 0.4],
              scale: [0.5, 1.5, 0.5],
            } : {
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 4 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero-style Introduction */}
        <motion.div
          ref={heroRef}
          initial="hidden"
          animate={heroInView ? "visible" : "hidden"}
          variants={scrollVariants}
          className="text-center mb-12 sm:mb-16 lg:mb-20"
        >
          {/* Floating badge */}
          <motion.div
            className="inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 backdrop-blur-md border border-sauce-red/30 rounded-full text-sauce-red font-bold text-sm sm:text-base mb-8 sm:mb-12"
            whileHover={!isMobile ? { scale: 1.05, y: -5 } : undefined}
            animate={!isMobile ? {
              y: [0, -5, 0],
            } : undefined}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <Sparkles className="w-5 h-5" />
            🏗️ Santa Cruz's First Builder Launchpad
            <Star className="w-5 h-5" />
          </motion.div>
          
          {/* Massive headline */}
          <motion.h2 
            className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-black text-accent-white mb-6 sm:mb-8 leading-tight"
            initial={{ opacity: 0, y: isMobile ? 20 : 100, scale: isMobile ? 0.95 : 0.8 }}
            animate={heroInView ? { opacity: 1, y: 0, scale: 1 } : { opacity: 0, y: isMobile ? 20 : 100, scale: isMobile ? 0.95 : 0.8 }}
            transition={{ duration: isMobile ? 0.6 : 1.2, ease: "easeOut" }}
          >
            What is{" "}
            <motion.span 
              className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red via-cheese-gold to-sauce-red"
              {...(!isMobile && {
                style: { backgroundSize: '300%' },
                animate: {
                  backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
                },
                transition: {
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }
              })}
            >
              Pie Fi?
            </motion.span>
          </motion.h2>
          
          {/* Compelling tagline */}
          <motion.div
            className="space-y-4 sm:space-y-6"
            variants={scrollVariants}
          >
            <p className="text-2xl sm:text-3xl lg:text-4xl text-transparent bg-clip-text bg-gradient-to-r from-crust-beige to-accent-white font-bold max-w-5xl mx-auto leading-tight">
              A new kind of builder clubhouse in Santa Cruz
            </p>
            
            <motion.p 
              className="text-lg sm:text-xl lg:text-2xl text-crust-beige/90 max-w-4xl mx-auto leading-relaxed"
              initial={{ opacity: 0 }}
              animate={heroInView ? { opacity: 1 } : { opacity: 0 }}
              transition={{ delay: isMobile ? 0.4 : 0.8, duration: isMobile ? 0.4 : 0.8 }}
            >
              We give students and young founders <span className="text-sauce-red font-bold">funding</span>, <span className="text-cheese-gold font-bold">mentorship</span>, and a place to <span className="text-green-400 font-bold">ship real products</span>.
            </motion.p>
          </motion.div>
        </motion.div>

        {/* Compact Three Pillars - Mobile optimized */}
        <motion.div 
          ref={pillarsRef}
          className={`grid ${isMobile ? 'grid-cols-1 gap-4' : 'md:grid-cols-3 gap-8 lg:gap-12'} mb-16 sm:mb-20 lg:mb-24`}
          initial="hidden"
          animate={pillarsInView ? "visible" : "hidden"}
          variants={staggerContainer}
        >
          {highlights.map((highlight, index) => (
            <motion.div 
              key={index}
              className="group relative"
              variants={scrollVariants}
              whileHover={!isMobile ? { y: -15, scale: 1.05 } : undefined}
              transition={{ type: "spring", stiffness: 300 }}
            >
              {/* Background glow */}
              {!isMobile && (
                <div className={`absolute -inset-2 bg-gradient-to-r ${highlight.gradient} rounded-3xl blur-xl opacity-0 group-hover:opacity-70 transition-opacity duration-500`} />
              )}
              
              <div className={`relative bg-gradient-to-br from-accent-white/10 to-accent-white/5 backdrop-blur-md rounded-2xl ${isMobile ? 'p-6' : 'p-8 lg:p-10'} border border-accent-white/20 hover:border-accent-white/40 transition-all duration-300 text-center h-full`}>
                <motion.div 
                  className={`${isMobile ? 'w-12 h-12 mb-4' : 'w-16 h-16 lg:w-20 lg:h-20 mb-6'} mx-auto bg-gradient-to-r ${highlight.gradient} rounded-2xl flex items-center justify-center shadow-2xl`}
                  whileHover={!isMobile ? { rotate: [0, -10, 10, 0], scale: 1.1 } : undefined}
                  transition={{ duration: 0.6 }}
                >
                  <highlight.icon className={`${isMobile ? 'h-6 w-6' : 'h-8 w-8 lg:h-10 lg:w-10'} text-white`} />
                </motion.div>
                
                <h3 className={`${isMobile ? 'text-lg' : 'text-xl lg:text-2xl'} font-bold text-accent-white mb-3`}>
                  {highlight.title}
                </h3>
                <p className={`${isMobile ? 'text-sm' : 'text-base lg:text-lg'} text-crust-beige/80 leading-relaxed`}>
                  {highlight.description}
                </p>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Streamlined Why Now Section */}
        <motion.div 
          ref={whyNowRef}
          className="relative"
          initial="hidden"
          animate={whyNowInView ? "visible" : "hidden"}
          variants={scrollVariants}
        >
          <div className="relative bg-gradient-to-r from-accent-white/8 via-accent-white/12 to-accent-white/8 backdrop-blur-xl rounded-3xl p-6 sm:p-10 lg:p-16 border border-accent-white/30 overflow-hidden">
            {/* Animated background pattern - desktop only */}
            {!isMobile && (
              <motion.div
                className="absolute inset-0 opacity-30"
                animate={{
                  backgroundPosition: ["0% 0%", "100% 100%"],
                }}
                transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
                style={{
                  backgroundImage: "radial-gradient(circle, rgba(255,71,71,0.15) 2px, transparent 2px)",
                  backgroundSize: "40px 40px"
                }}
              />
            )}
            
            <div className="relative z-10">
              <motion.div 
                className="text-center mb-8 sm:mb-12"
                variants={scrollVariants}
              >
                <h3 className={`${isMobile ? 'text-2xl' : 'text-3xl sm:text-4xl lg:text-5xl'} font-black text-accent-white mb-4`}>
                  Why{" "}
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                    Now?
                  </span>
                </h3>
                <p className={`${isMobile ? 'text-base' : 'text-lg sm:text-xl'} text-crust-beige/90 max-w-3xl mx-auto font-medium`}>
                  The startup playbook has been completely rewritten
                </p>
              </motion.div>

              <motion.div 
                className={`${isMobile ? 'space-y-6' : 'grid gap-8 lg:gap-12'} max-w-6xl mx-auto`}
                variants={staggerContainer}
              >
                {gameChangers.map((item, index) => (
                  <motion.div 
                    key={index}
                    className="group flex items-start gap-4 sm:gap-6"
                    variants={scrollVariants}
                    whileHover={!isMobile ? { x: 10, scale: 1.02 } : undefined}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <motion.div 
                      className={`flex-shrink-0 ${isMobile ? 'w-10 h-10' : 'w-14 h-14 sm:w-16 sm:h-16'} bg-gradient-to-r from-sauce-red/30 to-cheese-gold/30 rounded-xl flex items-center justify-center border border-accent-white/30 group-hover:border-accent-white/60 transition-all duration-300`}
                      whileHover={!isMobile ? { scale: 1.15, rotate: 10 } : undefined}
                    >
                      <item.icon className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6 sm:h-8 sm:w-8'} text-sauce-red`} />
                    </motion.div>
                    
                    <div className="flex-1 min-w-0">
                      <h4 className={`${isMobile ? 'text-base' : 'text-lg sm:text-xl lg:text-2xl'} font-bold text-accent-white mb-2 group-hover:text-sauce-red transition-colors`}>
                        {item.title}
                      </h4>
                      <p className={`${isMobile ? 'text-sm' : 'text-base lg:text-lg'} text-crust-beige/80 leading-relaxed`}>
                        {item.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Learn More CTAs */}
        <motion.div 
          ref={ctaRef}
          className="text-center mt-16 sm:mt-20 lg:mt-24 px-4 sm:px-6"
          initial="hidden"
          animate={ctaInView ? "visible" : "hidden"}
          variants={scrollVariants}
        >
          <div className={`flex flex-col ${isMobile ? 'gap-4' : 'sm:flex-row sm:gap-6'} justify-center items-center`}>
            {/* Primary CTA - Learn More */}
            <motion.div 
              whileHover={!isMobile ? { scale: 1.05 } : undefined} 
              whileTap={{ scale: 0.95 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-sauce-red to-pink-500 rounded-2xl blur-xl opacity-70 group-hover:opacity-100 transition-opacity" />
              <Button 
                size={isMobile ? "default" : "lg"}
                onClick={handleLearnMoreClick}
                className={`relative bg-gradient-to-r from-sauce-red to-sauce-red/90 hover:from-sauce-red/90 hover:to-sauce-red text-accent-white font-bold ${
                  isMobile 
                    ? 'text-sm px-6 py-4 rounded-xl' 
                    : 'text-lg px-10 py-6 rounded-2xl'
                } shadow-2xl border-0 w-full sm:w-auto max-w-sm sm:max-w-none`}
              >
                {isMobile ? (
                  <>Learn More <ArrowRight className="ml-2 h-4 w-4" /></>
                ) : (
                  <>Learn More About The 10 in 10 Program <ArrowRight className="ml-3 h-6 w-6" /></>
                )}
              </Button>
            </motion.div>

            {/* Secondary CTA - Family & Friends */}
            <motion.div 
              whileHover={!isMobile ? { scale: 1.05 } : undefined} 
              whileTap={{ scale: 0.95 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-cheese-gold/50 to-yellow-500/50 rounded-2xl blur-xl opacity-40 group-hover:opacity-70 transition-opacity" />
              <Button 
                variant="outline"
                size={isMobile ? "default" : "lg"}
                onClick={handleFamilyFriendsClick}
                className={`relative bg-accent-white/10 backdrop-blur-md border-2 border-cheese-gold/50 text-cheese-gold hover:bg-cheese-gold/10 hover:border-cheese-gold hover:text-cheese-gold font-bold ${
                  isMobile 
                    ? 'text-sm px-6 py-4 rounded-xl' 
                    : 'text-lg px-10 py-6 rounded-2xl'
                } shadow-xl w-full sm:w-auto max-w-sm sm:max-w-none`}
              >
                {isMobile ? (
                  <>Info for Family and Friends <Users className="ml-2 h-4 w-4" /></>
                ) : (
                  <>Info for Family and Friends <Users className="ml-3 h-6 w-6" /></>
                )}
              </Button>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default PieFireOverview;
