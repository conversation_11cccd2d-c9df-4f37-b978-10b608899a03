// Debounce function for mobile events
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null;
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// Throttle function for scroll and resize events
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle = false;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

// Request idle callback with fallback
export const requestIdleCallbackShim = (
  callback: IdleRequestCallback,
  options?: IdleRequestOptions
): number => {
  if ('requestIdleCallback' in window) {
    return window.requestIdleCallback(callback, options);
  }
  
  // Fallback for browsers that don't support requestIdleCallback
  const start = Date.now();
  return window.setTimeout(() => {
    callback({
      didTimeout: false,
      timeRemaining: () => Math.max(0, 50 - (Date.now() - start)),
    });
  }, 1);
};

// Cancel idle callback with fallback
export const cancelIdleCallbackShim = (id: number): void => {
  if ('cancelIdleCallback' in window) {
    window.cancelIdleCallback(id);
  } else {
    clearTimeout(id);
  }
};

// Preload critical images
export const preloadImages = (urls: string[]): Promise<void[]> => {
  return Promise.all(
    urls.map(
      (url) =>
        new Promise<void>((resolve, reject) => {
          const img = new Image();
          img.onload = () => resolve();
          img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
          img.src = url;
        })
    )
  );
};

// Check if device has touch capability
export const isTouchDevice = (): boolean => {
  return (
    'ontouchstart' in window ||
    navigator.maxTouchPoints > 0 ||
    (navigator as any).msMaxTouchPoints > 0
  );
};

// Get device pixel ratio for responsive images
export const getDevicePixelRatio = (): number => {
  return window.devicePixelRatio || 1;
};

// Optimize image URL based on device capabilities
export const getOptimizedImageUrl = (
  baseUrl: string,
  options: {
    width?: number;
    quality?: number;
    format?: 'webp' | 'jpg' | 'png';
  } = {}
): string => {
  const { width, quality = 80, format = 'webp' } = options;
  const dpr = getDevicePixelRatio();
  
  // If using a CDN that supports on-the-fly optimization
  const params = new URLSearchParams();
  if (width) params.append('w', String(width * dpr));
  params.append('q', String(quality));
  params.append('fm', format);
  
  return `${baseUrl}?${params.toString()}`;
};

// Disable body scroll (useful for modals on mobile)
let scrollPosition = 0;

export const disableBodyScroll = (): void => {
  scrollPosition = window.pageYOffset;
  document.body.style.overflow = 'hidden';
  document.body.style.position = 'fixed';
  document.body.style.top = `-${scrollPosition}px`;
  document.body.style.width = '100%';
};

export const enableBodyScroll = (): void => {
  document.body.style.overflow = '';
  document.body.style.position = '';
  document.body.style.top = '';
  document.body.style.width = '';
  window.scrollTo(0, scrollPosition);
};

// Check if element is in viewport
export const isInViewport = (element: HTMLElement): boolean => {
  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
};

// Lazy load handler with IntersectionObserver
export const lazyLoad = (
  elements: NodeListOf<HTMLElement>,
  callback: (element: HTMLElement) => void,
  options: IntersectionObserverInit = {}
): IntersectionObserver => {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        callback(entry.target as HTMLElement);
        observer.unobserve(entry.target);
      }
    });
  }, {
    rootMargin: '50px',
    ...options,
  });

  elements.forEach((element) => observer.observe(element));
  
  return observer;
};

// Haptic feedback for mobile interactions
export const triggerHapticFeedback = (
  type: 'light' | 'medium' | 'heavy' = 'light'
): void => {
  if ('vibrate' in navigator) {
    const durations = {
      light: 10,
      medium: 20,
      heavy: 30,
    };
    navigator.vibrate(durations[type]);
  }
};

// Network information API wrapper
export const getNetworkInfo = (): {
  type?: string;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
  saveData?: boolean;
} => {
  const nav = navigator as any;
  const connection = nav.connection || nav.mozConnection || nav.webkitConnection;
  
  if (!connection) return {};
  
  return {
    type: connection.type,
    effectiveType: connection.effectiveType,
    downlink: connection.downlink,
    rtt: connection.rtt,
    saveData: connection.saveData,
  };
};

// Optimize animations based on network and device capabilities
export const shouldReduceAnimations = (): boolean => {
  // Check user preference
  if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    return true;
  }
  
  // Check network conditions
  const network = getNetworkInfo();
  if (network.saveData || network.effectiveType === 'slow-2g' || network.effectiveType === '2g') {
    return true;
  }
  
  // Check device memory (if available)
  const nav = navigator as any;
  if (nav.deviceMemory && nav.deviceMemory < 4) {
    return true;
  }
  
  return false;
};

// Touch event normalizer
export const getTouchPoint = (event: TouchEvent | MouseEvent): { x: number; y: number } => {
  if ('touches' in event && event.touches.length > 0) {
    return {
      x: event.touches[0].clientX,
      y: event.touches[0].clientY,
    };
  }
  
  return {
    x: (event as MouseEvent).clientX,
    y: (event as MouseEvent).clientY,
  };
};

// Swipe gesture detector
export const detectSwipe = (
  element: HTMLElement,
  callbacks: {
    onSwipeLeft?: () => void;
    onSwipeRight?: () => void;
    onSwipeUp?: () => void;
    onSwipeDown?: () => void;
  },
  threshold = 50
): (() => void) => {
  let startX = 0;
  let startY = 0;
  let startTime = 0;
  
  const handleStart = (e: TouchEvent | MouseEvent) => {
    const point = getTouchPoint(e);
    startX = point.x;
    startY = point.y;
    startTime = Date.now();
  };
  
  const handleEnd = (e: TouchEvent | MouseEvent) => {
    const point = getTouchPoint(e);
    const deltaX = point.x - startX;
    const deltaY = point.y - startY;
    const deltaTime = Date.now() - startTime;
    
    // Ignore if the swipe was too slow
    if (deltaTime > 300) return;
    
    const absX = Math.abs(deltaX);
    const absY = Math.abs(deltaY);
    
    if (absX > threshold && absX > absY) {
      if (deltaX > 0 && callbacks.onSwipeRight) {
        callbacks.onSwipeRight();
      } else if (deltaX < 0 && callbacks.onSwipeLeft) {
        callbacks.onSwipeLeft();
      }
    } else if (absY > threshold && absY > absX) {
      if (deltaY > 0 && callbacks.onSwipeDown) {
        callbacks.onSwipeDown();
      } else if (deltaY < 0 && callbacks.onSwipeUp) {
        callbacks.onSwipeUp();
      }
    }
  };
  
  element.addEventListener('touchstart', handleStart as any, { passive: true });
  element.addEventListener('touchend', handleEnd as any, { passive: true });
  element.addEventListener('mousedown', handleStart as any);
  element.addEventListener('mouseup', handleEnd as any);
  
  // Return cleanup function
  return () => {
    element.removeEventListener('touchstart', handleStart as any);
    element.removeEventListener('touchend', handleEnd as any);
    element.removeEventListener('mousedown', handleStart as any);
    element.removeEventListener('mouseup', handleEnd as any);
  };
}; 