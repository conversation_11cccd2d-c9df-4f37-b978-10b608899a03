-- Enhanced Comprehensive User Profile Function
-- This function returns ALL AI-generated insights, analysis data, and comprehensive profile information

DROP FUNCTION IF EXISTS get_user_comprehensive_profile(UUID);

CREATE OR REPLACE FUNCTION get_user_comprehensive_profile(target_user_id UUID)
RETURNS TABLE (
  -- Basic user info
  user_id UUID,
  full_name <PERSON><PERSON>ACTER VARYING,
  email CHARACTER VARYING,
  track CHARACTER VARYING,
  created_at TIMESTAMP WITH TIME ZONE,
  onboarding_completed BOOLEAN,
  
  -- Progress metrics
  current_stage INTEGER,
  current_confidence INTEGER,
  days_in_current_stage NUMERIC,
  engagement_score INTEGER,
  risk_level TEXT,
  success_category TEXT,
  progress_status TEXT,
  
  -- Onboarding data
  problem_description TEXT,
  solution_approach TEXT,
  target_audience TEXT,
  technical_background CHARACTER VARYING,
  primary_goal CHARACTER VARYING,
  biggest_challenge TEXT,
  
  -- Activity metrics
  total_daily_updates INTEGER,
  updates_last_7_days INTEGER,
  last_update_date TIMESTAMP WITH TIME ZONE,
  total_milestones_submitted INTEGER,
  approved_milestones INTEGER,
  last_milestone_date TIMESTAMP WITH TIME ZONE,
  
  -- AI-Generated Comprehensive Profile Analysis
  profile_summary TEXT,
  strengths TEXT[],
  knowledge_gaps TEXT[],
  personality_traits JSONB,
  project_viability_score INTEGER,
  market_opportunity_assessment TEXT,
  technical_feasibility_analysis TEXT,
  recommended_next_steps TEXT[],
  potential_blockers TEXT[],
  optimal_learning_path JSONB,
  mentor_matching_criteria JSONB,
  resource_preferences JSONB,
  communication_strategy JSONB,
  personalized_goals JSONB,
  success_probability_factors JSONB,
  risk_factors JSONB,
  profile_confidence_score INTEGER,
  
  -- AI Conversation Data
  ai_satisfaction_score INTEGER,
  areas_needing_clarification TEXT[],
  key_insights_extracted JSONB,
  conversation_completed BOOLEAN,
  total_questions_asked INTEGER,
  
  -- User Memory & Learning Analytics
  total_memory_entries INTEGER,
  interaction_memories INTEGER,
  progress_memories INTEGER,
  insight_memories INTEGER,
  context_memories INTEGER,
  
  -- Recent activity
  recent_daily_updates JSONB,
  recent_milestones JSONB,
  recent_memory_insights JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    -- Basic user info
    upa.user_id,
    upa.full_name,
    upa.email,
    upa.track,
    upa.user_created_at as created_at,
    upa.onboarding_completed,
    
    -- Progress metrics
    upa.current_stage,
    upa.current_confidence,
    upa.days_in_current_stage,
    upa.engagement_score,
    upa.risk_level,
    upa.success_category,
    upa.progress_status,
    
    -- Onboarding data
    or_data.problem_description,
    or_data.solution_approach,
    or_data.target_audience,
    or_data.technical_background,
    or_data.primary_goal,
    or_data.biggest_challenge,
    
    -- Activity metrics
    upa.total_daily_updates,
    upa.updates_last_7_days,
    upa.last_update_date,
    upa.total_milestones_submitted,
    upa.approved_milestones,
    upa.last_milestone_date,
    
    -- AI-Generated Comprehensive Profile Analysis
    cup.profile_summary,
    cup.strengths,
    cup.knowledge_gaps,
    cup.personality_traits,
    cup.project_viability_score,
    cup.market_opportunity_assessment,
    cup.technical_feasibility_analysis,
    cup.recommended_next_steps,
    cup.potential_blockers,
    cup.optimal_learning_path,
    cup.mentor_matching_criteria,
    cup.resource_preferences,
    cup.communication_strategy,
    cup.personalized_goals,
    cup.success_probability_factors,
    cup.risk_factors,
    cup.profile_confidence_score,
    
    -- AI Conversation Data
    afc.ai_satisfaction_score,
    afc.areas_needing_clarification,
    afc.key_insights_extracted,
    afc.conversation_completed,
    afc.total_questions_asked,
    
    -- User Memory & Learning Analytics
    COALESCE(memory_stats.total_entries, 0) as total_memory_entries,
    COALESCE(memory_stats.interaction_count, 0) as interaction_memories,
    COALESCE(memory_stats.progress_count, 0) as progress_memories,
    COALESCE(memory_stats.insight_count, 0) as insight_memories,
    COALESCE(memory_stats.context_count, 0) as context_memories,
    
    -- Recent activity (as JSON)
    COALESCE(
      (SELECT jsonb_agg(
        jsonb_build_object(
          'date', du.created_at,
          'content', LEFT(du.content, 200),
          'sentiment_score', du.sentiment_score
        ) ORDER BY du.created_at DESC
      ) FROM daily_updates du WHERE du.user_id = upa.user_id LIMIT 5),
      '[]'::jsonb
    ) as recent_daily_updates,

    COALESCE(
      (SELECT jsonb_agg(
        jsonb_build_object(
          'date', ms.submission_date,
          'stage_id', ms.stage_id,
          'milestone_id', ms.milestone_id,
          'is_approved', ms.is_approved,
          'content', LEFT(ms.submission_content, 200)
        ) ORDER BY ms.submission_date DESC
      ) FROM milestone_submissions ms WHERE ms.user_id = upa.user_id LIMIT 5),
      '[]'::jsonb
    ) as recent_milestones,

    -- Recent memory insights
    COALESCE(
      (SELECT jsonb_agg(
        jsonb_build_object(
          'date', ume.created_at,
          'type', ume.memory_type,
          'content', ume.content,
          'metadata', ume.metadata
        ) ORDER BY ume.created_at DESC
      ) FROM user_memory_entries ume
       WHERE ume.user_id = upa.user_id
       AND ume.memory_type IN ('insight', 'progress')
       LIMIT 10),
      '[]'::jsonb
    ) as recent_memory_insights
    
  FROM user_progress_analytics upa
  LEFT JOIN onboarding_responses or_data ON upa.user_id = or_data.user_id
  LEFT JOIN comprehensive_user_profiles cup ON upa.user_id = cup.user_id
  LEFT JOIN ai_followup_conversations afc ON upa.user_id = afc.user_id
  LEFT JOIN (
    SELECT 
      user_id,
      COUNT(*) as total_entries,
      COUNT(CASE WHEN memory_type = 'interaction' THEN 1 END) as interaction_count,
      COUNT(CASE WHEN memory_type = 'progress' THEN 1 END) as progress_count,
      COUNT(CASE WHEN memory_type = 'insight' THEN 1 END) as insight_count,
      COUNT(CASE WHEN memory_type = 'context' THEN 1 END) as context_count
    FROM user_memory_entries
    GROUP BY user_id
  ) memory_stats ON upa.user_id = memory_stats.user_id
  WHERE upa.user_id = target_user_id;
END;
$$ LANGUAGE plpgsql;

-- Add comment
COMMENT ON FUNCTION get_user_comprehensive_profile(UUID) IS 'Returns complete comprehensive user profile with all AI-generated insights, analysis data, and learning analytics';
