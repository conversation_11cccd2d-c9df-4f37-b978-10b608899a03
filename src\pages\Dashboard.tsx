import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import geminiService from '@/services/geminiService';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Sparkles, 
  TrendingUp, 
  Target, 
  Calendar, 
  MessageSquare, 
  Lightbulb,
  Users,
  BookOpen,
  Send,
  Loader2,
  Pizza,
  BarChart3,
  Zap,
  ArrowRight,
  Star,
  Brain,
  CheckCircle,
  Rocket,
  User,
  Activity,
  Flame,
  House
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import ContextualHelpWidget from '@/components/ContextualHelpWidget';
import DevJourneyProgressWidget from '@/components/DevJourneyProgressWidget';
import ModernProgressTab from '@/components/ModernProgressTab';
import DevJourneyStageTab from '@/components/DevJourneyStageTab';
import AdaptiveDevJourneyStageTab from '@/components/AdaptiveDevJourneyStageTab';
import StageChecklist from '@/components/StageChecklist';
import AdaptiveJourneyDisplay from '@/components/AdaptiveJourneyDisplay';
import MarkdownText from '@/components/MarkdownText';
import PersonalizedStageInfo from '@/components/PersonalizedStageInfo';
import DashboardRefreshModal from '@/components/DashboardRefreshModal';
import devJourneyService from '@/services/devJourneyService';
import { RefreshResult } from '@/services/dashboardRefreshService';
import userMemoryService from '@/services/userMemoryService';
import stageProgressService from '@/services/stageProgressService';
import adaptiveJourneyService from '@/services/adaptiveJourneyService';
import aiAnalysisCacheService from '@/services/aiAnalysisCache';
import onboardingToJourneyService from '@/services/onboardingToJourneyService';
import { activityTrackingService } from '@/services/activityTrackingService';

const Dashboard = () => {
  const { user, signOut } = useAuth();
  const [dailyUpdate, setDailyUpdate] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [mentors, setMentors] = useState<any[]>([]);
  const [resources, setResources] = useState<any[]>([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [recentUpdates, setRecentUpdates] = useState<any[]>([]);
  const [currentProject, setCurrentProject] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [aiInsight, setAiInsight] = useState<string>('');
  const [comprehensiveProfile, setComprehensiveProfile] = useState<any>(null);
  const [userStage, setUserStage] = useState<number>(0);
  const [stageConfidence, setStageConfidence] = useState<number>(50);
  const [adaptiveJourney, setAdaptiveJourney] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'journey' | 'progress'>('overview');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showFollowUp, setShowFollowUp] = useState(false);
  const [followUpQuestions, setFollowUpQuestions] = useState<string[]>([]);
  const [followUpResponse, setFollowUpResponse] = useState('');
  const [rateLimitWarning, setRateLimitWarning] = useState('');
  const [showRefreshModal, setShowRefreshModal] = useState(false);
  const [usingCachedInsight, setUsingCachedInsight] = useState(false);
  const [hasAnalyzedOnLoad, setHasAnalyzedOnLoad] = useState(() => {
    // Check if we've already analyzed in this session
    const sessionKey = `analyzed_${user?.id}_${new Date().toDateString()}`;
    return localStorage.getItem(sessionKey) === 'true';
  });
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [lastSubmissionTime, setLastSubmissionTime] = useState<number>(0);
  const [isPopulatingJourney, setIsPopulatingJourney] = useState(false);
  const [quickQuestions, setQuickQuestions] = useState<string[]>([]);

  // Enhanced refresh function for comprehensive re-analysis
  const refreshDashboard = async () => {
    if (!user) return;

    setIsRefreshing(true);
    try {
      console.log('🔄 Starting comprehensive dashboard refresh...');
      
      // 1. Get all recent updates for re-analysis
      const { data: allUpdates } = await supabase
        .from('daily_updates')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(20);

      // 2. Get comprehensive profile for context
      const { data: profileData } = await supabase
        .from('comprehensive_user_profiles')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      // 3. Perform enhanced AI stage analysis using all available data
      const combinedContext = (allUpdates || []).map(u => u.content).join(' ');
      
      const enhancedAnalysis = await geminiService.analyzeComprehensiveProgress(
        user.id,
        combinedContext,
        {
          track: user.track,
          profileData: profileData,
          recentUpdates: allUpdates || [],
          currentStage: userStage
        }
      );

      console.log('📊 Enhanced analysis result:', enhancedAnalysis);

      // 4. Update stage if analysis suggests advancement
      if (enhancedAnalysis && enhancedAnalysis.suggested_stage !== undefined && 
          enhancedAnalysis.suggested_stage !== userStage) {
        
        console.log(`🚀 Stage advancement detected: ${userStage} → ${enhancedAnalysis.suggested_stage}`);
        
        // Store new stage
        await supabase
          .from('user_dev_journey_stages')
          .insert({
            user_id: user.id,
            stage_id: enhancedAnalysis.suggested_stage,
            confidence_score: enhancedAnalysis.confidence_score || 75,
            reasoning: enhancedAnalysis.reasoning || 'Comprehensive progress analysis',
            next_actions: enhancedAnalysis.next_actions || [],
            applicable_frameworks: enhancedAnalysis.applicable_frameworks || []
          });

        // Update local state
        setUserStage(enhancedAnalysis.suggested_stage);
        setStageConfidence(enhancedAnalysis.confidence_score || 75);
      }

      // 5. Re-analyze all recent updates for milestone progress
      for (const update of (allUpdates || []).slice(0, 10)) {
        await stageProgressService.analyzeUpdateForProgress(
          user.id,
          update.content,
          enhancedAnalysis?.suggested_stage || userStage
        );
      }

      // 6. Populate dev journey from onboarding if not already done
      console.log('🎯 Checking if dev journey needs population from onboarding...');
      const hasBeenPopulated = await onboardingToJourneyService.hasJourneyBeenPopulated(user.id);

      if (!hasBeenPopulated) {
        console.log('📝 Populating dev journey from onboarding data...');
        const populationResult = await onboardingToJourneyService.populateJourneyFromOnboarding(user.id);

        if (populationResult.success) {
          console.log(`✅ Journey populated: ${populationResult.milestonesPopulated} milestones for stage ${populationResult.stageAssigned}`);

          // Update stage if it was assigned during population
          if (populationResult.stageAssigned !== userStage) {
            setUserStage(populationResult.stageAssigned);
            setStageConfidence(85);
          }
        } else {
          console.log(`⚠️ Journey population failed: ${populationResult.message}`);
        }
      } else {
        console.log('✅ Dev journey already populated from onboarding');
      }

      // 7. Refresh all dashboard data
      setRefreshTrigger(prev => prev + 1);

      console.log('✅ Dashboard refresh completed');
      
    } catch (error) {
      console.error('❌ Error refreshing dashboard:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Manual journey population function
  const populateJourneyFromOnboarding = async () => {
    if (!user) return;

    setIsPopulatingJourney(true);
    try {
      console.log('🎯 Manually populating dev journey from onboarding...');

      const result = await onboardingToJourneyService.populateJourneyFromOnboarding(user.id);

      if (result.success) {
        console.log(`✅ Journey populated: ${result.milestonesPopulated} milestones for stage ${result.stageAssigned}`);

        // Update stage if it was assigned during population
        if (result.stageAssigned !== userStage) {
          setUserStage(result.stageAssigned);
          setStageConfidence(85);
        }

        // Refresh dashboard to show new data
        setRefreshTrigger(prev => prev + 1);

        // Show success message
        toast.success(`Journey populated with ${result.milestonesPopulated} milestones!`);
      } else {
        console.log(`⚠️ Journey population failed: ${result.message}`);
        toast.error(`Failed to populate journey: ${result.message}`);
      }
    } catch (error) {
      console.error('Error populating journey:', error);
      toast.error('Error populating journey from onboarding data');
    } finally {
      setIsPopulatingJourney(false);
    }
  };

  // Fetch all dashboard data
  const fetchDashboardData = useCallback(async () => {
      if (!user) return;

      try {
        // Track dashboard page visit
        await activityTrackingService.trackPageVisit('dashboard', {
          track: user.track,
          onboarding_completed: user.onboarding_completed
        });

        // Show loading toast instead of blocking screen
        if (loading) {
          toast.info('Loading your dashboard...', { id: 'dashboard-loading' });
        }
        
        // Cleanup expired cache entries periodically
        aiAnalysisCacheService.cleanupExpiredCache().catch(console.error);
        
        // Fetch user project
        const { data: projectData, error: projectError } = await supabase
          .from('user_projects')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(1);

        if (projectError) {
          console.error('Error fetching project:', projectError);
        } else if (projectData && projectData.length > 0) {
          setCurrentProject(projectData[0]);
        }

        // Fetch comprehensive profile (the rich AI data)
        const { data: profileData, error: profileError } = await supabase
          .from('comprehensive_user_profiles')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(1);

        if (profileError) {
          console.error('Error fetching comprehensive profile:', profileError);
        } else if (profileData && profileData.length > 0) {
          setComprehensiveProfile(profileData[0]);
          console.log('Comprehensive profile loaded:', profileData[0]);
        }

        // ADAPTIVE JOURNEY ANALYSIS - Analyze user's true position across all stages
        console.log('🔍 Performing adaptive journey analysis...');
        try {
          const adaptiveAnalysis = await adaptiveJourneyService.analyzeUserJourney(user.id);
          console.log('🎯 Adaptive journey analysis:', adaptiveAnalysis);

          setAdaptiveJourney(adaptiveAnalysis);
          setUserStage(adaptiveAnalysis.current_effective_stage);
          setStageConfidence(adaptiveAnalysis.confidence_score);

          // Store the adaptive analysis for consistency across components
          await adaptiveJourneyService.storeJourneyAnalysis(user.id, adaptiveAnalysis);

          console.log(`✅ User positioned at effective Stage ${adaptiveAnalysis.current_effective_stage} (${adaptiveAnalysis.confidence_score}% confidence)`);
          console.log(`📊 Reasoning: ${adaptiveAnalysis.reasoning}`);

        } catch (error) {
          console.error('Error in adaptive journey analysis:', error);

          // Fallback to traditional stage detection
          const { data: stageData, error: stageError } = await supabase
            .from('user_dev_journey_stages')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })
            .limit(1);

          if (!stageError && stageData && stageData.length > 0) {
            const latestStage = stageData[0];
            setUserStage(latestStage.stage_id);
            setStageConfidence(latestStage.confidence_score);
          }
        }

        // Fetch recent daily updates
        const { data: updatesData, error: updatesError } = await supabase
          .from('daily_updates')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(5);

        if (updatesError) {
          console.error('Error fetching updates:', updatesError);
        } else {
          setRecentUpdates(updatesData || []);
          
          // Only analyze updates that haven't been processed yet
          if (updatesData && updatesData.length > 0 && !hasAnalyzedOnLoad && !isAnalyzing && refreshTrigger === 0) {
            // Check if the latest update has already been analyzed by looking for ai_insights
            const latestUpdate = updatesData[0];
            const hasBeenAnalyzed = latestUpdate.ai_insights &&
              (typeof latestUpdate.ai_insights === 'object' ?
                Object.keys(latestUpdate.ai_insights).length > 0 :
                latestUpdate.ai_insights.length > 0);

            // Also check if we have recent progress data that's newer than the latest update
            const latestUpdateTime = new Date(latestUpdate.created_at).getTime();
            const hasRecentProgressData = stageData && stageData.length > 0 &&
              new Date(stageData[0].created_at).getTime() >= latestUpdateTime;

            if (!hasBeenAnalyzed && !hasRecentProgressData) {
              setIsAnalyzing(true);
              setHasAnalyzedOnLoad(true);

              // Set session flag to prevent re-analysis
              const sessionKey = `analyzed_${user.id}_${new Date().toDateString()}`;
              localStorage.setItem(sessionKey, 'true');

              console.log('🔄 Auto-analyzing new update for milestone progress...');

              try {
                await stageProgressService.analyzeUpdateForProgress(
                  user.id,
                  latestUpdate.content,
                  stageData?.[0]?.stage_id || 0
                );

                // Check for potential stage advancement
                const advancement = await stageProgressService.updateStageIfReady(user.id);
                if (advancement.updated) {
                  console.log('🚀 Auto-detected stage advancement:', advancement);
                  setUserStage(advancement.newStage!);
                  setStageConfidence(75);
                }
              } catch (error) {
                console.error('Error in auto-analysis:', error);
              } finally {
                setIsAnalyzing(false);
              }
            } else {
              console.log('✅ Latest update already analyzed or progress data is current, skipping auto-analysis');
              setHasAnalyzedOnLoad(true);

              // Set session flag even when skipping
              const sessionKey = `analyzed_${user.id}_${new Date().toDateString()}`;
              localStorage.setItem(sessionKey, 'true');
            }
          }
        }

        // Note: Mentors and resources tables don't exist yet - removing these calls to prevent errors
        // TODO: Implement mentors and resources system when ready
        setMentors([]);
        setResources([]);

        // Generate AI insight based on recent activity and profile (with caching)
        if (updatesData && updatesData.length > 0 && profileData && profileData[0]) {
          try {
            const profile = profileData[0];
            const analysisContext = {
              track: user.track,
              projectStage: projectData?.[0]?.current_stage,
              recentProgress: updatesData.slice(0, 3),
              profileSummary: profile.profile_summary,
              knowledgeGaps: profile.knowledge_gaps,
              strengths: profile.strengths,
              devJourneyStage: userStage,
              userContext: {
                user_id: user.id,
                track: user.track,
                stage: userStage
              }
            };

            // Use cached analysis service to prevent re-running on every reload
            const { result: insights, wasCached } = await aiAnalysisCacheService.getOrCreateAnalysis(
              user.id,
              'dashboard_insight',
              updatesData[0].content,
              analysisContext,
              async () => {
                return await geminiService.analyzeDailyUpdate(
                  updatesData[0].content,
                  analysisContext
                );
              },
              4 // Cache for 4 hours for dashboard insights
            );

            setUsingCachedInsight(wasCached);
            setAiInsight(insights.encouragement || insights.insights || insights.coachingInsight);
          } catch (error) {
            console.error('Error generating AI insight:', error);
            // Fallback to cached or default insight
            setAiInsight('Welcome back! Complete your daily check-in to get fresh insights.');
          }
        } else if (profileData && profileData[0]) {
          // Use profile summary as fallback insight
          setAiInsight(`Welcome back! Based on your profile, focus on: ${profileData[0].recommended_next_steps?.[0] || 'continuing your great work'}`);
        }

        toast.success('Dashboard loaded!', { id: 'dashboard-loading' });
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        toast.error('Error loading dashboard data', { id: 'dashboard-loading' });
      } finally {
        setLoading(false);
      }
    }, [user]);

  // useEffect to trigger data loading
  useEffect(() => {
    // Only fetch data on initial load or explicit refresh, not on tab focus
    if (!user) return;

    // Check if we've already loaded data for this user in this session
    const sessionKey = `dashboard_loaded_${user.id}_${new Date().toDateString()}`;
    const hasLoadedToday = localStorage.getItem(sessionKey) === 'true';

    // Always load on explicit refresh, or if not loaded today, or if accessing journey tab without adaptive data
    const shouldLoad = !hasLoadedToday || refreshTrigger > 0 || (activeTab === 'journey' && !adaptiveJourney);

    if (shouldLoad) {
      fetchDashboardData();
      localStorage.setItem(sessionKey, 'true');
    } else {
      console.log('✅ Dashboard data already loaded today, skipping fetch');
      setLoading(false);
    }
  }, [user?.id, refreshTrigger, activeTab]); // Include activeTab to trigger loading when switching to journey tab

  // Ensure adaptive journey analysis is available when switching to journey tab
  useEffect(() => {
    if (activeTab === 'journey' && user && !adaptiveJourney && !loading) {
      console.log('🔄 Journey tab accessed without adaptive analysis, loading...');
      fetchDashboardData();
    }
  }, [activeTab, user, adaptiveJourney, loading]);

  // Optimized function to refresh only adaptive journey analysis
  const refreshAdaptiveJourney = useCallback(async () => {
    if (!user) return;

    try {
      console.log('🔄 Refreshing adaptive journey analysis...');
      const adaptiveAnalysis = await adaptiveJourneyService.analyzeUserJourney(user.id);
      console.log('🎯 Updated adaptive journey analysis:', adaptiveAnalysis);

      setAdaptiveJourney(adaptiveAnalysis);
      setUserStage(adaptiveAnalysis.current_effective_stage);
      setStageConfidence(adaptiveAnalysis.confidence_score);

      // Store the updated analysis
      await adaptiveJourneyService.storeJourneyAnalysis(user.id, adaptiveAnalysis);

      toast.success('Journey analysis updated successfully!');
    } catch (error) {
      console.error('Error refreshing adaptive journey:', error);

      // Provide user-friendly error message
      if (error instanceof Error) {
        if (error.message.includes('network') || error.message.includes('fetch')) {
          toast.error('Network error while updating journey analysis. Please check your connection.');
        } else if (error.message.includes('unauthorized')) {
          toast.error('Session expired. Please refresh the page and try again.');
        } else {
          toast.error('Failed to update journey analysis. Please try again.');
        }
      } else {
        toast.error('An unexpected error occurred while updating your journey.');
      }
    }
  }, [user]);

  // Generate contextual quick questions based on user's stage and project
  const generateQuickQuestions = async () => {
    if (!user || !comprehensiveProfile) return;

    try {
      // Get current stage information and milestones
      const currentStageInfo = devJourneyService.getStage(userStage);
      const stageMilestones = stageProgressService.getStageMilestones(userStage);
      const incompleteMilestones = stageMilestones.filter(m => m.required && !m.completed);

      // Get user context for more personalized questions
      const userContext = await userMemoryService.buildUserContext(user.id);

      // Debug: Log available data to understand what we have
      console.log('🔍 Question generation data:', {
        comprehensiveProfile: comprehensiveProfile ? 'Available' : 'Missing',
        onboardingResponse: comprehensiveProfile?.onboarding_response ? 'Available' : 'Missing',
        userContext: userContext ? 'Available' : 'Missing',
        projectInfo: userContext?.project_info ? 'Available' : 'Missing'
      });

      // Extract user data with better fallbacks
      const problemDescription = comprehensiveProfile?.onboarding_response?.problem_description ||
                                userContext?.project_info?.description ||
                                comprehensiveProfile?.market_opportunity_assessment ||
                                'Not specified';

      const solutionApproach = comprehensiveProfile?.onboarding_response?.solution_approach ||
                              userContext?.project_info?.current_focus ||
                              'Not specified';

      const targetAudience = comprehensiveProfile?.onboarding_response?.target_audience ||
                            userContext?.project_info?.target_audience ||
                            'Not specified';

      const technicalBackground = comprehensiveProfile?.onboarding_response?.technical_background ||
                                  userContext?.project_info?.technical_background ||
                                  'Not specified';

      const primaryGoal = comprehensiveProfile?.onboarding_response?.primary_goal ||
                         userContext?.project_info?.primary_goal ||
                         'Not specified';

      const prompt = `
Generate 2-3 focused reflection questions for a daily check-in based on their current dev journey stage:

CURRENT STAGE: ${currentStageInfo?.title || 'Unknown'} (Stage ${userStage})
Stage Description: ${currentStageInfo?.description || 'No description'}
Stage Characteristics: ${currentStageInfo?.characteristics?.join(', ') || 'None'}

PROJECT CONTEXT:
- Problem: ${problemDescription}
- Solution: ${solutionApproach}
- Target Users: ${targetAudience}
- Technical Background: ${technicalBackground}
- Primary Goal: ${primaryGoal}

INCOMPLETE MILESTONES FOR THIS STAGE:
${incompleteMilestones.map(m => `- ${m.title}: ${m.description}`).join('\n')}

KEY CHALLENGES: ${userContext.key_challenges?.join(', ') || 'None identified'}
RECENT PROGRESS: ${userContext.recent_progress?.slice(0, 2)?.join('; ') || 'No recent updates'}
USER STRENGTHS: ${userContext.ai_insights?.strengths?.join(', ') || comprehensiveProfile?.strengths?.join(', ') || 'None identified'}
KNOWLEDGE GAPS: ${userContext.ai_insights?.knowledge_gaps?.join(', ') || comprehensiveProfile?.knowledge_gaps?.join(', ') || 'None identified'}

Create questions that:
1. Are specifically relevant to their current stage and incomplete milestones
2. Address their identified challenges and project context
3. Help them make progress on stage-specific objectives
4. Encourage validation and learning appropriate for their stage
5. Are actionable and answerable in 1-2 sentences

Focus on what they should be doing RIGHT NOW in ${currentStageInfo?.title || 'their current stage'}.
Format as direct questions, one per line, no bullet points or numbers.
`;

      const result = await geminiService.model?.generateContent(prompt);
      const questions = await result?.response.text();

      if (questions && questions.trim()) {
        const questionList = questions.trim().split('\n').filter(q => q.trim().length > 0).slice(0, 3);
        setQuickQuestions(questionList);

        // Cache the questions for the day
        const questionKey = `questions_${user.id}_${userStage}_${new Date().toDateString()}`;
        localStorage.setItem(questionKey, JSON.stringify(questionList));
        console.log('💾 Cached reflection questions for today');
      }
    } catch (error) {
      console.error('Error generating quick questions:', error);
      // Fallback questions based on stage
      const fallbackQuestions = getFallbackQuestions(userStage, comprehensiveProfile);
      setQuickQuestions(fallbackQuestions);
    }
  };

  // Fallback questions if AI generation fails
  const getFallbackQuestions = (stage: number, profile: any): string[] => {
    const projectType = profile?.onboarding_response?.problem_description?.toLowerCase() || '';

    if (projectType.includes('health') || projectType.includes('medical')) {
      return [
        "What's your biggest technical challenge with sensor accuracy this week?",
        "Have you connected with any healthcare professionals for feedback?",
        "What specific user testing are you planning next?"
      ];
    }

    const stageQuestions = {
      0: ["What evidence do you have that this problem is worth solving?", "Who have you talked to about this problem recently?", "What's the biggest assumption you need to validate?"],
      1: ["What's your biggest uncertainty about the solution approach?", "What resources or skills do you need to move forward?", "What would success look like in the next 2 weeks?"],
      2: ["What's blocking you from building your MVP?", "What's the riskiest part of your technical approach?", "How will you know if your MVP is working?"],
      3: ["How are users responding to your current version?", "What's your biggest user acquisition challenge?", "What metrics are you tracking for success?"],
      4: ["What's your biggest scaling challenge right now?", "How are you prioritizing new features vs. optimization?", "What partnerships could accelerate your growth?"],
      5: ["What's your strategy for market expansion?", "How are you maintaining product-market fit as you scale?", "What's your biggest competitive threat?"]
    };

    return stageQuestions[stage as keyof typeof stageQuestions] || stageQuestions[0];
  };

  // Generate questions when stage or profile changes (but not on every render)
  useEffect(() => {
    if (comprehensiveProfile && userStage !== undefined && quickQuestions.length === 0) {
      // Only generate if we don't already have questions
      const questionKey = `questions_${user?.id}_${userStage}_${new Date().toDateString()}`;
      const cachedQuestions = localStorage.getItem(questionKey);

      if (cachedQuestions) {
        try {
          const parsed = JSON.parse(cachedQuestions);
          setQuickQuestions(parsed);
          console.log('✅ Using cached reflection questions');
        } catch (error) {
          console.error('Error parsing cached questions:', error);
          generateQuickQuestions();
        }
      } else {
        generateQuickQuestions();
      }
    }
  }, [comprehensiveProfile?.id, userStage]); // Use profile.id instead of full object

  // Data only refreshes on explicit user actions (submissions, manual refresh)
  // No automatic refresh on window focus - preserves state across tab switches

  const handleDailyUpdate = async () => {
    if (!dailyUpdate.trim() || !user) return;

    // Debounce: prevent submissions within 5 seconds of each other
    const now = Date.now();
    if (now - lastSubmissionTime < 5000) {
      console.log('⏳ Submission debounced - please wait a moment');
      return;
    }

    setIsSubmitting(true);
    setLastSubmissionTime(now);
    setRateLimitWarning(''); // Clear any previous warnings
    
    try {
      // Store the interaction in user memory
      await userMemoryService.storeInteraction(
        user.id,
        'update',
        dailyUpdate,
        { 
          stage: userStage,
          project_stage: currentProject?.current_stage,
          timestamp: new Date().toISOString()
        }
      );

      // Get enhanced AI analysis with dev journey context
      console.log('Analyzing daily update with AI...');
      const userContext = await userMemoryService.buildUserContext(user.id);

      // Use authoritative stage from userContext (database) instead of React state
      const authoritativeStage = userContext.current_stage;
      const stageInfo = devJourneyService.getStage(authoritativeStage);

      console.log(`🎯 Using authoritative stage ${authoritativeStage} for AI analysis (React state: ${userStage})`);

      // Update React state if it's out of sync with database
      if (authoritativeStage !== userStage) {
        console.log(`🔄 Syncing React state: ${userStage} → ${authoritativeStage}`);
        setUserStage(authoritativeStage);
        setStageConfidence(userContext.stage_confidence);
      }

      // Track AI analysis request
      await activityTrackingService.trackAIAnalysisRequest('daily_update', {
        content_length: dailyUpdate.length,
        track: user.track,
        stage: userStage
      });

      const aiAnalysis = await geminiService.analyzeDailyUpdate(
        dailyUpdate,
        {
          track: user.track,
          projectStage: currentProject?.current_stage,
          recentProgress: recentUpdates,
          devJourneyStage: authoritativeStage, // Use database stage, not React state
          stageTitle: stageInfo?.title,
          userContext: {
            ...userContext,
            user_id: user.id
          },
          stageFrameworks: Object.keys(stageInfo?.frameworks || {}),
          currentChallenges: userContext.key_challenges
        }
      );

      console.log('AI Analysis:', aiAnalysis);

      // Handle rate limit warnings
      if (aiAnalysis.rateLimitWarning) {
        setRateLimitWarning(aiAnalysis.rateLimitWarning);
      }

      // Save update to database with AI insights
      const { data: update } = await supabase
        .from('daily_updates')
        .insert({
          user_id: user.id,
          content: dailyUpdate,
          project_id: currentProject?.id || null,
          ai_insights: aiAnalysis as any,
          sentiment_score: aiAnalysis.sentiment || 0.7,
          recommended_resources: []
        })
        .select()
        .single();

      // Invalidate dashboard insight cache since we have new content
      await aiAnalysisCacheService.invalidateCache(user.id, 'dashboard_insight');
      setUsingCachedInsight(false); // Fresh analysis will be run next time

      if (update) {
        // Track daily update submission
        await activityTrackingService.trackDailyUpdateSubmit(
          dailyUpdate.length,
          !!aiAnalysis
        );

        // Store progress in user memory
        await userMemoryService.storeProgress(
          user.id,
          'daily_update',
          {
            update_content: dailyUpdate,
            ai_analysis: aiAnalysis,
            timestamp: new Date().toISOString()
          },
          userStage
        );

        // IMMEDIATE milestone analysis with AI integration
        console.log('🔍 Analyzing update for milestone progress...');
        const progressUpdate = await stageProgressService.analyzeUpdateForProgress(
          user.id,
          dailyUpdate,
          userStage
        );

        if (progressUpdate) {
          console.log('📊 Stage progress updated:', progressUpdate);
          
          // IMMEDIATE stage advancement check
          const advancement = await stageProgressService.updateStageIfReady(user.id);
          if (advancement.updated) {
            console.log('🚀 Stage advancement detected:', advancement);
            setUserStage(advancement.newStage!);
            setStageConfidence(75);
            
            // Show success message with stage advancement
            setAiInsight(`🎉 Congratulations! You've advanced to ${devJourneyService.getStage(advancement.newStage!)?.name || 'the next stage'}! ${aiAnalysis.coachingInsight || aiAnalysis.insights}`);
            
            // Trigger full dashboard refresh to show new stage
            setRefreshTrigger(prev => prev + 1);
          } else {
            setAiInsight(aiAnalysis.coachingInsight || aiAnalysis.encouragement || aiAnalysis.insights);
          }
        } else {
          setAiInsight(aiAnalysis.coachingInsight || aiAnalysis.encouragement || aiAnalysis.insights);
        }

        // Handle follow-up questions
        if (aiAnalysis.needsFollowUp && aiAnalysis.followUpQuestions && aiAnalysis.followUpQuestions.length > 0) {
          setFollowUpQuestions(aiAnalysis.followUpQuestions);
          setShowFollowUp(true);
        }

        // Update recent updates list
        setRecentUpdates([update, ...recentUpdates.slice(0, 4)]);
      }

      setDailyUpdate('');
    } catch (error) {
      console.error('Error submitting daily update:', error);
      setAiInsight('Sorry, there was an error analyzing your update. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFollowUpResponse = async () => {
    if (!followUpResponse.trim()) return;
    
    setIsSubmitting(true);
    try {
      // Combine original update with follow-up for re-analysis
      const combinedContent = `${dailyUpdate} Follow-up: ${followUpResponse}`;
      
      // Re-analyze with additional context
      const userContext = await userMemoryService.buildUserContext(user.id);
      const stageInfo = devJourneyService.getStage(userStage);
      
      const enhancedAnalysis = await geminiService.analyzeDailyUpdate(
        combinedContent,
        {
          track: user.track,
          projectStage: currentProject?.current_stage,
          recentProgress: recentUpdates,
          devJourneyStage: userStage,
          stageTitle: stageInfo?.title,
          userContext: {
            ...userContext,
            user_id: user.id
          },
          stageFrameworks: Object.keys(stageInfo?.frameworks || {}),
          currentChallenges: userContext.key_challenges,
          isFollowUp: true
        }
      );

      setAiInsight(enhancedAnalysis.coachingInsight || enhancedAnalysis.encouragement || 'Great additional context! This helps me understand your progress better.');
      
      // Store follow-up response
      await userMemoryService.storeInteraction(
        user.id,
        'update', // Use valid interaction type
        followUpResponse,
        { 
          stage: userStage,
          original_update: dailyUpdate,
          timestamp: new Date().toISOString(),
          type: 'follow_up'
        }
      );

      setShowFollowUp(false);
      setFollowUpResponse('');
      setFollowUpQuestions([]);
      
    } catch (error) {
      console.error('Error processing follow-up:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Trigger refresh for dynamic updates
  const triggerRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Handle dashboard refresh completion
  const handleRefreshComplete = (result: RefreshResult) => {
    console.log('🔄 Dashboard refresh completed:', result);

    if (result.success) {
      // Update stage if it changed
      if (result.newStage !== undefined) {
        setUserStage(result.newStage);
        setStageConfidence(result.newConfidence || 75);
      }

      // Trigger full dashboard refresh to show new data
      setRefreshTrigger(prev => prev + 1);

      // Show success message
      setAiInsight(`🎉 Dashboard refreshed successfully! ${result.message}`);
    } else {
      // Show error message
      setAiInsight(`⚠️ Dashboard refresh completed with issues: ${result.message}`);
    }
  };

  // Show dashboard immediately with progressive loading indicators
  // No blocking loading screen - use skeleton loaders and toasts instead

  const currentStage = devJourneyService.getStage(userStage);
  const allStages = [0, 1, 2, 3, 4, 5].map(id => devJourneyService.getStage(id)).filter(Boolean);

  // Skeleton loader component
  const SkeletonLoader = ({ className = "" }: { className?: string }) => (
    <div className={`animate-pulse bg-accent-white/10 rounded-lg ${className}`} />
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-oven-black via-purple-900/10 to-oven-black text-accent-white">
      {/* Enhanced Animated Background with Premium Pie Fi Glow */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        {/* Primary gradient glows - Enhanced */}
        <motion.div 
          className="absolute top-1/4 left-1/4 w-[1000px] h-[1000px] rounded-full bg-gradient-to-r from-sauce-red/30 to-cheese-gold/30 blur-3xl will-change-transform"
          animate={{
            scale: [1, 1.4, 1],
            rotate: [0, 360],
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{ transform: 'translateZ(0)' }}
        />
        
        <motion.div 
          className="absolute bottom-1/3 right-1/4 w-[800px] h-[800px] rounded-full bg-gradient-to-r from-purple-500/25 to-pink-500/25 blur-3xl will-change-transform"
          animate={{
            scale: [1.3, 1, 1.3],
            x: [0, 100, 0],
            y: [0, -60, 0],
            opacity: [0.2, 0.5, 0.2]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 3
          }}
          style={{ transform: 'translateZ(0)' }}
        />

        {/* Additional premium glow layers for depth */}
        <motion.div 
          className="absolute top-1/2 left-1/2 w-[600px] h-[600px] rounded-full bg-gradient-to-r from-cheese-gold/35 to-orange-500/35 blur-2xl will-change-transform"
          animate={{
            scale: [1, 1.6, 1],
            opacity: [0.15, 0.4, 0.15],
            rotate: [0, -180, 0]
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 6
          }}
          style={{ transform: 'translate(-50%, -50%) translateZ(0)' }}
        />

        {/* Pie Fi signature glow rings */}
        <motion.div 
          className="absolute top-1/3 right-1/3 w-[400px] h-[400px] rounded-full border border-sauce-red/20 blur-sm will-change-transform"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 360],
            opacity: [0.1, 0.3, 0.1]
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "linear"
          }}
        />

        <motion.div 
          className="absolute bottom-1/4 left-1/3 w-[300px] h-[300px] rounded-full border border-cheese-gold/25 blur-sm will-change-transform"
          animate={{
            scale: [1.1, 0.9, 1.1],
            rotate: [360, 0],
            opacity: [0.2, 0.4, 0.2]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "linear",
            delay: 2
          }}
        />

        {/* Enhanced floating particles with premium glow */}
        {[...Array(16)].map((_, i) => (
          <motion.div
            key={i}
            className={`absolute w-4 h-4 rounded-full ${
              i % 5 === 0 ? 'bg-cheese-gold/70 shadow-2xl shadow-cheese-gold/50' : 
              i % 5 === 1 ? 'bg-sauce-red/70 shadow-2xl shadow-sauce-red/50' : 
              i % 5 === 2 ? 'bg-purple-400/70 shadow-2xl shadow-purple-400/50' : 
              i % 5 === 3 ? 'bg-pink-400/70 shadow-2xl shadow-pink-400/50' :
              'bg-blue-400/70 shadow-2xl shadow-blue-400/50'
            } blur-[0.5px]`}
            style={{
              left: `${5 + (i * 6)}%`,
              top: `${15 + (i * 5)}%`,
            }}
            animate={{
              y: [-40, 40, -40],
              x: [-20, 20, -20],
              opacity: [0.4, 1, 0.4],
              scale: [0.6, 1.4, 0.6]
            }}
            transition={{
              duration: 8 + i * 0.7,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.5,
            }}
          />
        ))}

        {/* Premium geometric shapes with enhanced glow effects */}
        {[
          { shape: "circle", size: 80, delay: 0, x: 12, y: 20, color: "bg-sauce-red/25", glow: "shadow-sauce-red/40", blur: "blur-[1px]" },
          { shape: "square", size: 60, delay: 1.5, x: 88, y: 30, color: "bg-cheese-gold/30", glow: "shadow-cheese-gold/50", blur: "blur-[1px]" },
          { shape: "triangle", size: 70, delay: 3, x: 20, y: 70, color: "bg-purple-400/25", glow: "shadow-purple-400/40", blur: "blur-[1px]" },
          { shape: "diamond", size: 50, delay: 1, x: 80, y: 75, color: "bg-blue-400/30", glow: "shadow-blue-400/50", blur: "blur-[1px]" },
          { shape: "hexagon", size: 45, delay: 2, x: 50, y: 10, color: "bg-pink-400/25", glow: "shadow-pink-400/40", blur: "blur-[1px]" }
        ].map((item, i) => (
          <motion.div
            key={i}
            className={`absolute select-none pointer-events-none ${item.color} shadow-2xl ${item.glow} ${item.blur}`}
            style={{
              left: `${item.x}%`,
              top: `${item.y}%`,
              width: `${item.size}px`,
              height: `${item.size}px`,
              borderRadius: item.shape === 'circle' ? '50%' : 
                           item.shape === 'triangle' ? '0' : 
                           item.shape === 'diamond' ? '0' : 
                           item.shape === 'hexagon' ? '0' : '15px',
              clipPath: item.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' :
                       item.shape === 'diamond' ? 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)' : 
                       item.shape === 'hexagon' ? 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)' : 'none'
            }}
            animate={{
              y: [-25, 25, -25],
              x: [-15, 15, -15],
              rotate: [0, 360],
              opacity: [0.3, 0.8, 0.3],
              scale: [0.7, 1.3, 0.7]
            }}
            transition={{
              duration: 10 + i * 1.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: item.delay * 2.5
            }}
          />
        ))}

        {/* Signature Pie Fi pizza slice elements */}
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={`pizza-${i}`}
            className="absolute w-8 h-8 bg-gradient-to-br from-cheese-gold/60 to-sauce-red/60 shadow-2xl shadow-orange-500/40 blur-[0.5px]"
            style={{
              left: `${20 + i * 30}%`,
              top: `${25 + i * 20}%`,
              clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)',
              borderRadius: '0 0 50% 50%'
            }}
            animate={{
              rotate: [0, 360],
              scale: [0.8, 1.2, 0.8],
              opacity: [0.4, 0.8, 0.4]
            }}
            transition={{
              duration: 6 + i * 2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 1.5
            }}
          />
        ))}
      </div>

      {/* Enhanced Premium Header */}
      <div className="relative z-10 border-b border-accent-white/10 backdrop-blur-xl bg-gradient-to-r from-oven-black/80 via-oven-black/60 to-oven-black/80">
        <div className="absolute inset-0 bg-gradient-to-r from-sauce-red/5 via-cheese-gold/5 to-sauce-red/5" />
        
        <div className="relative p-6">
          <div className="flex items-center justify-between max-w-7xl mx-auto">
            <motion.div 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="flex items-center gap-6"
            >
              <motion.div 
                animate={{ 
                  rotate: [0, 5, -5, 0],
                  scale: [1, 1.05, 1]
                }} 
                transition={{ 
                  duration: 6, 
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="relative p-4 rounded-3xl bg-gradient-to-br from-sauce-red/25 to-cheese-gold/25 backdrop-blur-xl border border-cheese-gold/20 shadow-2xl"
              >
                {/* Pizza icon glow effect */}
                <motion.div
                  className="absolute inset-0 rounded-3xl bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 blur-xl"
                  animate={{
                    opacity: [0.3, 0.6, 0.3],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
                <Pizza className="relative h-10 w-10 text-cheese-gold drop-shadow-2xl" />
              </motion.div>
              
              <div>
                <motion.h1 
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="text-4xl font-black text-transparent bg-clip-text bg-gradient-to-r from-sauce-red via-cheese-gold to-sauce-red drop-shadow-2xl"
                  style={{
                    textShadow: '0 0 30px rgba(255, 140, 0, 0.3), 0 0 60px rgba(220, 38, 38, 0.2)'
                  }}
                >
                  Welcome back, {user?.full_name || 'Builder'}!
                </motion.h1>
                <motion.div 
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="flex items-center gap-4 mt-2"
                >
                  <p className="text-accent-white/80 text-lg font-medium">
                    {currentStage ? `${currentStage.name} Stage` : 'Getting Started'}
                  </p>
                  {stageConfidence > 0 && (
                    <Badge className="bg-gradient-to-r from-cheese-gold/20 to-orange-500/20 text-cheese-gold border-cheese-gold/30 text-sm font-semibold shadow-lg">
                      {stageConfidence}% Confidence
                    </Badge>
                  )}
                </motion.div>
              </div>
            </motion.div>

            <motion.div 
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6 }}
              className="flex items-center gap-4"
            >
              {/* Enhanced Refresh Dashboard Button */}
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowRefreshModal(true)}
                  disabled={isRefreshing}
                  className="relative overflow-hidden border-cheese-gold/40 text-cheese-gold hover:bg-cheese-gold/15 backdrop-blur-sm shadow-xl group"
                >
                  {/* Button glow effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-cheese-gold/10 to-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    animate={{
                      x: ['-100%', '100%']
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                  />
                  {isRefreshing ? (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <Loader2 className="h-4 w-4 mr-2" />
                    </motion.div>
                  ) : (
                    <Zap className="h-4 w-4 mr-2 relative z-10" />
                  )}
                  <span className="relative z-10">{isRefreshing ? 'Refreshing...' : 'Refresh Dashboard'}</span>
                </Button>
              </motion.div>

              {/* Populate Journey Button */}
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={populateJourneyFromOnboarding}
                  disabled={isPopulatingJourney}
                  className="relative overflow-hidden border-purple-500/40 text-purple-400 hover:bg-purple-500/15 backdrop-blur-sm shadow-xl group"
                >
                  {isPopulatingJourney ? (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <Loader2 className="h-4 w-4 mr-2" />
                    </motion.div>
                  ) : (
                    <Rocket className="h-4 w-4 mr-2 relative z-10" />
                  )}
                  <span className="relative z-10">{isPopulatingJourney ? 'Populating...' : 'Populate Journey'}</span>
                </Button>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button variant="outline" size="sm" asChild>
                  <Link to="/profile" className="border-accent-white/30 text-accent-white hover:bg-accent-white/15 backdrop-blur-sm shadow-lg">
                    <User className="h-4 w-4 mr-2" />
                    Profile
                  </Link>
                </Button>
              </motion.div>
              
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={signOut}
                  className="border-sauce-red/40 text-sauce-red hover:bg-sauce-red/15 backdrop-blur-sm shadow-lg"
                >
                  Sign Out
                </Button>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Enhanced Main Content with Better Tab Navigation */}
      <div className="relative z-10 flex-1 p-6">
        <div className="max-w-7xl mx-auto">
          <Tabs value={activeTab} onValueChange={(value) => {
            const newTab = value as 'overview' | 'journey' | 'progress';
            const oldTab = activeTab;
            setActiveTab(newTab);

            // Track tab change activity
            activityTrackingService.trackTabChange(oldTab, newTab);
          }} className="space-y-6">
            <TabsList className="grid w-full grid-cols-3 bg-oven-black/60 backdrop-blur-xl border border-accent-white/20 rounded-2xl p-2 shadow-2xl h-16">
              <TabsTrigger
                value="overview"
                className="flex items-center justify-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-sauce-red/40 data-[state=active]:to-cheese-gold/40 data-[state=active]:text-accent-white data-[state=active]:border data-[state=active]:border-cheese-gold/30 text-accent-white/70 font-semibold py-3 px-4 rounded-xl transition-all duration-300 h-12"
              >
                <House className="h-4 w-4 flex-shrink-0" />
                <span className="truncate">Overview</span>
              </TabsTrigger>

              <TabsTrigger
                value="journey"
                className="flex items-center justify-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500/40 data-[state=active]:to-pink-500/40 data-[state=active]:text-accent-white data-[state=active]:border data-[state=active]:border-purple-400/30 text-accent-white/70 font-semibold py-3 px-4 rounded-xl transition-all duration-300 h-12"
              >
                <Rocket className="h-4 w-4 flex-shrink-0" />
                <span className="truncate">Dev Journey</span>
              </TabsTrigger>

              <TabsTrigger
                value="progress"
                className="flex items-center justify-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500/40 data-[state=active]:to-cyan-500/40 data-[state=active]:text-accent-white data-[state=active]:border data-[state=active]:border-blue-400/30 text-accent-white/70 font-semibold py-3 px-4 rounded-xl transition-all duration-300 h-12"
              >
                <TrendingUp className="h-4 w-4 flex-shrink-0" />
                <span className="truncate">Progress</span>
              </TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="mt-8 space-y-8">
              <div className="grid lg:grid-cols-3 gap-8">
                {/* Enhanced Daily Update Card */}
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="lg:col-span-2"
                >
                  <Card className="relative overflow-hidden bg-gradient-to-br from-accent-white/15 to-accent-white/8 backdrop-blur-2xl border border-accent-white/25 shadow-2xl rounded-3xl group">
                    {/* Premium background effects */}
                    <div className="absolute inset-0 bg-gradient-to-r from-sauce-red/8 via-cheese-gold/8 to-sauce-red/8 opacity-60" />
                    
                    {/* Animated glow border */}
                    <motion.div
                      className="absolute inset-0 rounded-3xl"
                      style={{
                        background: 'linear-gradient(45deg, transparent, rgba(255, 140, 0, 0.1), transparent, rgba(220, 38, 38, 0.1), transparent)',
                        backgroundSize: '300% 300%'
                      }}
                      animate={{
                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
                      }}
                      transition={{
                        duration: 6,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                    />
                    
                    <CardHeader className="relative z-10">
                      <div className="flex items-center gap-4">
                        <motion.div 
                          className="relative p-4 rounded-2xl bg-gradient-to-br from-sauce-red/25 to-cheese-gold/25 backdrop-blur-xl shadow-2xl"
                          whileHover={{ scale: 1.1, rotate: 5 }}
                          transition={{ type: "spring", stiffness: 300 }}
                        >
                          {/* Icon glow effect */}
                          <motion.div
                            className="absolute inset-0 rounded-2xl bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 blur-xl"
                            animate={{
                              opacity: [0.4, 0.8, 0.4],
                              scale: [1, 1.2, 1]
                            }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              ease: "easeInOut"
                            }}
                          />
                          <Flame className="relative h-7 w-7 text-cheese-gold drop-shadow-xl" />
                        </motion.div>
                        <div>
                          <CardTitle className="text-2xl text-accent-white font-bold drop-shadow-lg">Daily Check-in</CardTitle>
                          <CardDescription className="text-accent-white/70 text-base">
                            Share your progress and get AI-powered coaching insights
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="relative z-10 space-y-6">
                      <Textarea
                        value={dailyUpdate}
                        onChange={(e) => setDailyUpdate(e.target.value)}
                        placeholder="What did you work on today? Any challenges or wins?"
                        className="bg-oven-black/60 border-accent-white/25 text-accent-white placeholder:text-accent-white/60 min-h-[140px] resize-none rounded-2xl backdrop-blur-sm shadow-lg text-base leading-relaxed"
                      />

                      {/* Quick Questions Section */}
                      {quickQuestions.length > 0 && (
                        <div className="space-y-3">
                          <div className="flex items-center gap-2">
                            <Sparkles className="h-4 w-4 text-purple-400" />
                            <span className="text-sm font-medium text-purple-400">Quick reflection questions:</span>
                          </div>
                          <div className="space-y-2">
                            {quickQuestions.map((question, index) => (
                              <motion.button
                                key={index}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                onClick={() => {
                                  const currentText = dailyUpdate;
                                  const newText = currentText ? `${currentText}\n\n${question}` : question;
                                  setDailyUpdate(newText);
                                }}
                                className="w-full text-left p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg hover:bg-purple-500/15 transition-all duration-200 group"
                              >
                                <div className="flex items-start gap-2">
                                  <div className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2 flex-shrink-0 group-hover:bg-purple-300" />
                                  <span className="text-sm text-accent-white/80 group-hover:text-accent-white">
                                    {question}
                                  </span>
                                </div>
                              </motion.button>
                            ))}
                          </div>
                          <p className="text-xs text-accent-white/50">
                            Click any question to add it to your check-in
                          </p>
                        </div>
                      )}

                      <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                        <Button
                          onClick={handleDailyUpdate}
                          disabled={!dailyUpdate.trim() || isSubmitting}
                          className="relative w-full bg-gradient-to-r from-sauce-red to-cheese-gold hover:from-sauce-red/90 hover:to-cheese-gold/90 text-white font-bold h-14 rounded-2xl shadow-2xl overflow-hidden group text-lg"
                        >
                          {/* Button shine effect */}
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 opacity-0 group-hover:opacity-100"
                            animate={{
                              x: ['-100%', '200%']
                            }}
                            transition={{
                              duration: 0.8,
                              ease: "easeInOut"
                            }}
                          />
                          
                          {isSubmitting ? (
                            <>
                              <motion.div
                                animate={{ rotate: 360 }}
                                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                className="relative z-10"
                              >
                                <Loader2 className="h-5 w-5 mr-3" />
                              </motion.div>
                              <span className="relative z-10">Analyzing...</span>
                            </>
                          ) : (
                            <>
                              <Send className="h-5 w-5 mr-3 relative z-10" />
                              <span className="relative z-10">Submit Update</span>
                            </>
                          )}
                        </Button>
                      </motion.div>

                      {/* Rate Limit Warning */}
                      {rateLimitWarning && (
                        <motion.div 
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg"
                        >
                          <p className="text-yellow-400 text-sm">{rateLimitWarning}</p>
                        </motion.div>
                      )}

                      {/* Follow-up Questions */}
                      <AnimatePresence>
                        {showFollowUp && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="border-t border-accent-white/10 pt-4 space-y-3"
                          >
                            <div className="flex items-start gap-3">
                              <Brain className="h-5 w-5 text-purple-400 mt-1 flex-shrink-0" />
                              <div className="space-y-2">
                                <p className="text-accent-white/90 text-sm font-medium">
                                  Let's dig deeper:
                                </p>
                                <div className="space-y-1">
                                  {followUpQuestions.map((question, idx) => (
                                    <MarkdownText key={idx} className="text-accent-white/70 text-sm">
                                      • {question}
                                    </MarkdownText>
                                  ))}
                                </div>
                              </div>
                            </div>
                            
                            <Textarea
                              value={followUpResponse}
                              onChange={(e) => setFollowUpResponse(e.target.value)}
                              placeholder="Share more details about these points..."
                              className="bg-oven-black/50 border-accent-white/20 text-accent-white placeholder:text-accent-white/50 min-h-[80px]"
                            />
                            
                            <div className="flex gap-2">
                              <Button
                                onClick={handleFollowUpResponse}
                                disabled={!followUpResponse.trim() || isSubmitting}
                                size="sm"
                                className="bg-purple-500/20 hover:bg-purple-500/30 text-purple-400 border border-purple-500/30"
                              >
                                Share More
                              </Button>
                              <Button
                                onClick={() => setShowFollowUp(false)}
                                size="sm"
                                variant="ghost"
                                className="text-accent-white/60 hover:text-accent-white"
                              >
                                Skip for now
                              </Button>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Enhanced AI Insight Card */}
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <Card className="relative overflow-hidden bg-gradient-to-br from-purple-500/15 to-pink-500/15 backdrop-blur-2xl border border-purple-500/25 shadow-2xl rounded-3xl h-full group">
                    {/* AI-themed background animation */}
                    <div className="absolute inset-0">
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10"
                        animate={{
                          opacity: [0.3, 0.6, 0.3],
                          scale: [1, 1.02, 1]
                        }}
                        transition={{
                          duration: 4,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />
                      
                      {/* Neural network-like pattern */}
                      <motion.div
                        className="absolute top-0 right-0 w-32 h-32 opacity-10"
                        animate={{
                          rotate: [0, 360]
                        }}
                        transition={{
                          duration: 20,
                          repeat: Infinity,
                          ease: "linear"
                        }}
                      >
                        <div className="w-full h-full border border-purple-400/30 rounded-full" />
                        <div className="absolute top-1/2 left-1/2 w-16 h-16 border border-pink-400/30 rounded-full -translate-x-1/2 -translate-y-1/2" />
                        <div className="absolute top-1/2 left-1/2 w-8 h-8 border border-purple-400/30 rounded-full -translate-x-1/2 -translate-y-1/2" />
                      </motion.div>
                    </div>
                    
                    <CardHeader className="relative z-10">
                      <div className="flex items-center gap-4">
                        <motion.div 
                          className="relative p-4 rounded-2xl bg-gradient-to-br from-purple-500/25 to-pink-500/25 backdrop-blur-xl shadow-2xl"
                          animate={{ 
                            boxShadow: [
                              "0 0 20px rgba(168, 85, 247, 0.3)",
                              "0 0 40px rgba(168, 85, 247, 0.5)",
                              "0 0 60px rgba(168, 85, 247, 0.3)",
                              "0 0 20px rgba(168, 85, 247, 0.3)"
                            ]
                          }}
                          transition={{ duration: 4, repeat: Infinity }}
                        >
                          <motion.div
                            className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 blur-lg"
                            animate={{
                              opacity: [0.4, 0.8, 0.4],
                              scale: [1, 1.3, 1]
                            }}
                            transition={{
                              duration: 2.5,
                              repeat: Infinity,
                              ease: "easeInOut"
                            }}
                          />
                          <Brain className="relative h-7 w-7 text-purple-400 drop-shadow-xl" />
                        </motion.div>
                        <div className="flex-1">
                          <CardTitle className="text-xl text-accent-white font-bold drop-shadow-lg">AI Coach</CardTitle>
                          <CardDescription className="text-accent-white/70 text-sm">
                            Personalized insights & challenges
                          </CardDescription>
                        </div>
                        {usingCachedInsight && (
                          <Badge variant="outline" className="text-xs text-green-400 border-green-400/30 bg-green-500/10">
                            📋 Cached
                          </Badge>
                        )}
                      </div>
                    </CardHeader>
                    
                    <CardContent className="relative z-10">
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.6 }}
                        className="relative"
                      >
                        {/* Typing indicator when AI is thinking */}
                        {isSubmitting && (
                          <motion.div 
                            className="flex items-center gap-2 text-purple-400 text-sm mb-3"
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                          >
                            <div className="flex gap-1">
                              {[0, 1, 2].map((i) => (
                                <motion.div
                                  key={i}
                                  className="w-2 h-2 bg-purple-400 rounded-full"
                                  animate={{
                                    scale: [1, 1.5, 1],
                                    opacity: [0.5, 1, 0.5]
                                  }}
                                  transition={{
                                    duration: 1,
                                    repeat: Infinity,
                                    delay: i * 0.2,
                                    ease: "easeInOut"
                                  }}
                                />
                              ))}
                            </div>
                            AI thinking...
                          </motion.div>
                        )}
                        
                        {loading && !aiInsight ? (
                          <div className="space-y-2">
                            <SkeletonLoader className="h-4 w-full" />
                            <SkeletonLoader className="h-4 w-3/4" />
                            <SkeletonLoader className="h-4 w-1/2" />
                          </div>
                        ) : (
                          <MarkdownText className="text-accent-white/85 text-sm leading-relaxed">
                            {aiInsight || "Complete your daily check-in to get personalized AI coaching insights!"}
                          </MarkdownText>
                        )}
                      </motion.div>
                    </CardContent>
                  </Card>
                </motion.div>
              </div>

              {/* Quick Stats */}
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="grid md:grid-cols-4 gap-6"
              >
                {[
                  { label: 'Current Stage', value: currentStage?.name || 'Getting Started', icon: Target, color: 'from-blue-500/20 to-purple-500/20' },
                  { label: 'Recent Updates', value: recentUpdates.length, icon: Calendar, color: 'from-green-500/20 to-emerald-500/20' },
                  { label: 'Stage Progress', value: `${stageConfidence}%`, icon: TrendingUp, color: 'from-yellow-500/20 to-orange-500/20' },
                  { label: 'Track', value: user?.track || 'Not Set', icon: Star, color: 'from-pink-500/20 to-red-500/20' }
                ].map((stat, idx) => (
                  <motion.div
                    key={idx}
                    whileHover={{ scale: 1.05, y: -5 }}
                    className={`p-6 rounded-2xl bg-gradient-to-br ${stat.color} backdrop-blur-xl border border-accent-white/10 shadow-xl`}
                  >
                    <div className="flex items-center gap-3">
                      <stat.icon className="h-6 w-6 text-cheese-gold" />
                      <div>
                        <p className="text-accent-white/60 text-sm">{stat.label}</p>
                        <p className="text-accent-white font-bold text-lg">{stat.value}</p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </TabsContent>

            {/* Dev Journey Tab - ENHANCED TRADITIONAL WITH ADAPTIVE INTELLIGENCE */}
            <TabsContent value="journey" className="mt-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-accent-white mb-2">Your Development Journey</h2>
                  <p className="text-accent-white/60">Enhanced with adaptive intelligence and personalized insights</p>
                  {adaptiveJourney && (
                    <div className="flex justify-center gap-6 mt-4 text-sm">
                      <div className="text-center">
                        <p className="text-lg font-bold text-blue-400">Stage {adaptiveJourney.current_effective_stage}</p>
                        <p className="text-accent-white/70">Current Focus</p>
                      </div>
                      <div className="text-center">
                        <p className="text-lg font-bold text-green-400">{adaptiveJourney.completed_stages.length}</p>
                        <p className="text-accent-white/70">Completed</p>
                      </div>
                      <div className="text-center">
                        <p className="text-lg font-bold text-purple-400">{adaptiveJourney.confidence_score}%</p>
                        <p className="text-accent-white/70">Confidence</p>
                      </div>
                    </div>
                  )}
                  <Button
                    onClick={() => {
                      console.log('🔄 Refreshing adaptive analysis...');
                      localStorage.removeItem(`dashboard_loaded_${user?.id}_${new Date().toDateString()}`);
                      fetchDashboardData();
                    }}
                    variant="outline"
                    size="sm"
                    className="mt-3"
                  >
                    🔄 Refresh Analysis
                  </Button>
                </div>

                {adaptiveJourney ? (
                  <div className="space-y-4">
                    {/* Adaptive Insights Summary */}
                    {adaptiveJourney.next_recommended_actions.length > 0 && (
                      <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg p-4 border border-blue-400/20">
                        <h4 className="text-sm font-medium text-accent-white mb-2 flex items-center gap-2">
                          <Zap className="h-4 w-4 text-yellow-400" />
                          Recommended Next Actions
                        </h4>
                        <div className="text-sm text-accent-white/80 space-y-1">
                          {adaptiveJourney.next_recommended_actions.slice(0, 2).map((action, index) => (
                            <p key={index}>• {action}</p>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Enhanced Traditional Journey */}
                    {allStages.map((stage) => {
                      const stageAnalysis = adaptiveJourney.stage_analyses.find(s => s.stage_id === stage.id);
                      if (!stageAnalysis) return null;

                      return (
                        <AdaptiveDevJourneyStageTab
                          key={stage.id}
                          stage={stage}
                          stageAnalysis={stageAnalysis}
                          isCurrentStage={stage.id === adaptiveJourney.current_effective_stage}
                          isCompleted={adaptiveJourney.completed_stages.includes(stage.id)}
                          userId={user?.id || ''}
                          onStageUpdate={() => {
                            console.log('Stage updated - refreshing adaptive journey');
                            refreshAdaptiveJourney();
                          }}
                        />
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin text-accent-white/60 mx-auto mb-4" />
                    <p className="text-accent-white/60">Analyzing your journey progress...</p>
                  </div>
                )}

                {/* Fallback to traditional view if needed */}
                <details className="mt-8">
                  <summary className="text-accent-white/70 cursor-pointer hover:text-accent-white transition-colors">
                    View Traditional Stage Progression
                  </summary>
                  <div className="space-y-4 mt-4">
                    {allStages.map((stage, idx) => (
                      <DevJourneyStageTab
                        key={stage.id}
                        stage={stage}
                        isCurrentStage={stage.id === userStage}
                        isCompleted={stage.id < userStage}
                        userId={user?.id || ''}
                        onStageUpdate={() => {
                          console.log('Stage updated - refreshing adaptive journey');
                          refreshAdaptiveJourney();
                        }}
                      />
                    ))}
                  </div>
                </details>
              </motion.div>
            </TabsContent>

            {/* Progress Tab */}
            <TabsContent value="progress" className="mt-8">
              <ModernProgressTab
                userId={user.id}
                currentStage={userStage}
                stageConfidence={stageConfidence}
                onStageUpdate={() => {
                  setRefreshTrigger(prev => prev + 1);
                }}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Contextual Help Widget */}
      <ContextualHelpWidget userStage={userStage} />

      {/* Dashboard Refresh Modal */}
      <DashboardRefreshModal
        isOpen={showRefreshModal}
        onClose={() => setShowRefreshModal(false)}
        userId={user?.id || ''}
        onRefreshComplete={handleRefreshComplete}
      />


    </div>
  );
};

export default Dashboard; 