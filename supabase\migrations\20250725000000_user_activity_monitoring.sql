-- User Activity Monitoring System
-- Comprehensive activity logging for admin dashboard monitoring

-- 1. Create user_activity_logs table for tracking all user interactions
CREATE TABLE IF NOT EXISTS user_activity_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  activity_type VARCHAR NOT NULL,
  activity_data JSONB NOT NULL DEFAULT '{}'::jsonb,
  
  -- Session and browser information
  session_id VARCHAR,
  ip_address INET,
  user_agent TEXT,
  
  -- Page and navigation context
  page_url VARCHAR,
  referrer VARCHAR,
  
  -- Timing and metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'::jsonb
);

-- 2. Create indexes for optimal query performance
CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON user_activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_activity_type ON user_activity_logs(activity_type);
CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON user_activity_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_logs_session_id ON user_activity_logs(session_id);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_activity_logs_user_type_date ON user_activity_logs(user_id, activity_type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_logs_type_date ON user_activity_logs(activity_type, created_at DESC);

-- 3. Create activity_type_definitions table for managing activity types
CREATE TABLE IF NOT EXISTS activity_type_definitions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  activity_type VARCHAR UNIQUE NOT NULL,
  display_name VARCHAR NOT NULL,
  description TEXT,
  category VARCHAR NOT NULL, -- 'authentication', 'navigation', 'onboarding', 'dev_journey', 'ai_interaction', 'profile', 'admin'
  color_code VARCHAR DEFAULT '#6B7280', -- Hex color for UI display
  icon_name VARCHAR, -- Icon identifier for UI
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Insert default activity type definitions
INSERT INTO activity_type_definitions (activity_type, display_name, description, category, color_code, icon_name) VALUES
-- Authentication activities
('login', 'User Login', 'User successfully logged into the platform', 'authentication', '#10B981', 'LogIn'),
('logout', 'User Logout', 'User logged out of the platform', 'authentication', '#EF4444', 'LogOut'),
('signup', 'User Signup', 'New user account created', 'authentication', '#3B82F6', 'UserPlus'),

-- Navigation activities
('page_visit', 'Page Visit', 'User visited a page', 'navigation', '#6B7280', 'Eye'),
('tab_change', 'Tab Change', 'User switched tabs in dashboard', 'navigation', '#8B5CF6', 'ArrowRight'),
('route_change', 'Route Change', 'User navigated to different route', 'navigation', '#06B6D4', 'Navigation'),

-- Onboarding activities
('onboarding_start', 'Onboarding Started', 'User began onboarding process', 'onboarding', '#F59E0B', 'Play'),
('onboarding_step', 'Onboarding Step', 'User completed an onboarding step', 'onboarding', '#F59E0B', 'CheckCircle'),
('onboarding_complete', 'Onboarding Complete', 'User finished onboarding process', 'onboarding', '#10B981', 'Award'),

-- Dev Journey activities
('milestone_view', 'Milestone Viewed', 'User viewed a dev journey milestone', 'dev_journey', '#8B5CF6', 'Target'),
('milestone_submit', 'Milestone Submitted', 'User submitted milestone content', 'dev_journey', '#10B981', 'Send'),
('stage_progress', 'Stage Progress', 'User progressed to new dev journey stage', 'dev_journey', '#3B82F6', 'TrendingUp'),

-- AI Interaction activities
('ai_analysis_request', 'AI Analysis Request', 'User requested AI analysis', 'ai_interaction', '#EC4899', 'Brain'),
('ai_insight_view', 'AI Insight Viewed', 'User viewed AI-generated insights', 'ai_interaction', '#EC4899', 'Lightbulb'),
('refresh_analysis', 'Analysis Refresh', 'User refreshed AI analysis', 'ai_interaction', '#EC4899', 'RefreshCw'),

-- Profile activities
('profile_update', 'Profile Updated', 'User updated their profile', 'profile', '#F97316', 'User'),
('profile_view', 'Profile Viewed', 'User viewed their profile', 'profile', '#F97316', 'Eye'),

-- Daily Update activities
('daily_update_submit', 'Daily Update Submitted', 'User submitted daily update', 'daily_update', '#84CC16', 'Edit'),
('daily_update_view', 'Daily Update Viewed', 'User viewed daily updates', 'daily_update', '#84CC16', 'Calendar'),

-- Help activities
('help_request', 'Help Request', 'User requested help or support', 'help', '#EF4444', 'HelpCircle'),
('help_view', 'Help Viewed', 'User viewed help content', 'help', '#EF4444', 'BookOpen'),

-- Admin activities
('admin_login', 'Admin Login', 'Admin user logged into dashboard', 'admin', '#DC2626', 'Shield'),
('admin_action', 'Admin Action', 'Admin performed an action', 'admin', '#DC2626', 'Settings');

-- 5. Create activity_summary_stats table for caching summary statistics
CREATE TABLE IF NOT EXISTS activity_summary_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  date_period DATE NOT NULL, -- Daily aggregation
  activity_type VARCHAR NOT NULL,
  user_count INTEGER DEFAULT 0,
  total_activities INTEGER DEFAULT 0,
  unique_sessions INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(date_period, activity_type)
);

-- Index for summary stats
CREATE INDEX IF NOT EXISTS idx_activity_summary_date ON activity_summary_stats(date_period DESC);
CREATE INDEX IF NOT EXISTS idx_activity_summary_type ON activity_summary_stats(activity_type);

-- 6. Function to update summary statistics
CREATE OR REPLACE FUNCTION update_activity_summary_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- Update daily summary for the activity type
  INSERT INTO activity_summary_stats (date_period, activity_type, user_count, total_activities, unique_sessions)
  SELECT 
    DATE(NEW.created_at),
    NEW.activity_type,
    COUNT(DISTINCT user_id),
    COUNT(*),
    COUNT(DISTINCT session_id)
  FROM user_activity_logs 
  WHERE DATE(created_at) = DATE(NEW.created_at) 
    AND activity_type = NEW.activity_type
  ON CONFLICT (date_period, activity_type) 
  DO UPDATE SET
    user_count = EXCLUDED.user_count,
    total_activities = EXCLUDED.total_activities,
    unique_sessions = EXCLUDED.unique_sessions,
    updated_at = NOW();
    
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7. Create trigger for automatic summary updates
CREATE TRIGGER update_activity_summary_trigger
  AFTER INSERT ON user_activity_logs
  FOR EACH ROW EXECUTE FUNCTION update_activity_summary_stats();

-- 8. Function for data retention (cleanup old activity logs)
CREATE OR REPLACE FUNCTION cleanup_old_activity_logs(retention_days INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM user_activity_logs 
  WHERE created_at < NOW() - INTERVAL '1 day' * retention_days;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- Also cleanup old summary stats (keep 1 year)
  DELETE FROM activity_summary_stats 
  WHERE date_period < CURRENT_DATE - INTERVAL '365 days';
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 9. RLS Policies for security
ALTER TABLE user_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_type_definitions ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_summary_stats ENABLE ROW LEVEL SECURITY;

-- Admin-only access to activity logs
CREATE POLICY "Admin can view all activity logs" ON user_activity_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.email IN ('<EMAIL>', '<EMAIL>') -- Add admin emails
    )
  );

-- Users can only view their own activity logs (optional, for user privacy dashboard)
CREATE POLICY "Users can view own activity logs" ON user_activity_logs
  FOR SELECT USING (auth.uid() = user_id);

-- Admin-only access to activity type definitions
CREATE POLICY "Admin can manage activity types" ON activity_type_definitions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.email IN ('<EMAIL>', '<EMAIL>')
    )
  );

-- Admin-only access to summary stats
CREATE POLICY "Admin can view summary stats" ON activity_summary_stats
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.email IN ('<EMAIL>', '<EMAIL>')
    )
  );

-- 10. Create helpful views for common queries
CREATE OR REPLACE VIEW recent_user_activity AS
SELECT 
  ual.*,
  u.full_name,
  u.email,
  u.track,
  atd.display_name as activity_display_name,
  atd.category,
  atd.color_code,
  atd.icon_name
FROM user_activity_logs ual
JOIN users u ON ual.user_id = u.id
JOIN activity_type_definitions atd ON ual.activity_type = atd.activity_type
WHERE ual.created_at >= NOW() - INTERVAL '7 days'
ORDER BY ual.created_at DESC;

CREATE OR REPLACE VIEW daily_activity_summary AS
SELECT 
  DATE(ual.created_at) as activity_date,
  atd.category,
  atd.display_name,
  COUNT(*) as total_count,
  COUNT(DISTINCT ual.user_id) as unique_users,
  COUNT(DISTINCT ual.session_id) as unique_sessions
FROM user_activity_logs ual
JOIN activity_type_definitions atd ON ual.activity_type = atd.activity_type
WHERE ual.created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(ual.created_at), atd.category, atd.display_name
ORDER BY activity_date DESC, total_count DESC;

-- 11. Comments for documentation
COMMENT ON TABLE user_activity_logs IS 'Comprehensive logging of all user activities for admin monitoring';
COMMENT ON TABLE activity_type_definitions IS 'Definitions and metadata for different types of user activities';
COMMENT ON TABLE activity_summary_stats IS 'Cached daily summary statistics for activity monitoring performance';
COMMENT ON FUNCTION cleanup_old_activity_logs IS 'Maintenance function to remove old activity logs based on retention policy';
