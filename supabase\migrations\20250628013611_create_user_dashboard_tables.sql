-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email VARCHAR UNIQUE NOT NULL,
  full_name VARCHAR NOT NULL,
  track VARCHAR CHECK (track IN ('newbie', 'builder', 'scaler')),
  onboarding_completed B<PERSON><PERSON>EA<PERSON> DEFAULT false,
  notion_workspace_id VARCHAR,
  discord_user_id VARCHAR,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_projects table
CREATE TABLE IF NOT EXISTS user_projects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR NOT NULL,
  description TEXT,
  current_stage VARCHAR,
  track VARCHAR CHECK (track IN ('newbie', 'builder', 'scaler')),
  weekly_goals JSONB DEFAULT '[]'::jsonb,
  progress_data JSONB DEFAULT '{}'::jsonb,
  notion_page_id VARCHAR,
  discord_thread_id VARCHAR,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create daily_updates table
CREATE TABLE IF NOT EXISTS daily_updates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES user_projects(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  ai_insights JSONB DEFAULT '{}'::jsonb,
  sentiment_score FLOAT,
  recommended_resources JSONB DEFAULT '[]'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create ai_conversations table
CREATE TABLE IF NOT EXISTS ai_conversations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  conversation_type VARCHAR CHECK (conversation_type IN ('onboarding', 'daily_update', 'help')),
  messages JSONB DEFAULT '[]'::jsonb,
  context JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create pie_fi_mentors table
CREATE TABLE IF NOT EXISTS pie_fi_mentors (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR NOT NULL,
  expertise TEXT[] DEFAULT '{}',
  bio TEXT,
  availability VARCHAR,
  email VARCHAR,
  discord_handle VARCHAR,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create pie_fi_resources table
CREATE TABLE IF NOT EXISTS pie_fi_resources (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title VARCHAR NOT NULL,
  description TEXT,
  resource_type VARCHAR CHECK (resource_type IN ('tool', 'template', 'guide', 'course')),
  url VARCHAR,
  tags TEXT[] DEFAULT '{}',
  track_relevance VARCHAR[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_user_projects_user_id ON user_projects(user_id);
CREATE INDEX idx_daily_updates_user_id ON daily_updates(user_id);
CREATE INDEX idx_daily_updates_project_id ON daily_updates(project_id);
CREATE INDEX idx_ai_conversations_user_id ON ai_conversations(user_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_projects_updated_at BEFORE UPDATE ON user_projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_updates ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_conversations ENABLE ROW LEVEL SECURITY;

-- Create policies for users table
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- Create policies for user_projects table
CREATE POLICY "Users can view their own projects" ON user_projects
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own projects" ON user_projects
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own projects" ON user_projects
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own projects" ON user_projects
    FOR DELETE USING (auth.uid() = user_id);

-- Create policies for daily_updates table
CREATE POLICY "Users can view their own updates" ON daily_updates
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own updates" ON daily_updates
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create policies for ai_conversations table
CREATE POLICY "Users can view their own conversations" ON ai_conversations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own conversations" ON ai_conversations
    FOR INSERT WITH CHECK (auth.uid() = user_id); 