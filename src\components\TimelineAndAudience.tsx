import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Target, Rocket, Users, Calendar, Trophy, Lightbulb, Clock, Gauge, Award, CheckCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { useScrollAnimation, scrollVariants, staggerContainer } from '@/hooks/useScrollAnimation';

const timelineItems = [
  { 
    phase: 'Applications Open', 
    date: 'July 8th', 
    description: 'Submit your application and team info',
    icon: Users,
    color: 'from-blue-400 to-blue-600'
  },
  { 
    phase: 'Selection & Team Formation', 
    date: 'July 15th', 
    description: 'Review process and squad matching',
    icon: Target,
    color: 'from-green-400 to-green-600'
  },
  { 
    phase: 'Kickoff & Orientation', 
    date: 'July  22nd', 
    description: 'Program launch and resource setup',
    icon: Rocket,
    color: 'from-purple-400 to-purple-600'
  },
  { 
    phase: '10-Week Sprint', 
    date: 'July - September 27th', 
    description: 'Weekly mentor sessions, midpoint check-in, shareouts',
    icon: Calendar,
    color: 'from-orange-400 to-orange-600'
  },
  { 
    phase: 'Demo Day', 
    date: 'September 27th', 
    description: 'Present to investors, media, and community',
    icon: Trophy,
    color: 'from-red-400 to-red-600'
  }
];

const audiencePoints = [
  {
    text: 'Passionate builders hungry to launch and create impact',
    icon: Rocket,
    color: 'text-blue-400'
  },
  {
    text: 'Willing to show up, build in public, and support peers',
    icon: Users,
    color: 'text-green-400'
  },
  {
    text: 'You have a project idea or unique skillset to contribute',
    icon: Lightbulb,
    color: 'text-yellow-400'
  },
  {
    text: 'Committed to a 10-week sprint with no tourists, no ghosting',
    icon: Target,
    color: 'text-red-400'
  }
];

const TimelineAndAudience = () => {
  const { ref, isInView } = useScrollAnimation();
  const { ref: timelineRef, isInView: timelineInView } = useScrollAnimation();
  const { ref: audienceRef, isInView: audienceInView } = useScrollAnimation();
  const isMobile = typeof window !== 'undefined' && window.innerWidth <= 768;

  return (
    <section className="relative py-16 sm:py-24 lg:py-32 overflow-hidden bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black" ref={ref}>
      {/* Simplified background for mobile */}
      <div className="absolute inset-0">
        {(isMobile ? [...Array(8)] : [...Array(20)]).map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={!isMobile ? {
              y: [-20, 20, -20],
              x: [-10, 10, -10],
              opacity: [0.2, 0.6, 0.2],
              scale: [0.8, 1.2, 0.8],
            } : undefined}
            transition={!isMobile ? {
              duration: 8 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            } : undefined}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Timeline Section */}
        <motion.div
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={scrollVariants}
          className="text-center mb-8 sm:mb-12 lg:mb-20"
        >
          <motion.div 
            className="inline-flex items-center gap-2 px-4 py-2 sm:px-6 sm:py-3 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 rounded-full border border-sauce-red/20 mb-4 sm:mb-6 lg:mb-8"
            whileHover={!isMobile ? { scale: 1.05 } : undefined}
          >
            <Calendar className="w-3 h-3 sm:w-4 sm:h-4 text-sauce-red" />
            <span className="text-sauce-red font-semibold text-sm sm:text-base">Program Timeline</span>
          </motion.div>
          
          <h2 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-black text-accent-white mb-4 sm:mb-6 px-2">
            Your{" "}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
              10-Week Journey
            </span>
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-crust-beige/80 max-w-3xl mx-auto px-4">
            From application to Demo Day, here's how your startup transformation unfolds
          </p>
        </motion.div>

        {/* Timeline Items - Simplified Layout */}
        <motion.div 
          className="grid gap-4 sm:gap-6 lg:gap-8 max-w-5xl mx-auto mb-16 sm:mb-20 lg:mb-32"
          variants={staggerContainer}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          ref={timelineRef}
        >
          {timelineItems.map((item, index) => (
            <motion.div 
              key={item.phase}
              className="group relative"
              variants={scrollVariants}
            >
              <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20 hover:border-accent-white/40 transition-all duration-300">
                <CardContent className="p-4 sm:p-6 lg:p-8">
                  <div className="flex items-start gap-4 sm:gap-6">
                    {/* Icon */}
                    <motion.div 
                      className={`flex-shrink-0 w-12 h-12 sm:w-16 sm:h-16 rounded-xl bg-gradient-to-r ${item.color} flex items-center justify-center shadow-lg`}
                      whileHover={!isMobile ? { scale: 1.1, rotate: 5 } : undefined}
                      transition={{ duration: 0.3 }}
                    >
                      <item.icon className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                    </motion.div>
                    
                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2 sm:mb-3">
                        <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-accent-white">
                          {item.phase}
                        </h3>
                        <span className="text-sm sm:text-base text-cheese-gold font-semibold">
                          {item.date}
                        </span>
                      </div>
                      <p className="text-sm sm:text-base lg:text-lg text-crust-beige/80 leading-relaxed">
                        {item.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Audience Section */}
        <motion.div
          initial="hidden"
          animate={audienceInView ? "visible" : "hidden"}
          variants={scrollVariants}
          className="text-center mb-12 lg:mb-16"
          ref={audienceRef}
        >
          <motion.div 
            className="inline-flex items-center gap-2 px-4 py-2 sm:px-6 sm:py-3 bg-gradient-to-r from-cheese-gold/10 to-sauce-red/10 rounded-full border border-cheese-gold/20 mb-4 sm:mb-6 lg:mb-8"
            whileHover={!isMobile ? { scale: 1.05 } : undefined}
          >
            <Target className="w-3 h-3 sm:w-4 sm:h-4 text-cheese-gold" />
            <span className="text-cheese-gold font-semibold text-sm sm:text-base">Perfect For</span>
          </motion.div>
          
          <h2 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-black text-accent-white mb-4 sm:mb-6 px-2">
            Is{" "}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-cheese-gold to-sauce-red">
              Pie Fi
            </span>
            {" "}for You?
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-crust-beige/80 max-w-3xl mx-auto px-4">
            We're looking for committed builders ready to ship something great
          </p>
        </motion.div>

        {/* Audience Points */}
        <motion.div 
          className="grid sm:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 max-w-5xl mx-auto"
          variants={staggerContainer}
          initial="hidden"
          animate={audienceInView ? "visible" : "hidden"}
        >
          {audiencePoints.map((point, index) => (
            <motion.div 
              key={index}
              className="group"
              variants={scrollVariants}
            >
              <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20 hover:border-accent-white/40 transition-all duration-300 h-full">
                <CardContent className="p-4 sm:p-6 lg:p-8">
                  <div className="flex items-start gap-3 sm:gap-4">
                    <motion.div 
                      className="flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 rounded-lg bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 flex items-center justify-center"
                      whileHover={!isMobile ? { scale: 1.1 } : undefined}
                    >
                      <point.icon className={`w-4 h-4 sm:w-5 sm:h-5 ${point.color}`} />
                    </motion.div>
                    <p className="text-sm sm:text-base lg:text-lg text-crust-beige/90 leading-relaxed flex-1">
                      {point.text}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default TimelineAndAudience;
