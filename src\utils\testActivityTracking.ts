// Test utility for activity tracking service
import { activityTrackingService } from '@/services/activityTrackingService';

export const testActivityTracking = async () => {
  console.log('🧪 Testing Activity Tracking Service...');
  
  try {
    // Test basic activity tracking
    await activityTrackingService.trackActivity('page_visit', {
      action: 'test_page_visit',
      target: 'test_page',
      test: true
    });
    
    console.log('✅ Basic activity tracking test passed');
    
    // Test convenience methods
    await activityTrackingService.trackPageVisit('test_dashboard', {
      test: true,
      timestamp: new Date().toISOString()
    });
    
    console.log('✅ Page visit tracking test passed');
    
    // Test AI analysis tracking
    await activityTrackingService.trackAIAnalysisRequest('test_analysis', {
      test: true,
      content_length: 100
    });
    
    console.log('✅ AI analysis tracking test passed');
    
    // Force flush to ensure data is sent
    await activityTrackingService.forceFlush();
    
    console.log('✅ All activity tracking tests passed!');
    
    return true;
  } catch (error) {
    console.error('❌ Activity tracking test failed:', error);
    return false;
  }
};

// Test function to generate sample activity data
export const generateSampleActivityData = async () => {
  console.log('🎲 Generating sample activity data...');
  
  const sampleActivities = [
    { type: 'login', data: { action: 'user_login', method: 'email' } },
    { type: 'page_visit', data: { action: 'page_view', target: 'dashboard' } },
    { type: 'tab_change', data: { action: 'tab_switch', from: 'overview', to: 'journey' } },
    { type: 'daily_update_submit', data: { action: 'daily_update_submitted', content_length: 150 } },
    { type: 'ai_analysis_request', data: { action: 'ai_analysis_requested', analysis_type: 'daily_update' } },
    { type: 'milestone_submit', data: { action: 'milestone_submitted', stage_id: 1, milestone_id: 'problem_definition' } },
    { type: 'profile_update', data: { action: 'profile_updated', updated_fields: ['full_name'] } },
    { type: 'help_request', data: { action: 'help_requested', topic: 'dev_journey' } }
  ];
  
  try {
    for (const activity of sampleActivities) {
      await activityTrackingService.trackActivity(activity.type as any, activity.data);
      // Small delay to spread out timestamps
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    await activityTrackingService.forceFlush();
    console.log('✅ Sample activity data generated successfully!');
    return true;
  } catch (error) {
    console.error('❌ Failed to generate sample data:', error);
    return false;
  }
};

// Function to test admin activity retrieval
export const testAdminActivityRetrieval = async () => {
  console.log('🔍 Testing admin activity retrieval...');
  
  try {
    const activities = await activityTrackingService.getRecentActivities(10);
    console.log('📊 Retrieved activities:', activities.length);
    
    const activityTypes = await activityTrackingService.getActivityTypeDefinitions();
    console.log('📋 Retrieved activity types:', activityTypes.length);
    
    console.log('✅ Admin activity retrieval test passed!');
    return { activities, activityTypes };
  } catch (error) {
    console.error('❌ Admin activity retrieval test failed:', error);
    return null;
  }
};

// Utility to run all tests
export const runAllActivityTests = async () => {
  console.log('🚀 Running comprehensive activity tracking tests...');
  
  const results = {
    basicTracking: await testActivityTracking(),
    sampleData: await generateSampleActivityData(),
    adminRetrieval: await testAdminActivityRetrieval()
  };
  
  const allPassed = Object.values(results).every(result => result !== false && result !== null);
  
  if (allPassed) {
    console.log('🎉 All activity tracking tests passed!');
  } else {
    console.log('⚠️ Some activity tracking tests failed. Check the logs above.');
  }
  
  return results;
};

// Export for use in browser console or components
if (typeof window !== 'undefined') {
  (window as any).testActivityTracking = {
    testActivityTracking,
    generateSampleActivityData,
    testAdminActivityRetrieval,
    runAllActivityTests
  };
}
