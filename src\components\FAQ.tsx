import React, { useState } from 'react';
import { ChevronDown, Sparkles, Zap, Star } from 'lucide-react';
import { motion, AnimatePresence, Variants } from 'framer-motion';
import { useScrollAnimation, mobileOptimizedVariants, staggerContainer } from '@/hooks/useScrollAnimation';

const faqData = [
  {
    question: 'Do I need an idea/team to apply?',
    answer: 'No, individuals welcome! We\'ll help you squad up. We believe great teams can form during the program, and solo builders often bring unique perspectives that complement existing teams.',
    icon: Sparkles,
    gradient: 'from-blue-400 to-purple-500'
  },
  {
    question: 'Do I have to be from Santa Cruz?',
    answer: 'Not at all! We welcome anyone from the Bay Area who can make it to Santa Cruz for our regular events and weekly activities. While we\'re based in Santa Cruz, our community includes builders from all across the region who are excited to be part of something special.',
    icon: Zap,
    gradient: 'from-green-400 to-blue-500'
  },
  {
    question: 'Is funding guaranteed?',
    answer: 'No, but we\'ll work with you to get what you need to build. Small grants are available on a needs basis to cover essential development costs, but the real value is in the mentorship, resources, and community.',
    icon: Star,
    gradient: 'from-yellow-400 to-orange-500'
  },
  {
    question: 'Do I have to be technical?',
    answer: 'No, but technical chops are a big plus. We value diverse skill sets including design, business development, marketing, and domain expertise. Great products need great teams with complementary skills.',
    icon: Sparkles,
    gradient: 'from-purple-400 to-pink-500'
  },
  {
    question: 'What happens at Demo Day?',
    answer: 'Demo Day isn\'t just a showcase. It\'s a real shot to win investors, users, and media attention. We bring operators, angels, and talent together to meet the builders. This is your launchpad.',
    icon: Zap,
    gradient: 'from-teal-400 to-blue-500'
  },
  {
    question: 'Is this just another accelerator?',
    answer: 'No. We\'re building a repeatable, sustainable model for founder talent with low-burn operations, partner support, and pay-it-forward from builder alumni. This is about community, not just capital.',
    icon: Star,
    gradient: 'from-rose-400 to-red-500'
  }
];

const FAQ = () => {
  const [openItems, setOpenItems] = useState<number[]>([0, 1, 2, 3, 4, 5]); // All items expanded by default
  const { ref, isInView } = useScrollAnimation();
  
  // Check if mobile for conditional rendering
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  // Optimized mobile-friendly accordion animation
  const accordionVariants: Variants = {
    closed: { 
      height: 0, 
      opacity: 0,
      transition: { 
        duration: isMobile ? 0.2 : 0.3, 
        ease: "easeInOut"
      }
    },
    open: { 
      height: "auto", 
      opacity: 1,
      transition: { 
        duration: isMobile ? 0.25 : 0.35, 
        ease: "easeInOut"
      }
    }
  };

  // Simplified content animation for mobile
  const contentVariants: Variants = {
    closed: { y: -10, opacity: 0 },
    open: { 
      y: 0, 
      opacity: 1,
      transition: { 
        duration: isMobile ? 0.2 : 0.3, 
        delay: isMobile ? 0.1 : 0.15,
        ease: "easeOut"
      }
    }
  };

  return (
    <section className="relative py-32 overflow-hidden bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black" ref={ref} id="faq">
      {/* Simplified background elements for mobile */}
      <div className="absolute inset-0">
        {!isMobile && [...Array(12)].map((_, i) => ( // Reduced from 20 to 12
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              willChange: 'transform, opacity'
            }}
            animate={{
              y: [-15, 15, -15], // Reduced movement
              x: [-5, 5, -5], // Reduced movement
              opacity: [0.2, 0.6, 0.2],
              scale: [0.7, 1.2, 0.7],
            }}
            transition={{
              duration: 8 + Math.random() * 3, // Longer, smoother animations
              repeat: Infinity,
              delay: Math.random() * 2,
              ease: "easeInOut"
            }}
          />
        ))}
        
        {/* Static particles for mobile */}
        {isMobile && [...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-sauce-red/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
          />
        ))}
      </div>

      {/* Simplified floating geometric shapes - desktop only */}
      {!isMobile && (
        <div className="absolute inset-0 overflow-hidden">
          <motion.div
            className="absolute top-20 left-10 w-16 h-16 border border-sauce-red/30 rounded-full"
            style={{ willChange: 'transform' }}
            animate={{
              rotate: [0, 360],
              scale: [1, 1.1, 1],
              opacity: [0.3, 0.5, 0.3],
            }}
            transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
          />
          <motion.div
            className="absolute bottom-32 right-20 w-12 h-12 bg-gradient-to-r from-cheese-gold/20 to-sauce-red/20 rotate-45"
            style={{ willChange: 'transform' }}
            animate={{
              rotate: [45, 225, 45],
              y: [-10, 10, -10],
            }}
            transition={{ duration: 20, repeat: Infinity, ease: "easeInOut" }}
          />
        </div>
      )}

      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={mobileOptimizedVariants}
          className="text-center mb-20"
        >
          <motion.div 
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 rounded-full border border-sauce-red/20 mb-6 backdrop-blur-sm"
            whileHover={!isMobile ? { scale: 1.05, borderColor: "rgba(255, 71, 71, 0.4)" } : {}}
            style={{ willChange: 'transform' }}
          >
            <Sparkles className="w-4 h-4 text-sauce-red" />
            <span className="text-sauce-red font-semibold">Got Questions?</span>
          </motion.div>
          
          <h2 className="text-5xl sm:text-6xl font-black text-accent-white mb-6">
            Frequently Asked{" "}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
              Questions
            </span>
          </h2>
          <p className="text-xl text-crust-beige/80 max-w-3xl mx-auto">
            Everything you need to know about Pie Fi
          </p>
        </motion.div>
        
        <motion.div 
          className="space-y-6"
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={staggerContainer}
        >
          {faqData.map((item, index) => (
            <motion.div 
              key={index} 
              className="group relative"
              variants={mobileOptimizedVariants}
              whileHover={!isMobile ? { y: -2 } : {}}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
              style={{ willChange: 'transform' }}
            >
              {/* Simplified glow effect - desktop only */}
              {!isMobile && (
                <div className={`absolute -inset-1 bg-gradient-to-r ${item.gradient} rounded-3xl blur-xl opacity-0 group-hover:opacity-30 transition-opacity duration-300`} />
              )}
              
              {/* Main card with optimized styling */}
              <div className="relative bg-accent-white/10 backdrop-blur-xl rounded-3xl border border-accent-white/20 overflow-hidden shadow-xl">
                {/* Simplified background gradient - desktop only */}
                {!isMobile && (
                  <motion.div
                    className={`absolute inset-0 bg-gradient-to-r ${item.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-300`}
                  />
                )}
                
                <motion.button
                  onClick={() => toggleItem(index)}
                  className="relative z-10 w-full p-6 sm:p-8 text-left flex items-center justify-between hover:bg-accent-white/5 transition-colors duration-200"
                  whileTap={{ scale: 0.99 }}
                  style={{ willChange: 'transform' }}
                >
                  <div className="flex items-center gap-4 sm:gap-6">
                    {/* Optimized 3D Icon */}
                    <motion.div 
                      className={`p-3 sm:p-4 rounded-2xl bg-gradient-to-r ${item.gradient} shadow-lg group-hover:shadow-xl transition-shadow duration-200`}
                      whileHover={!isMobile ? { scale: 1.05 } : {}}
                      transition={{ duration: 0.2 }}
                      style={{ willChange: 'transform' }}
                    >
                      <item.icon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                    </motion.div>
                    
                    <h3 className="font-bold text-lg sm:text-xl text-accent-white group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-sauce-red group-hover:to-cheese-gold transition-all duration-200">
                      {item.question}
                    </h3>
                  </div>
                  
                  <motion.div
                    animate={{ rotate: openItems.includes(index) ? 180 : 0 }}
                    transition={{ duration: 0.2, type: "spring", stiffness: 200, damping: 15 }}
                    className={`p-2 rounded-full bg-gradient-to-r ${item.gradient} shadow-lg flex-shrink-0`}
                    style={{ willChange: 'transform' }}
                  >
                    <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                  </motion.div>
                </motion.button>
                
                <AnimatePresence mode="wait">
                  {openItems.includes(index) && (
                    <motion.div
                      key={`content-${index}`}
                      initial="closed"
                      animate="open"
                      exit="closed"
                      variants={accordionVariants}
                      className="overflow-hidden relative z-10"
                      style={{ willChange: 'height, opacity' }}
                    >
                      <motion.div
                        variants={contentVariants}
                        className="px-6 pb-6 sm:px-8 sm:pb-8"
                      >
                        <div className="flex items-start gap-4 sm:gap-6">
                          <div className="w-11 sm:w-14 flex-shrink-0" /> {/* Spacer to align with icon */}
                          <p className="text-base sm:text-lg text-crust-beige/90 leading-relaxed">
                            {item.answer}
                          </p>
                        </div>
                      </motion.div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Optimized call to action */}
        <motion.div 
          className="mt-20 text-center"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <motion.div
            className="relative inline-block"
            whileHover={!isMobile ? { scale: 1.02 } : {}}
            style={{ willChange: 'transform' }}
          >
            {!isMobile && (
              <div className="absolute inset-0 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-full blur-lg opacity-50" />
            )}
            <motion.p 
              className="relative text-xl sm:text-2xl md:text-3xl font-bold text-accent-white px-6 py-3 sm:px-8 sm:py-4 bg-oven-black rounded-full border border-sauce-red/30"
              whileHover={!isMobile ? {
                background: "linear-gradient(135deg, rgba(255,71,71,0.1) 0%, rgba(255,210,63,0.1) 100%)",
              } : {}}
            >
              Still have questions?{" "}
              <a 
                href="https://www.instagram.com/piefisantacruz/" 
                target="_blank"
                rel="noopener noreferrer"
                className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold hover:from-cheese-gold hover:to-sauce-red transition-all duration-300"
              >
                Let's chat!
              </a>
            </motion.p>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default FAQ;
