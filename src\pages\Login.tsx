import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Pizza, ArrowRight, Zap, Sparkles } from 'lucide-react';
import { motion } from 'framer-motion';

const Login = () => {
  const navigate = useNavigate();
  const { signIn, signOut } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Check for logout parameter
  React.useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('logout') === 'true') {
      signOut();
      // Clear the URL parameter
      window.history.replaceState({}, document.title, '/login');
    }
  }, [signOut]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccessMessage('');
    setLoading(true);

    try {
      const { data, error } = await signIn(email, password);
      
      if (error) {
        setError(error.message);
        return;
      }

      if (data?.user) {
        // Check if email is confirmed
        if (!data.user.email_confirmed_at) {
          setError('Please check your email and click the confirmation link before signing in.');
          return;
        }

        setSuccessMessage('Sign in successful! Redirecting...');
        setTimeout(() => {
          navigate('/dashboard');
        }, 1000);
      }
    } catch (error: any) {
      console.error('Login error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-oven-black relative overflow-hidden flex items-center justify-center">
      {/* Animated Background Elements - matching Hero design */}
      <div className="absolute inset-0">
        <motion.div 
          className="absolute top-1/4 right-1/4 w-[600px] h-[600px] rounded-full bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        
        <motion.div 
          className="absolute bottom-1/4 left-1/4 w-[500px] h-[500px] rounded-full bg-gradient-to-r from-purple-500/15 to-blue-500/15 blur-2xl"
          animate={{
            scale: [1.2, 1, 1.2],
            x: [0, 50, 0],
            y: [0, -30, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Floating geometric shapes */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className={`absolute w-4 h-4 ${i % 3 === 0 ? 'bg-cheese-gold/30' : i % 3 === 1 ? 'bg-sauce-red/30' : 'bg-purple-400/30'} rounded-full`}
            style={{
              left: `${20 + (i * 10)}%`,
              top: `${25 + (i * 8)}%`,
            }}
            animate={{
              y: [-20, 20, -20],
              x: [-10, 10, -10],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 4 + i,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.5,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 w-full max-w-md px-6">
        <motion.div
          initial={{ opacity: 0, y: 30, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8 }}
        >
          {/* Logo and Title Section */}
          <div className="text-center mb-8">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="mb-6"
            >
              <motion.div
                animate={{ rotate: [0, 5, -5, 0] }}
                transition={{ duration: 3, repeat: Infinity }}
                className="flex justify-center mb-4"
              >
                <Pizza className="h-16 w-16 text-cheese-gold" />
              </motion.div>
              <h1 className="text-4xl md:text-5xl font-black text-transparent bg-clip-text bg-gradient-to-r from-sauce-red via-cheese-gold to-sauce-red tracking-tight mb-2"
                  style={{ filter: "drop-shadow(0 4px 12px rgba(255, 71, 71, 0.3))" }}>
                Pie Fi
              </h1>
              <motion.p 
                className="text-lg text-accent-white/80"
                animate={{ opacity: [0.7, 1, 0.7] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                Welcome back, Builder!
              </motion.p>
            </motion.div>
          </div>

          {/* Login Card */}
          <Card className="bg-gradient-to-br from-accent-white/10 to-accent-white/5 backdrop-blur-sm border border-accent-white/20 shadow-2xl">
            <CardHeader className="space-y-1 pb-6">
              <motion.div
                className="inline-flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 backdrop-blur-sm border border-sauce-red/20 rounded-full text-sauce-red font-semibold text-sm mb-4 mx-auto"
              >
                <Sparkles className="w-4 h-4" />
                Sign in to continue building
                <Sparkles className="w-4 h-4" />
              </motion.div>
              
              <CardTitle className="text-2xl text-center text-accent-white font-bold">
                Access Your Dashboard
              </CardTitle>
              <CardDescription className="text-center text-accent-white/70 text-base">
                Continue your entrepreneurial journey
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-accent-white font-medium">
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    required
                    className="h-12 bg-oven-black/50 border-accent-white/30 text-accent-white placeholder:text-accent-white/50 focus:border-cheese-gold/50 focus:ring-cheese-gold/20 text-base"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-accent-white font-medium">
                    Password
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="h-12 bg-oven-black/50 border-accent-white/30 text-accent-white focus:border-cheese-gold/50 focus:ring-cheese-gold/20 text-base"
                  />
                </div>
                
                {error && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <Alert className="bg-sauce-red/10 border-sauce-red/20 border">
                      <AlertDescription className="text-sauce-red">
                        {error}
                      </AlertDescription>
                    </Alert>
                  </motion.div>
                )}

                {successMessage && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <Alert className="bg-green-500/10 border-green-500/20 border">
                      <AlertDescription className="text-green-400">
                        {successMessage}
                      </AlertDescription>
                    </Alert>
                  </motion.div>
                )}

                <motion.div 
                  whileHover={{ scale: 1.02 }} 
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    type="submit"
                    disabled={loading}
                    className="w-full h-12 bg-gradient-to-r from-sauce-red to-cheese-gold hover:from-sauce-red/90 hover:to-cheese-gold/90 text-white font-semibold text-lg shadow-lg transition-all duration-300"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="mr-3 h-5 w-5 animate-spin" />
                        Signing you in...
                      </>
                    ) : (
                      <>
                        <Zap className="mr-3 h-5 w-5" />
                        Sign In
                      </>
                    )}
                  </Button>
                </motion.div>
              </form>

              {/* Sign Up Link */}
              <div className="mt-8 text-center">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-accent-white/20" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-4 bg-gradient-to-r from-accent-white/5 to-accent-white/10 text-accent-white/60 rounded-full">
                      New to Pie Fi?
                    </span>
                  </div>
                </div>
                
                <motion.div 
                  whileHover={{ scale: 1.05 }}
                  className="mt-4"
                >
                  <Link
                    to="/register"
                    className="inline-flex items-center gap-2 text-cheese-gold hover:text-cheese-gold/80 font-semibold transition-colors group"
                  >
                    Create your account
                    <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </motion.div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default Login; 