// User Progress Analytics Service
// Provides meaningful user journey insights for admin dashboard

import { supabaseAdmin } from '@/integrations/supabase/admin-client';

export interface UserProgressMetrics {
  user_id: string;
  full_name: string;
  email: string;
  track: 'newbie' | 'builder' | 'scaler';
  onboarding_completed: boolean;
  current_stage: number;
  current_confidence: number;
  days_in_current_stage: number;
  
  // Milestone metrics
  total_milestones_submitted: number;
  approved_milestones: number;
  stages_with_submissions: number;
  last_milestone_date: string | null;
  avg_submission_length: number;
  milestones_with_ai_feedback: number;
  
  // Daily update metrics
  total_daily_updates: number;
  last_update_date: string | null;
  avg_sentiment: number;
  updates_last_7_days: number;
  updates_last_30_days: number;
  
  // AI interaction metrics
  total_ai_interactions: number;
  ai_insights_received: number;
  last_ai_interaction: string | null;
  
  // Calculated indicators
  progress_status: 'Active' | 'Onboarded - No Progress' | 'Onboarding In Progress' | 'Not Started' | 'Unknown';
  engagement_score: number;
  risk_level: 'High' | 'Medium' | 'Low';
  success_category: 'High Performer' | 'On Track' | 'Engaged' | 'Needs Attention';
}

export interface UserNeedingIntervention {
  user_id: string;
  full_name: string;
  email: string;
  track: string;
  risk_level: string;
  reason: string;
  days_since_last_activity: number;
  recommended_action: string;
}

export interface SuccessStory {
  user_id: string;
  full_name: string;
  email: string;
  track: string;
  success_category: string;
  current_stage: number;
  total_milestones_submitted: number;
  approved_milestones: number;
  engagement_score: number;
  days_to_current_stage: number;
  success_metrics: string;
}

export interface CohortAnalytics {
  track: string;
  cohort_week: string;
  total_users: number;
  completed_onboarding: number;
  started_journey: number;
  reached_stage_2: number;
  reached_stage_4: number;
  high_performers: number;
  high_risk_users: number;
  avg_engagement_score: number;
  avg_confidence: number;
}

export interface ProgressSummary {
  track: string;
  total_users: number;
  onboarding_completed_count: number;
  avg_engagement_score: number;
  high_risk_count: number;
  high_performer_count: number;
  progressed_users: number;
  avg_confidence: number;
}

export interface StageProgressionData {
  user_id: string;
  stage_id: number;
  stage_entered_at: string;
  confidence_score: number;
  previous_stage: number | null;
  days_in_previous_stage: number | null;
  track: string;
  full_name: string;
  stage_velocity: number;
  progression_pace: 'Fast' | 'Normal' | 'Slow' | 'Stuck';
}

export interface DashboardInsights {
  totalUsers: number;
  activeUsers: number;
  usersNeedingHelp: number;
  highPerformers: number;
  avgEngagementScore: number;
  onboardingCompletionRate: number;
  stageDistribution: { stage: number; count: number; percentage: number }[];
  riskDistribution: { level: string; count: number; percentage: number }[];
  trackPerformance: { track: string; avgScore: number; userCount: number }[];
}

class UserProgressAnalyticsService {
  
  /**
   * Get comprehensive user progress metrics
   */
  async getUserProgressMetrics(limit: number = 100): Promise<UserProgressMetrics[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('user_progress_analytics')
        .select('*')
        .order('engagement_score', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching user progress metrics:', error);
      return [];
    }
  }

  /**
   * Get users who need intervention
   */
  async getUsersNeedingIntervention(): Promise<UserNeedingIntervention[]> {
    try {
      const { data, error } = await supabaseAdmin
        .rpc('get_users_needing_intervention');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching users needing intervention:', error);
      return [];
    }
  }

  /**
   * Get success stories
   */
  async getSuccessStories(): Promise<SuccessStory[]> {
    try {
      const { data, error } = await supabaseAdmin
        .rpc('get_success_stories');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching success stories:', error);
      return [];
    }
  }

  /**
   * Get cohort analytics
   */
  async getCohortAnalytics(): Promise<CohortAnalytics[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('user_cohort_analytics')
        .select('*')
        .order('cohort_week', { ascending: false })
        .limit(20);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching cohort analytics:', error);
      return [];
    }
  }

  /**
   * Get progress summary by track
   */
  async getProgressSummary(): Promise<ProgressSummary[]> {
    try {
      // Refresh materialized view first
      await supabaseAdmin.rpc('refresh_progress_summary');
      
      const { data, error } = await supabaseAdmin
        .from('user_progress_summary')
        .select('*')
        .order('total_users', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching progress summary:', error);
      return [];
    }
  }

  /**
   * Get stage progression analytics
   */
  async getStageProgressionData(): Promise<StageProgressionData[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('stage_progression_analytics')
        .select('*')
        .order('stage_entered_at', { ascending: false })
        .limit(200);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching stage progression data:', error);
      return [];
    }
  }

  /**
   * Get dashboard insights summary
   */
  async getDashboardInsights(): Promise<DashboardInsights> {
    try {
      const [progressMetrics, progressSummary] = await Promise.all([
        this.getUserProgressMetrics(1000),
        this.getProgressSummary()
      ]);

      const totalUsers = progressMetrics.length;
      const activeUsers = progressMetrics.filter(u => u.progress_status === 'Active').length;
      const usersNeedingHelp = progressMetrics.filter(u => u.risk_level === 'High').length;
      const highPerformers = progressMetrics.filter(u => u.success_category === 'High Performer').length;
      const avgEngagementScore = progressMetrics.reduce((sum, u) => sum + u.engagement_score, 0) / totalUsers;
      const onboardingCompletionRate = progressMetrics.filter(u => u.onboarding_completed).length / totalUsers * 100;

      // Stage distribution
      const stageDistribution = Array.from({ length: 6 }, (_, stage) => {
        const count = progressMetrics.filter(u => u.current_stage === stage).length;
        return {
          stage,
          count,
          percentage: totalUsers > 0 ? (count / totalUsers) * 100 : 0
        };
      });

      // Risk distribution
      const riskLevels = ['Low', 'Medium', 'High'];
      const riskDistribution = riskLevels.map(level => {
        const count = progressMetrics.filter(u => u.risk_level === level).length;
        return {
          level,
          count,
          percentage: totalUsers > 0 ? (count / totalUsers) * 100 : 0
        };
      });

      // Track performance
      const trackPerformance = progressSummary.map(summary => ({
        track: summary.track,
        avgScore: summary.avg_engagement_score,
        userCount: summary.total_users
      }));

      return {
        totalUsers,
        activeUsers,
        usersNeedingHelp,
        highPerformers,
        avgEngagementScore: Math.round(avgEngagementScore * 10) / 10,
        onboardingCompletionRate: Math.round(onboardingCompletionRate * 10) / 10,
        stageDistribution,
        riskDistribution,
        trackPerformance
      };
    } catch (error) {
      console.error('Error generating dashboard insights:', error);
      return {
        totalUsers: 0,
        activeUsers: 0,
        usersNeedingHelp: 0,
        highPerformers: 0,
        avgEngagementScore: 0,
        onboardingCompletionRate: 0,
        stageDistribution: [],
        riskDistribution: [],
        trackPerformance: []
      };
    }
  }

  /**
   * Get detailed user profile for intervention
   */
  async getUserDetailedProfile(userId: string): Promise<UserProgressMetrics | null> {
    try {
      const { data, error } = await supabaseAdmin
        .from('user_progress_analytics')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching user detailed profile:', error);
      return null;
    }
  }

  /**
   * Get engagement trends over time
   */
  async getEngagementTrends(days: number = 30): Promise<{ date: string; avgEngagement: number; activeUsers: number }[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('daily_updates')
        .select(`
          created_at,
          user_id,
          sentiment_score
        `)
        .gte('created_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: true });

      if (error) throw error;

      // Group by date and calculate metrics
      const dailyMetrics = new Map<string, { sentiments: number[]; users: Set<string> }>();
      
      data?.forEach(update => {
        const date = new Date(update.created_at).toISOString().split('T')[0];
        if (!dailyMetrics.has(date)) {
          dailyMetrics.set(date, { sentiments: [], users: new Set() });
        }
        const dayData = dailyMetrics.get(date)!;
        dayData.sentiments.push(update.sentiment_score || 0.5);
        dayData.users.add(update.user_id);
      });

      return Array.from(dailyMetrics.entries()).map(([date, data]) => ({
        date,
        avgEngagement: data.sentiments.reduce((sum, s) => sum + s, 0) / data.sentiments.length * 100,
        activeUsers: data.users.size
      }));
    } catch (error) {
      console.error('Error fetching engagement trends:', error);
      return [];
    }
  }

  /**
   * Get milestone completion rates by stage
   */
  async getMilestoneCompletionRates(): Promise<{ stage: number; totalUsers: number; completedMilestones: number; completionRate: number }[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('milestone_submissions')
        .select(`
          stage_id,
          user_id,
          is_approved
        `);

      if (error) throw error;

      // Group by stage and calculate completion rates
      const stageMetrics = new Map<number, { users: Set<string>; completed: Set<string> }>();
      
      data?.forEach(submission => {
        if (!stageMetrics.has(submission.stage_id)) {
          stageMetrics.set(submission.stage_id, { users: new Set(), completed: new Set() });
        }
        const stageData = stageMetrics.get(submission.stage_id)!;
        stageData.users.add(submission.user_id);
        if (submission.is_approved) {
          stageData.completed.add(submission.user_id);
        }
      });

      return Array.from(stageMetrics.entries()).map(([stage, data]) => ({
        stage,
        totalUsers: data.users.size,
        completedMilestones: data.completed.size,
        completionRate: data.users.size > 0 ? (data.completed.size / data.users.size) * 100 : 0
      }));
    } catch (error) {
      console.error('Error fetching milestone completion rates:', error);
      return [];
    }
  }
}

// Export singleton instance
export const userProgressAnalytics = new UserProgressAnalyticsService();
export default userProgressAnalytics;
