import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  Users, 
  AlertTriangle, 
  Star, 
  Target,
  Brain,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw,
  Download,
  UserCheck,
  Zap,
  Award,
  Activity,
  BarChart3,
  PieChart
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { userProgressAnalytics, DashboardInsights, UserProgressMetrics, UserNeedingIntervention, SuccessStory } from '@/services/userProgressAnalytics';

const AdminUserProgressDashboard: React.FC = () => {
  const [insights, setInsights] = useState<DashboardInsights | null>(null);
  const [usersNeedingHelp, setUsersNeedingHelp] = useState<UserNeedingIntervention[]>([]);
  const [successStories, setSuccessStories] = useState<SuccessStory[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const { toast } = useToast();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      const [dashboardInsights, interventionUsers, successUsers] = await Promise.all([
        userProgressAnalytics.getDashboardInsights(),
        userProgressAnalytics.getUsersNeedingIntervention(),
        userProgressAnalytics.getSuccessStories()
      ]);

      setInsights(dashboardInsights);
      setUsersNeedingHelp(interventionUsers);
      setSuccessStories(successUsers);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast({
        title: "Error",
        description: "Failed to load progress analytics",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'High': return 'text-red-400 bg-red-500/20 border-red-500/30';
      case 'Medium': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      case 'Low': return 'text-green-400 bg-green-500/20 border-green-500/30';
      default: return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  const getSuccessColor = (category: string) => {
    switch (category) {
      case 'High Performer': return 'text-purple-400 bg-purple-500/20 border-purple-500/30';
      case 'On Track': return 'text-blue-400 bg-blue-500/20 border-blue-500/30';
      case 'Engaged': return 'text-green-400 bg-green-500/20 border-green-500/30';
      default: return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  const getTrackColor = (track: string) => {
    switch (track) {
      case 'newbie': return 'text-green-400 bg-green-500/20';
      case 'builder': return 'text-blue-400 bg-blue-500/20';
      case 'scaler': return 'text-purple-400 bg-purple-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <RefreshCw className="h-8 w-8 text-accent-white/60 animate-spin" />
        <span className="ml-3 text-accent-white/60">Loading progress analytics...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-accent-white mb-2">User Progress Analytics</h2>
          <p className="text-crust-beige/80">Comprehensive insights into user journey success and intervention needs</p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={loadDashboardData}
            variant="outline"
            size="sm"
            className="bg-accent-white/10 border-accent-white/30 text-accent-white hover:bg-accent-white/20"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            onClick={() => {
              // Export functionality can be added here
              toast({ title: "Export", description: "Export functionality coming soon!" });
            }}
            variant="outline"
            size="sm"
            className="bg-accent-white/10 border-accent-white/30 text-accent-white hover:bg-accent-white/20"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      {insights && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
            <Card className="bg-gradient-to-br from-blue-500/10 to-blue-600/10 border-blue-500/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-400 text-sm font-medium">Total Users</p>
                    <p className="text-3xl font-bold text-accent-white">{insights.totalUsers}</p>
                    <p className="text-xs text-blue-400/60 mt-1">{insights.activeUsers} active</p>
                  </div>
                  <Users className="h-12 w-12 text-blue-400" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }}>
            <Card className="bg-gradient-to-br from-red-500/10 to-red-600/10 border-red-500/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-red-400 text-sm font-medium">Need Help</p>
                    <p className="text-3xl font-bold text-accent-white">{insights.usersNeedingHelp}</p>
                    <p className="text-xs text-red-400/60 mt-1">Require intervention</p>
                  </div>
                  <AlertTriangle className="h-12 w-12 text-red-400" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3 }}>
            <Card className="bg-gradient-to-br from-purple-500/10 to-purple-600/10 border-purple-500/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-400 text-sm font-medium">High Performers</p>
                    <p className="text-3xl font-bold text-accent-white">{insights.highPerformers}</p>
                    <p className="text-xs text-purple-400/60 mt-1">Success stories</p>
                  </div>
                  <Star className="h-12 w-12 text-purple-400" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.4 }}>
            <Card className="bg-gradient-to-br from-green-500/10 to-green-600/10 border-green-500/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-400 text-sm font-medium">Avg Engagement</p>
                    <p className="text-3xl font-bold text-accent-white">{insights.avgEngagementScore}%</p>
                    <p className="text-xs text-green-400/60 mt-1">Overall score</p>
                  </div>
                  <TrendingUp className="h-12 w-12 text-green-400" />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-accent-white/10 backdrop-blur-md border border-accent-white/20 rounded-2xl p-2 h-14">
          <TabsTrigger 
            value="overview" 
            className="flex items-center justify-center gap-2 data-[state=active]:bg-sauce-red data-[state=active]:text-white text-accent-white font-medium rounded-xl transition-all duration-300 h-10"
          >
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger 
            value="intervention" 
            className="flex items-center justify-center gap-2 data-[state=active]:bg-sauce-red data-[state=active]:text-white text-accent-white font-medium rounded-xl transition-all duration-300 h-10"
          >
            <AlertTriangle className="h-4 w-4" />
            Need Help
          </TabsTrigger>
          <TabsTrigger 
            value="success" 
            className="flex items-center justify-center gap-2 data-[state=active]:bg-sauce-red data-[state=active]:text-white text-accent-white font-medium rounded-xl transition-all duration-300 h-10"
          >
            <Award className="h-4 w-4" />
            Success Stories
          </TabsTrigger>
          <TabsTrigger 
            value="analytics" 
            className="flex items-center justify-center gap-2 data-[state=active]:bg-sauce-red data-[state=active]:text-white text-accent-white font-medium rounded-xl transition-all duration-300 h-10"
          >
            <PieChart className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {insights && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Stage Distribution */}
              <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
                <CardHeader>
                  <CardTitle className="text-xl font-bold text-accent-white flex items-center gap-2">
                    <Target className="h-6 w-6 text-sauce-red" />
                    Stage Distribution
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {insights.stageDistribution.map((stage, index) => (
                    <div key={stage.stage} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-accent-white font-medium">
                          Stage {stage.stage} {stage.stage === 0 ? '(Not Started)' : ''}
                        </span>
                        <span className="text-accent-white/80">{stage.count} users ({stage.percentage.toFixed(1)}%)</span>
                      </div>
                      <div className="w-full bg-accent-white/20 rounded-full h-2">
                        <div 
                          className="bg-gradient-to-r from-sauce-red to-cheese-gold h-2 rounded-full transition-all duration-500"
                          style={{ width: `${stage.percentage}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Risk Distribution */}
              <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
                <CardHeader>
                  <CardTitle className="text-xl font-bold text-accent-white flex items-center gap-2">
                    <AlertTriangle className="h-6 w-6 text-sauce-red" />
                    Risk Assessment
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {insights.riskDistribution.map((risk) => (
                    <div key={risk.level} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-accent-white font-medium">{risk.level} Risk</span>
                        <span className="text-accent-white/80">{risk.count} users ({risk.percentage.toFixed(1)}%)</span>
                      </div>
                      <div className="w-full bg-accent-white/20 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all duration-500 ${
                            risk.level === 'High' ? 'bg-red-500' :
                            risk.level === 'Medium' ? 'bg-yellow-500' : 'bg-green-500'
                          }`}
                          style={{ width: `${risk.percentage}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="intervention" className="space-y-6">
          <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-accent-white flex items-center gap-2">
                <AlertTriangle className="h-6 w-6 text-sauce-red" />
                Users Needing Intervention ({usersNeedingHelp.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {usersNeedingHelp.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
                  <p className="text-accent-white/60">All users are progressing well! No immediate interventions needed.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {usersNeedingHelp.map((user) => (
                    <div key={user.user_id} className="bg-accent-white/5 rounded-lg p-4 border border-accent-white/10">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="font-semibold text-accent-white">{user.full_name}</h3>
                            <Badge className={`text-xs ${getRiskColor(user.risk_level)}`}>
                              {user.risk_level} Risk
                            </Badge>
                            <Badge className={`text-xs ${getTrackColor(user.track)}`}>
                              {user.track}
                            </Badge>
                          </div>
                          <p className="text-sm text-accent-white/80 mb-2">{user.email}</p>
                          <p className="text-sm text-accent-white/70 mb-3">{user.reason}</p>
                          <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-3">
                            <p className="text-sm text-blue-400 font-medium mb-1">Recommended Action:</p>
                            <p className="text-sm text-accent-white">{user.recommended_action}</p>
                          </div>
                        </div>
                        <div className="text-right text-sm text-accent-white/60">
                          <p>{user.days_since_last_activity} days</p>
                          <p>since last activity</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="success" className="space-y-6">
          <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-accent-white flex items-center gap-2">
                <Award className="h-6 w-6 text-sauce-red" />
                Success Stories ({successStories.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {successStories.length === 0 ? (
                <div className="text-center py-8">
                  <Star className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
                  <p className="text-accent-white/60">No success stories yet. Users are still building momentum!</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {successStories.map((user) => (
                    <div key={user.user_id} className="bg-accent-white/5 rounded-lg p-4 border border-accent-white/10">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="font-semibold text-accent-white">{user.full_name}</h3>
                            <Badge className={`text-xs ${getSuccessColor(user.success_category)}`}>
                              {user.success_category}
                            </Badge>
                            <Badge className={`text-xs ${getTrackColor(user.track)}`}>
                              {user.track}
                            </Badge>
                          </div>
                          <p className="text-sm text-accent-white/80 mb-2">{user.email}</p>
                          <p className="text-sm text-accent-white/70 mb-3">{user.success_metrics}</p>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                            <div className="text-center">
                              <p className="text-lg font-bold text-accent-white">{user.current_stage}</p>
                              <p className="text-xs text-accent-white/60">Current Stage</p>
                            </div>
                            <div className="text-center">
                              <p className="text-lg font-bold text-accent-white">{user.total_milestones_submitted}</p>
                              <p className="text-xs text-accent-white/60">Milestones</p>
                            </div>
                            <div className="text-center">
                              <p className="text-lg font-bold text-accent-white">{user.engagement_score}%</p>
                              <p className="text-xs text-accent-white/60">Engagement</p>
                            </div>
                            <div className="text-center">
                              <p className="text-lg font-bold text-accent-white">{Math.round(user.days_to_current_stage)}</p>
                              <p className="text-xs text-accent-white/60">Days Active</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {insights && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Track Performance */}
              <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
                <CardHeader>
                  <CardTitle className="text-xl font-bold text-accent-white flex items-center gap-2">
                    <Users className="h-6 w-6 text-sauce-red" />
                    Track Performance
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {insights.trackPerformance.map((track) => (
                    <div key={track.track} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          <Badge className={`text-xs ${getTrackColor(track.track)}`}>
                            {track.track}
                          </Badge>
                          <span className="text-accent-white font-medium">{track.userCount} users</span>
                        </div>
                        <span className="text-accent-white/80">{track.avgScore.toFixed(1)}% avg engagement</span>
                      </div>
                      <div className="w-full bg-accent-white/20 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-sauce-red to-cheese-gold h-2 rounded-full transition-all duration-500"
                          style={{ width: `${track.avgScore}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Key Metrics Summary */}
              <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
                <CardHeader>
                  <CardTitle className="text-xl font-bold text-accent-white flex items-center gap-2">
                    <Activity className="h-6 w-6 text-sauce-red" />
                    Key Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-accent-white/5 rounded-lg">
                      <p className="text-2xl font-bold text-accent-white">{insights.onboardingCompletionRate.toFixed(1)}%</p>
                      <p className="text-sm text-accent-white/60">Onboarding Completion</p>
                    </div>
                    <div className="text-center p-4 bg-accent-white/5 rounded-lg">
                      <p className="text-2xl font-bold text-accent-white">{((insights.highPerformers / insights.totalUsers) * 100).toFixed(1)}%</p>
                      <p className="text-sm text-accent-white/60">High Performers</p>
                    </div>
                    <div className="text-center p-4 bg-accent-white/5 rounded-lg">
                      <p className="text-2xl font-bold text-accent-white">{((insights.activeUsers / insights.totalUsers) * 100).toFixed(1)}%</p>
                      <p className="text-sm text-accent-white/60">Active Users</p>
                    </div>
                    <div className="text-center p-4 bg-accent-white/5 rounded-lg">
                      <p className="text-2xl font-bold text-accent-white">{((insights.usersNeedingHelp / insights.totalUsers) * 100).toFixed(1)}%</p>
                      <p className="text-sm text-accent-white/60">Need Intervention</p>
                    </div>
                  </div>

                  <div className="mt-6 p-4 bg-blue-500/20 border border-blue-500/30 rounded-lg">
                    <h4 className="font-medium text-blue-400 mb-2">Program Health Score</h4>
                    <div className="flex items-center gap-3">
                      <div className="flex-1 bg-accent-white/20 rounded-full h-3">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-500"
                          style={{ width: `${insights.avgEngagementScore}%` }}
                        />
                      </div>
                      <span className="text-accent-white font-bold">{insights.avgEngagementScore.toFixed(1)}%</span>
                    </div>
                    <p className="text-sm text-accent-white/70 mt-2">
                      Overall program effectiveness based on user engagement, progression, and success rates
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminUserProgressDashboard;
