import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger } from '@/components/ui/dialog';
import {
  Users,
  Search,
  Filter,
  Mail,
  Calendar,
  Target,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Star,
  MessageSquare,
  Brain,
  Award,
  User,
  Eye,
  RefreshCw,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  BookOpen,
  Heart,
  Shield,
  Sparkles
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabaseAdmin } from '@/integrations/supabase/admin-client';
import { formatDistanceToNow, format } from 'date-fns';

interface UserManagementData {
  user_id: string;
  full_name: string;
  email: string;
  track: 'newbie' | 'builder' | 'scaler' | null;
  created_at: string;
  onboarding_completed: boolean;
  current_stage: number;
  engagement_score: number;
  risk_level: 'High' | 'Medium' | 'Low';
  success_category: string;
  days_since_last_activity: number;
  last_update_date: string | null;
  total_milestones_submitted: number;
  problem_description: string | null;
}

interface UserProfile {
  user_id: string;
  full_name: string;
  email: string;
  track: string | null;
  created_at: string;
  onboarding_completed: boolean;
  current_stage: number;
  current_confidence: number;
  days_in_current_stage: number;
  engagement_score: number;
  risk_level: string;
  success_category: string;
  progress_status: string;
  problem_description: string | null;
  solution_approach: string | null;
  target_audience: string | null;
  technical_background: string | null;
  primary_goal: string | null;
  biggest_challenge: string | null;
  total_daily_updates: number;
  updates_last_7_days: number;
  last_update_date: string | null;
  total_milestones_submitted: number;
  approved_milestones: number;
  last_milestone_date: string | null;
  recent_daily_updates: any[];
  recent_milestones: any[];

  // AI-Generated Comprehensive Profile Analysis
  profile_summary?: string | null;
  strengths?: string[] | null;
  knowledge_gaps?: string[] | null;
  personality_traits?: any | null;
  project_viability_score?: number | null;
  market_opportunity_assessment?: string | null;
  technical_feasibility_analysis?: string | null;
  recommended_next_steps?: string[] | null;
  potential_blockers?: string[] | null;
  optimal_learning_path?: any | null;
  mentor_matching_criteria?: any | null;
  resource_preferences?: any | null;
  communication_strategy?: any | null;
  personalized_goals?: any | null;
  success_probability_factors?: any | null;
  risk_factors?: any | null;
  profile_confidence_score?: number | null;

  // AI Conversation Data
  ai_satisfaction_score?: number | null;
  areas_needing_clarification?: string[] | null;
  key_insights_extracted?: any | null;
  conversation_completed?: boolean | null;
  total_questions_asked?: number | null;

  // User Memory & Learning Analytics
  total_memory_entries?: number | null;
  interaction_memories?: number | null;
  progress_memories?: number | null;
  insight_memories?: number | null;
  context_memories?: number | null;
  recent_memory_insights?: any[] | null;
}

const AdminUserManagement: React.FC = () => {
  const [users, setUsers] = useState<UserManagementData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterTrack, setFilterTrack] = useState<string>('all');
  const [filterRisk, setFilterRisk] = useState<string>('all');
  const [filterSuccess, setFilterSuccess] = useState<string>('all');
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [userProfileLoading, setUserProfileLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);
  const itemsPerPage = 20;
  const { toast } = useToast();

  useEffect(() => {
    loadUsers();
  }, [currentPage, filterTrack, filterRisk, filterSuccess, searchTerm]);

  const loadUsers = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabaseAdmin
        .rpc('get_users_for_management', {
          limit_count: itemsPerPage,
          offset_count: (currentPage - 1) * itemsPerPage,
          filter_track: filterTrack === 'all' ? null : filterTrack,
          filter_risk_level: filterRisk === 'all' ? null : filterRisk,
          filter_success_category: filterSuccess === 'all' ? null : filterSuccess,
          search_term: searchTerm || null
        });

      if (error) throw error;
      setUsers(data || []);
      
      // Get total count for pagination
      const { count } = await supabaseAdmin
        .from('user_progress_analytics')
        .select('*', { count: 'exact', head: true });
      setTotalUsers(count || 0);
      
    } catch (error) {
      console.error('Error loading users:', error);
      toast({
        title: "Error",
        description: "Failed to load users",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadUserProfile = async (userId: string) => {
    setUserProfileLoading(true);
    try {
      // Get basic profile data
      const { data: basicData, error: basicError } = await supabaseAdmin
        .rpc('get_user_comprehensive_profile', {
          target_user_id: userId
        });

      if (basicError) throw basicError;

      if (basicData && basicData.length > 0) {
        let profileData = basicData[0];

        // Fetch comprehensive AI profile data
        const { data: aiData, error: aiError } = await supabaseAdmin
          .from('comprehensive_user_profiles')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle();

        if (!aiError && aiData) {
          profileData = { ...profileData, ...aiData };
        }

        // Fetch AI conversation data
        const { data: conversationData, error: convError } = await supabaseAdmin
          .from('ai_followup_conversations')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle();

        if (!convError && conversationData) {
          profileData = { ...profileData, ...conversationData };
        }

        // Fetch user memory analytics
        try {
          const { data: memoryData, error: memoryError } = await supabaseAdmin
            .from('user_memory_entries')
            .select('memory_type')
            .eq('user_id', userId);

          if (!memoryError && memoryData) {
            const memoryCounts = memoryData.reduce((acc: any, entry: any) => {
              acc[entry.memory_type] = (acc[entry.memory_type] || 0) + 1;
              return acc;
            }, {});

            profileData.total_memory_entries = memoryData.length;
            profileData.interaction_memories = memoryCounts.interaction || 0;
            profileData.progress_memories = memoryCounts.progress || 0;
            profileData.insight_memories = memoryCounts.insight || 0;
            profileData.context_memories = memoryCounts.context || 0;
          }
        } catch (memoryError) {
          console.log('No memory data found for user:', userId);
        }

        setSelectedUser(profileData);
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
      toast({
        title: "Error",
        description: "Failed to load user profile",
        variant: "destructive",
      });
    } finally {
      setUserProfileLoading(false);
    }
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'High': return 'text-red-400 bg-red-500/20 border-red-500/30';
      case 'Medium': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      case 'Low': return 'text-green-400 bg-green-500/20 border-green-500/30';
      default: return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  const getTrackColor = (track: string | null) => {
    switch (track) {
      case 'newbie': return 'text-green-400 bg-green-500/20';
      case 'builder': return 'text-blue-400 bg-blue-500/20';
      case 'scaler': return 'text-purple-400 bg-purple-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getSuccessColor = (category: string) => {
    switch (category) {
      case 'High Performer': return 'text-purple-400 bg-purple-500/20 border-purple-500/30';
      case 'On Track': return 'text-blue-400 bg-blue-500/20 border-blue-500/30';
      case 'Engaged': return 'text-green-400 bg-green-500/20 border-green-500/30';
      default: return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  const getEngagementIcon = (score: number) => {
    if (score >= 80) return <Star className="h-4 w-4 text-yellow-400" />;
    if (score >= 60) return <TrendingUp className="h-4 w-4 text-green-400" />;
    if (score >= 40) return <Clock className="h-4 w-4 text-yellow-400" />;
    return <AlertTriangle className="h-4 w-4 text-red-400" />;
  };

  const totalPages = Math.ceil(totalUsers / itemsPerPage);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-accent-white mb-2">User Management</h2>
          <p className="text-crust-beige/80">Individual user progress monitoring and intervention tools</p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={loadUsers}
            variant="outline"
            size="sm"
            className="bg-accent-white/10 border-accent-white/30 text-accent-white hover:bg-accent-white/20"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-blue-500/10 to-blue-600/10 border-blue-500/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-400 text-sm font-medium">Total Users</p>
                <p className="text-2xl font-bold text-accent-white">{totalUsers}</p>
              </div>
              <Users className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-500/10 to-red-600/10 border-red-500/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-red-400 text-sm font-medium">High Risk</p>
                <p className="text-2xl font-bold text-accent-white">
                  {users.filter(u => u.risk_level === 'High').length}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/10 to-green-600/10 border-green-500/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-400 text-sm font-medium">High Performers</p>
                <p className="text-2xl font-bold text-accent-white">
                  {users.filter(u => u.success_category === 'High Performer').length}
                </p>
              </div>
              <Star className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-500/10 to-purple-600/10 border-purple-500/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-400 text-sm font-medium">Avg Engagement</p>
                <p className="text-2xl font-bold text-accent-white">
                  {users.length > 0 ? Math.round(users.reduce((sum, u) => sum + u.engagement_score, 0) / users.length) : 0}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-accent-white">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-accent-white/60" />
                <Input
                  placeholder="Search users, emails, projects..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-accent-white/10 border-accent-white/30 text-accent-white placeholder:text-accent-white/60"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-accent-white">Track</label>
              <Select value={filterTrack} onValueChange={setFilterTrack}>
                <SelectTrigger className="bg-accent-white/10 border-accent-white/30 text-accent-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-oven-black border-accent-white/30">
                  <SelectItem value="all">All Tracks</SelectItem>
                  <SelectItem value="newbie">Newbie</SelectItem>
                  <SelectItem value="builder">Builder</SelectItem>
                  <SelectItem value="scaler">Scaler</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-accent-white">Risk Level</label>
              <Select value={filterRisk} onValueChange={setFilterRisk}>
                <SelectTrigger className="bg-accent-white/10 border-accent-white/30 text-accent-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-oven-black border-accent-white/30">
                  <SelectItem value="all">All Risk Levels</SelectItem>
                  <SelectItem value="High">High Risk</SelectItem>
                  <SelectItem value="Medium">Medium Risk</SelectItem>
                  <SelectItem value="Low">Low Risk</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-accent-white">Success Category</label>
              <Select value={filterSuccess} onValueChange={setFilterSuccess}>
                <SelectTrigger className="bg-accent-white/10 border-accent-white/30 text-accent-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-oven-black border-accent-white/30">
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="High Performer">High Performer</SelectItem>
                  <SelectItem value="On Track">On Track</SelectItem>
                  <SelectItem value="Engaged">Engaged</SelectItem>
                  <SelectItem value="Needs Attention">Needs Attention</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-accent-white">Results</label>
              <div className="text-sm text-accent-white/80 bg-accent-white/10 rounded-lg px-3 py-2 border border-accent-white/20">
                {users.length} of {totalUsers} users
                {totalPages > 1 && (
                  <div className="text-xs text-accent-white/60 mt-1">
                    Page {currentPage} of {totalPages}
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* User Grid */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 text-accent-white/60 animate-spin" />
          <span className="ml-3 text-accent-white/60">Loading users...</span>
        </div>
      ) : users.length === 0 ? (
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-accent-white/40 mx-auto mb-4" />
          <p className="text-accent-white/60">No users found matching your filters</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          <AnimatePresence>
            {users.map((user, index) => (
              <motion.div
                key={user.user_id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.05 }}
              >
                <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20 hover:bg-accent-white/15 transition-all duration-300 cursor-pointer">
                  <CardContent className="p-6">
                    {/* User Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="font-semibold text-accent-white text-lg mb-1">{user.full_name}</h3>
                        <p className="text-sm text-accent-white/70 mb-2">{user.email}</p>
                        <div className="flex items-center gap-2 mb-3">
                          <Badge className={`text-xs ${getTrackColor(user.track)}`}>
                            {user.track || 'No Track'}
                          </Badge>
                          <Badge className={`text-xs ${getRiskColor(user.risk_level)}`}>
                            {user.risk_level} Risk
                          </Badge>
                          <Badge className={`text-xs ${getSuccessColor(user.success_category)}`}>
                            {user.success_category}
                          </Badge>
                          {user.onboarding_completed ? (
                            <Badge className="text-xs bg-green-500/20 text-green-400">
                              Onboarded
                            </Badge>
                          ) : (
                            <Badge className="text-xs bg-orange-500/20 text-orange-400">
                              Pending Onboarding
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        {getEngagementIcon(user.engagement_score)}
                        <span className="text-sm font-medium text-accent-white">{user.engagement_score}%</span>
                      </div>
                    </div>

                    {/* Project Description or Activity Summary */}
                    {user.problem_description ? (
                      <div className="mb-4">
                        <p className="text-sm text-accent-white/80 line-clamp-3">
                          {user.problem_description}
                        </p>
                      </div>
                    ) : user.total_milestones_submitted > 0 ? (
                      <div className="mb-4">
                        <p className="text-sm text-accent-white/80">
                          Active user with {user.total_milestones_submitted} milestone submissions but no formal onboarding completed.
                        </p>
                      </div>
                    ) : (
                      <div className="mb-4">
                        <p className="text-sm text-accent-white/60 italic">
                          No project description or activity yet.
                        </p>
                      </div>
                    )}

                    {/* Progress Metrics */}
                    <div className="grid grid-cols-3 gap-3 mb-4">
                      <div className="text-center">
                        <p className="text-lg font-bold text-accent-white">{user.current_stage}</p>
                        <p className="text-xs text-accent-white/60">Stage</p>
                      </div>
                      <div className="text-center">
                        <p className="text-lg font-bold text-accent-white">{user.total_milestones_submitted}</p>
                        <p className="text-xs text-accent-white/60">Milestones</p>
                      </div>
                      <div className="text-center">
                        <p className="text-lg font-bold text-accent-white">{user.days_since_last_activity}</p>
                        <p className="text-xs text-accent-white/60">Days Ago</p>
                      </div>
                    </div>

                    {/* Last Activity */}
                    <div className="flex items-center justify-between text-sm text-accent-white/70 mb-4">
                      <span>Last Activity:</span>
                      <span>
                        {user.last_update_date
                          ? formatDistanceToNow(new Date(user.last_update_date), { addSuffix: true })
                          : 'No recent activity'
                        }
                      </span>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 bg-accent-white/10 border-accent-white/30 text-accent-white hover:bg-accent-white/20"
                            onClick={() => loadUserProfile(user.user_id)}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            View Profile
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto bg-oven-black border-accent-white/30">
                          <DialogHeader>
                            <DialogTitle className="text-accent-white">
                              {selectedUser?.full_name} - Comprehensive Profile
                            </DialogTitle>
                            <DialogDescription className="text-accent-white/70">
                              Complete user profile with progress metrics, project details, and recent activity
                            </DialogDescription>
                          </DialogHeader>
                          {userProfileLoading ? (
                            <div className="flex items-center justify-center py-8">
                              <RefreshCw className="h-6 w-6 text-accent-white/60 animate-spin" />
                              <span className="ml-3 text-accent-white/60">Loading profile...</span>
                            </div>
                          ) : selectedUser ? (
                            <UserProfileModal user={selectedUser} />
                          ) : null}
                        </DialogContent>
                      </Dialog>

                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-accent-white/10 border-accent-white/30 text-accent-white hover:bg-accent-white/20"
                        onClick={() => window.open(`mailto:${user.email}`, '_blank')}
                      >
                        <Mail className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-accent-white/60">
            Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalUsers)} of {totalUsers} users
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="bg-accent-white/10 border-accent-white/30 text-accent-white hover:bg-accent-white/20"
            >
              Previous
            </Button>
            <span className="text-sm text-accent-white/80 px-3">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="bg-accent-white/10 border-accent-white/30 text-accent-white hover:bg-accent-white/20"
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

// User Profile Modal Component
const UserProfileModal: React.FC<{ user: UserProfile }> = ({ user }) => {
  return (
    <div className="space-y-6">
      {/* AI Profile Summary */}
      {user.profile_summary && (
        <Card className="bg-gradient-to-r from-purple-500/20 to-blue-500/20 border-purple-400/30">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <Brain className="h-5 w-5" />
              AI Profile Summary
              {user.profile_confidence_score && (
                <Badge className="ml-2 bg-purple-500/20 text-purple-200">
                  Confidence: {user.profile_confidence_score}%
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-accent-white/90 leading-relaxed">{user.profile_summary}</p>
          </CardContent>
        </Card>
      )}

      {/* Basic Info & Progress Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="bg-accent-white/10 border-accent-white/20">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <User className="h-5 w-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <label className="text-sm font-medium text-accent-white/70">Full Name</label>
              <p className="text-accent-white">{user.full_name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-accent-white/70">Email</label>
              <p className="text-accent-white">{user.email}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-accent-white/70">Track</label>
              <Badge className={`${user.track === 'newbie' ? 'bg-green-500/20 text-green-400' :
                user.track === 'builder' ? 'bg-blue-500/20 text-blue-400' :
                user.track === 'scaler' ? 'bg-purple-500/20 text-purple-400' : 'bg-gray-500/20 text-gray-400'}`}>
                {user.track || 'No Track Assigned'}
              </Badge>
            </div>
            <div>
              <label className="text-sm font-medium text-accent-white/70">Member Since</label>
              <p className="text-accent-white">{format(new Date(user.created_at), 'PPP')}</p>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-accent-white/10 border-accent-white/20">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <Target className="h-5 w-5" />
              Progress Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <label className="text-sm font-medium text-accent-white/70">Current Stage</label>
              <p className="text-accent-white text-lg font-bold">{user.current_stage}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-accent-white/70">Confidence Level</label>
              <div className="flex items-center gap-2">
                <div className="flex-1 bg-accent-white/20 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-sauce-red to-cheese-gold h-2 rounded-full"
                    style={{ width: `${user.current_confidence}%` }}
                  />
                </div>
                <span className="text-accent-white text-sm">{user.current_confidence}%</span>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-accent-white/70">Engagement Score</label>
              <div className="flex items-center gap-2">
                <div className="flex-1 bg-accent-white/20 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                    style={{ width: `${user.engagement_score}%` }}
                  />
                </div>
                <span className="text-accent-white text-sm">{user.engagement_score}%</span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-accent-white/70">Risk Level</label>
                <Badge className={`${user.risk_level === 'High' ? 'bg-red-500/20 text-red-400' :
                  user.risk_level === 'Medium' ? 'bg-yellow-500/20 text-yellow-400' : 'bg-green-500/20 text-green-400'}`}>
                  {user.risk_level}
                </Badge>
              </div>
              <div>
                <label className="text-sm font-medium text-accent-white/70">Success Category</label>
                <Badge className={`${user.success_category === 'High Performer' ? 'bg-purple-500/20 text-purple-400' :
                  user.success_category === 'On Track' ? 'bg-blue-500/20 text-blue-400' : 'bg-green-500/20 text-green-400'}`}>
                  {user.success_category}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activity Metrics */}
      <Card className="bg-accent-white/10 border-accent-white/20">
        <CardHeader>
          <CardTitle className="text-accent-white flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Activity Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-accent-white">{user.total_daily_updates || 0}</p>
              <p className="text-sm text-accent-white/60">Daily Updates</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-accent-white">{user.total_milestones_submitted || 0}</p>
              <p className="text-sm text-accent-white/60">Milestones</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-accent-white">{user.approved_milestones || 0}</p>
              <p className="text-sm text-accent-white/60">Approved</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-accent-white">{user.days_since_last_activity || 0}</p>
              <p className="text-sm text-accent-white/60">Days Since Activity</p>
            </div>
          </div>
          {user.last_update_date && (
            <div className="mt-4 pt-4 border-t border-accent-white/20">
              <p className="text-sm text-accent-white/70">
                Last Activity: {formatDistanceToNow(new Date(user.last_update_date), { addSuffix: true })}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Project Information */}
      {user.problem_description && (
        <Card className="bg-accent-white/10 border-accent-white/20">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <Brain className="h-5 w-5" />
              Project Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {user.problem_description && (
              <div>
                <label className="text-sm font-medium text-accent-white/70">Problem Description</label>
                <p className="text-accent-white">{user.problem_description}</p>
              </div>
            )}
            {user.solution_approach && (
              <div>
                <label className="text-sm font-medium text-accent-white/70">Solution Approach</label>
                <p className="text-accent-white">{user.solution_approach}</p>
              </div>
            )}
            {user.target_audience && (
              <div>
                <label className="text-sm font-medium text-accent-white/70">Target Audience</label>
                <p className="text-accent-white">{user.target_audience}</p>
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {user.primary_goal && (
                <div>
                  <label className="text-sm font-medium text-accent-white/70">Primary Goal</label>
                  <p className="text-accent-white">{user.primary_goal}</p>
                </div>
              )}
              {user.biggest_challenge && (
                <div>
                  <label className="text-sm font-medium text-accent-white/70">Biggest Challenge</label>
                  <p className="text-accent-white">{user.biggest_challenge}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Activity Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-accent-white/10 border-accent-white/20">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Daily Updates
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-accent-white/70">Total Updates</span>
                <span className="text-accent-white font-bold">{user.total_daily_updates}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-accent-white/70">Last 7 Days</span>
                <span className="text-accent-white font-bold">{user.updates_last_7_days}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-accent-white/70">Last Update</span>
                <span className="text-accent-white text-sm">
                  {user.last_update_date ? formatDistanceToNow(new Date(user.last_update_date), { addSuffix: true }) : 'Never'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-accent-white/10 border-accent-white/20">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <Award className="h-5 w-5" />
              Milestones
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-accent-white/70">Submitted</span>
                <span className="text-accent-white font-bold">{user.total_milestones_submitted}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-accent-white/70">Approved</span>
                <span className="text-accent-white font-bold">{user.approved_milestones}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-accent-white/70">Last Submission</span>
                <span className="text-accent-white text-sm">
                  {user.last_milestone_date ? formatDistanceToNow(new Date(user.last_milestone_date), { addSuffix: true }) : 'Never'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-accent-white/10 border-accent-white/20">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Activity Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-accent-white/70">Days in Current Stage</span>
                <span className="text-accent-white font-bold">{Math.round(user.days_in_current_stage)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-accent-white/70">Progress Status</span>
                <span className="text-accent-white text-sm">{user.progress_status}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card className="bg-accent-white/10 border-accent-white/20">
        <CardHeader>
          <CardTitle className="text-accent-white flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          {(user.recent_daily_updates?.length > 0 || user.recent_milestones?.length > 0) ? (
            <div className="space-y-4">
              {user.recent_daily_updates?.slice(0, 3).map((update, index) => (
                <div key={index} className="border-l-2 border-blue-500/30 pl-4">
                  <div className="flex justify-between items-start mb-1">
                    <span className="text-sm font-medium text-blue-400">Daily Update</span>
                    <span className="text-xs text-accent-white/60">
                      {formatDistanceToNow(new Date(update.date), { addSuffix: true })}
                    </span>
                  </div>
                  <p className="text-sm text-accent-white/80">{update.content}</p>
                </div>
              ))}

              {user.recent_milestones?.slice(0, 2).map((milestone, index) => (
                <div key={index} className="border-l-2 border-purple-500/30 pl-4">
                  <div className="flex justify-between items-start mb-1">
                    <span className="text-sm font-medium text-purple-400">
                      Milestone - Stage {milestone.stage_id}
                    </span>
                    <span className="text-xs text-accent-white/60">
                      {formatDistanceToNow(new Date(milestone.date), { addSuffix: true })}
                    </span>
                  </div>
                  <p className="text-sm text-accent-white/80">{milestone.content}</p>
                  {milestone.is_approved && (
                    <Badge className="mt-1 bg-green-500/20 text-green-400 text-xs">Approved</Badge>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Clock className="h-12 w-12 text-accent-white/30 mx-auto mb-3" />
              <p className="text-accent-white/60">No recent activity recorded</p>
              <p className="text-sm text-accent-white/40 mt-1">
                {user.onboarding_completed
                  ? "User hasn't submitted any updates or milestones yet"
                  : "User hasn't completed onboarding yet"
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* AI Analysis & Insights */}
      {(user.strengths?.length || user.knowledge_gaps?.length || user.personality_traits) && (
        <Card className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 border-purple-400/30">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <Brain className="h-5 w-5" />
              AI Analysis & Insights
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {user.strengths?.length && (
              <div>
                <label className="text-sm font-medium text-green-400 mb-2 block">Identified Strengths</label>
                <div className="flex flex-wrap gap-2">
                  {user.strengths.map((strength, index) => (
                    <Badge key={index} className="bg-green-500/20 text-green-400 text-xs">
                      {strength}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {user.knowledge_gaps?.length && (
              <div>
                <label className="text-sm font-medium text-orange-400 mb-2 block">Knowledge Gaps</label>
                <div className="flex flex-wrap gap-2">
                  {user.knowledge_gaps.map((gap, index) => (
                    <Badge key={index} className="bg-orange-500/20 text-orange-400 text-xs">
                      {gap}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {user.personality_traits && (
              <div>
                <label className="text-sm font-medium text-purple-400 mb-2 block">Personality Traits</label>
                <div className="bg-accent-white/5 rounded-lg p-3">
                  <pre className="text-sm text-accent-white/80 whitespace-pre-wrap">
                    {JSON.stringify(user.personality_traits, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Project Viability & Market Analysis */}
      {(user.project_viability_score || user.market_opportunity_assessment || user.technical_feasibility_analysis) && (
        <Card className="bg-gradient-to-r from-blue-500/10 to-green-500/10 border-blue-400/30">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Project Viability Analysis
              {user.project_viability_score && (
                <Badge className="ml-2 bg-blue-500/20 text-blue-200">
                  Score: {user.project_viability_score}/100
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {user.market_opportunity_assessment && (
              <div>
                <label className="text-sm font-medium text-blue-400 mb-2 block">Market Opportunity Assessment</label>
                <p className="text-accent-white/90 text-sm leading-relaxed">{user.market_opportunity_assessment}</p>
              </div>
            )}

            {user.technical_feasibility_analysis && (
              <div>
                <label className="text-sm font-medium text-green-400 mb-2 block">Technical Feasibility Analysis</label>
                <p className="text-accent-white/90 text-sm leading-relaxed">{user.technical_feasibility_analysis}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Recommendations & Action Items */}
      {(user.recommended_next_steps?.length || user.potential_blockers?.length) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {user.recommended_next_steps?.length && (
            <Card className="bg-green-500/10 border-green-400/30">
              <CardHeader>
                <CardTitle className="text-accent-white flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  Recommended Next Steps
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {user.recommended_next_steps.map((step, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm text-accent-white/90">{step}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {user.potential_blockers?.length && (
            <Card className="bg-red-500/10 border-red-400/30">
              <CardHeader>
                <CardTitle className="text-accent-white flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Potential Blockers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {user.potential_blockers.map((blocker, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm text-accent-white/90">{blocker}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Learning & Communication Preferences */}
      {(user.optimal_learning_path || user.resource_preferences || user.communication_strategy) && (
        <Card className="bg-yellow-500/10 border-yellow-400/30">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Learning & Communication Preferences
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {user.optimal_learning_path && (
              <div>
                <label className="text-sm font-medium text-yellow-400 mb-2 block">Optimal Learning Path</label>
                <div className="bg-accent-white/5 rounded-lg p-3">
                  <pre className="text-sm text-accent-white/80 whitespace-pre-wrap">
                    {JSON.stringify(user.optimal_learning_path, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {user.resource_preferences && (
              <div>
                <label className="text-sm font-medium text-yellow-400 mb-2 block">Resource Preferences</label>
                <div className="bg-accent-white/5 rounded-lg p-3">
                  <pre className="text-sm text-accent-white/80 whitespace-pre-wrap">
                    {JSON.stringify(user.resource_preferences, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {user.communication_strategy && (
              <div>
                <label className="text-sm font-medium text-yellow-400 mb-2 block">Communication Strategy</label>
                <div className="bg-accent-white/5 rounded-lg p-3">
                  <pre className="text-sm text-accent-white/80 whitespace-pre-wrap">
                    {JSON.stringify(user.communication_strategy, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Goals & Success Factors */}
      {(user.personalized_goals || user.success_probability_factors || user.risk_factors) && (
        <Card className="bg-purple-500/10 border-purple-400/30">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <Target className="h-5 w-5" />
              Goals & Success Factors
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {user.personalized_goals && (
              <div>
                <label className="text-sm font-medium text-purple-400 mb-2 block">Personalized Goals</label>
                <div className="bg-accent-white/5 rounded-lg p-3">
                  <pre className="text-sm text-accent-white/80 whitespace-pre-wrap">
                    {JSON.stringify(user.personalized_goals, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {user.success_probability_factors && (
                <div>
                  <label className="text-sm font-medium text-green-400 mb-2 block">Success Factors</label>
                  <div className="bg-accent-white/5 rounded-lg p-3">
                    <pre className="text-xs text-accent-white/80 whitespace-pre-wrap">
                      {JSON.stringify(user.success_probability_factors, null, 2)}
                    </pre>
                  </div>
                </div>
              )}

              {user.risk_factors && (
                <div>
                  <label className="text-sm font-medium text-red-400 mb-2 block">Risk Factors</label>
                  <div className="bg-accent-white/5 rounded-lg p-3">
                    <pre className="text-xs text-accent-white/80 whitespace-pre-wrap">
                      {JSON.stringify(user.risk_factors, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* AI Conversation Analytics */}
      {(user.ai_satisfaction_score || user.areas_needing_clarification?.length || user.key_insights_extracted) && (
        <Card className="bg-cyan-500/10 border-cyan-400/30">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              AI Conversation Analytics
              {user.ai_satisfaction_score && (
                <Badge className="ml-2 bg-cyan-500/20 text-cyan-200">
                  Satisfaction: {user.ai_satisfaction_score}%
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-cyan-400">{user.total_questions_asked || 0}</p>
                <p className="text-sm text-accent-white/60">Questions Asked</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-cyan-400">{user.conversation_completed ? 'Yes' : 'No'}</p>
                <p className="text-sm text-accent-white/60">Completed</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-cyan-400">{user.total_memory_entries || 0}</p>
                <p className="text-sm text-accent-white/60">Memory Entries</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-cyan-400">{user.insight_memories || 0}</p>
                <p className="text-sm text-accent-white/60">AI Insights</p>
              </div>
            </div>

            {user.areas_needing_clarification?.length && (
              <div>
                <label className="text-sm font-medium text-orange-400 mb-2 block">Areas Needing Clarification</label>
                <div className="flex flex-wrap gap-2">
                  {user.areas_needing_clarification.map((area, index) => (
                    <Badge key={index} className="bg-orange-500/20 text-orange-400 text-xs">
                      {area}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {user.key_insights_extracted && (
              <div>
                <label className="text-sm font-medium text-cyan-400 mb-2 block">Key Insights Extracted</label>
                <div className="bg-accent-white/5 rounded-lg p-3">
                  <pre className="text-sm text-accent-white/80 whitespace-pre-wrap">
                    {JSON.stringify(user.key_insights_extracted, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Memory Analytics */}
      {(user.total_memory_entries || user.recent_memory_insights?.length) && (
        <Card className="bg-indigo-500/10 border-indigo-400/30">
          <CardHeader>
            <CardTitle className="text-accent-white flex items-center gap-2">
              <Brain className="h-5 w-5" />
              Memory & Learning Analytics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-indigo-400">{user.interaction_memories || 0}</p>
                <p className="text-sm text-accent-white/60">Interactions</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-indigo-400">{user.progress_memories || 0}</p>
                <p className="text-sm text-accent-white/60">Progress</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-indigo-400">{user.insight_memories || 0}</p>
                <p className="text-sm text-accent-white/60">Insights</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-indigo-400">{user.context_memories || 0}</p>
                <p className="text-sm text-accent-white/60">Context</p>
              </div>
            </div>

            {user.recent_memory_insights?.length && (
              <div>
                <label className="text-sm font-medium text-indigo-400 mb-2 block">Recent Memory Insights</label>
                <div className="space-y-2">
                  {user.recent_memory_insights.slice(0, 3).map((insight, index) => (
                    <div key={index} className="bg-accent-white/5 rounded-lg p-3">
                      <div className="flex justify-between items-start mb-1">
                        <span className="text-xs text-indigo-400 font-medium">{insight.memory_type}</span>
                        <span className="text-xs text-accent-white/60">
                          {formatDistanceToNow(new Date(insight.created_at), { addSuffix: true })}
                        </span>
                      </div>
                      <p className="text-sm text-accent-white/80">{insight.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}


    </div>
  );
};

export default AdminUserManagement;
