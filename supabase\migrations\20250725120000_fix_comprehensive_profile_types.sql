-- Fix type mismatches in get_user_comprehensive_profile function
-- The function was declaring technical_background and primary_goal as text
-- but they are character varying in the onboarding_responses table

DROP FUNCTION IF EXISTS get_user_comprehensive_profile(UUID);

CREATE OR REPLACE FUNCTION get_user_comprehensive_profile(target_user_id UUID)
RETURNS TABLE (
  -- Basic user info
  user_id UUID,
  full_name CHARACTER VARYING,
  email CHARACTER VARYING,
  track CHARACTER VARYING,
  created_at TIMESTAMP WITH TIME ZONE,
  onboarding_completed BOOLEAN,
  
  -- Progress metrics
  current_stage INTEGER,
  current_confidence INTEGER,
  days_in_current_stage NUMERIC,
  engagement_score INTEGER,
  risk_level TEXT,
  success_category TEXT,
  progress_status TEXT,
  
  -- Onboarding data (fixed types to match actual table schema)
  problem_description TEXT,
  solution_approach TEXT,
  target_audience TEXT,
  technical_background CHARACTER VARYING,  -- Fixed: was TEXT, now CHARACTER VARYING
  primary_goal CHARACTER VARYING,          -- Fixed: was TEXT, now CHARACTER VARYING
  biggest_challenge TEXT,
  
  -- Activity metrics
  total_daily_updates INTEGER,
  updates_last_7_days INTEGER,
  last_update_date TIMESTAMP WITH TIME ZONE,
  total_milestones_submitted INTEGER,
  approved_milestones INTEGER,
  last_milestone_date TIMESTAMP WITH TIME ZONE,
  
  -- Recent activity
  recent_daily_updates JSONB,
  recent_milestones JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    -- Basic user info
    upa.user_id,
    upa.full_name,
    upa.email,
    upa.track,
    upa.user_created_at as created_at,
    upa.onboarding_completed,
    
    -- Progress metrics
    upa.current_stage,
    upa.current_confidence,
    upa.days_in_current_stage,
    upa.engagement_score,
    upa.risk_level,
    upa.success_category,
    upa.progress_status,
    
    -- Onboarding data
    or_data.problem_description,
    or_data.solution_approach,
    or_data.target_audience,
    or_data.technical_background,
    or_data.primary_goal,
    or_data.biggest_challenge,
    
    -- Activity metrics
    upa.total_daily_updates,
    upa.updates_last_7_days,
    upa.last_update_date,
    upa.total_milestones_submitted,
    upa.approved_milestones,
    upa.last_milestone_date,
    
    -- Recent activity (as JSON)
    COALESCE(
      (SELECT jsonb_agg(
        jsonb_build_object(
          'date', du.created_at,
          'content', LEFT(du.content, 200),
          'sentiment_score', du.sentiment_score
        ) ORDER BY du.created_at DESC
      ) FROM daily_updates du WHERE du.user_id = target_user_id LIMIT 5),
      '[]'::jsonb
    ) as recent_daily_updates,
    
    COALESCE(
      (SELECT jsonb_agg(
        jsonb_build_object(
          'date', ms.submission_date,
          'stage_id', ms.stage_id,
          'milestone_id', ms.milestone_id,
          'is_approved', ms.is_approved,
          'content', LEFT(ms.submission_content, 200)
        ) ORDER BY ms.submission_date DESC
      ) FROM milestone_submissions ms WHERE ms.user_id = target_user_id LIMIT 5),
      '[]'::jsonb
    ) as recent_milestones
    
  FROM user_progress_analytics upa
  LEFT JOIN onboarding_responses or_data ON upa.user_id = or_data.user_id
  WHERE upa.user_id = target_user_id;
END;
$$ LANGUAGE plpgsql;

-- Add comment
COMMENT ON FUNCTION get_user_comprehensive_profile(UUID) IS 'Returns comprehensive user profile with corrected return types matching actual table schema';
