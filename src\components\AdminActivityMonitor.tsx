import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Activity, 
  Search, 
  Download,
  RefreshCw, 
  Users, 
  Calendar,
  Clock,
  Eye,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  User,
  Globe,
  ChevronDown,
  ChevronUp,
  ExternalLink
} from 'lucide-react';
import { supabaseAdmin } from '@/integrations/supabase/admin-client';
import { activityTrackingService, ActivityLogEntry, ActivityTypeDefinition } from '@/services/activityTrackingService';
import { useToast } from '@/hooks/use-toast';
import { formatDistanceToNow, format } from 'date-fns';

interface ActivityWithUser extends ActivityLogEntry {
  users?: {
    full_name: string;
    email: string;
    track: string;
  };
}

interface ActivitySummary {
  totalActivities: number;
  uniqueUsers: number;
  topActivities: { activity_type: string; count: number; display_name: string }[];
  hourlyDistribution: { hour: number; count: number }[];
}

const AdminActivityMonitor: React.FC = () => {
  const [activities, setActivities] = useState<ActivityWithUser[]>([]);
  const [activityTypes, setActivityTypes] = useState<ActivityTypeDefinition[]>([]);
  const [summary, setSummary] = useState<ActivitySummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedUser, setSelectedUser] = useState<string>('all');
  const [dateRange, setDateRange] = useState<string>('24h');
  const [expandedActivity, setExpandedActivity] = useState<string | null>(null);
  const [realTimeEnabled, setRealTimeEnabled] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(50);
  const { toast } = useToast();

  // Real-time subscription
  useEffect(() => {
    if (!realTimeEnabled) return;

    const subscription = supabaseAdmin
      .channel('activity_monitor')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'user_activity_logs'
        },
        (payload) => {
          console.log('New activity received:', payload);
          // Refresh activities when new ones come in
          fetchActivities();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [realTimeEnabled]);

  useEffect(() => {
    loadInitialData();
  }, [dateRange, selectedCategory]);

  // Reset pagination when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedUser, selectedCategory]);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchActivities(),
        fetchActivityTypes(),
        fetchSummary()
      ]);
    } catch (error) {
      console.error('Error loading activity data:', error);
      toast({
        title: "Error",
        description: "Failed to load activity data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchActivities = async () => {
    try {
      const hoursBack = getHoursFromDateRange(dateRange);
      const cutoffTime = new Date(Date.now() - hoursBack * 60 * 60 * 1000).toISOString();

      let query = supabaseAdmin
        .from('user_activity_logs')
        .select(`
          *,
          users!inner(full_name, email, track)
        `)
        .gte('created_at', cutoffTime)
        .order('created_at', { ascending: false })
        .limit(500);

      if (selectedCategory !== 'all') {
        // Join with activity_type_definitions to filter by category
        const { data: typeData } = await supabaseAdmin
          .from('activity_type_definitions')
          .select('activity_type')
          .eq('category', selectedCategory);
        
        if (typeData && typeData.length > 0) {
          const activityTypes = typeData.map(t => t.activity_type);
          query = query.in('activity_type', activityTypes);
        }
      }

      const { data, error } = await query;

      if (error) throw error;
      setActivities(data || []);
    } catch (error) {
      console.error('Error fetching activities:', error);
    }
  };

  const fetchActivityTypes = async () => {
    try {
      const types = await activityTrackingService.getActivityTypeDefinitions();
      setActivityTypes(types);
    } catch (error) {
      console.error('Error fetching activity types:', error);
    }
  };

  const fetchSummary = async () => {
    try {
      const hoursBack = getHoursFromDateRange(dateRange);
      const cutoffTime = new Date(Date.now() - hoursBack * 60 * 60 * 1000).toISOString();

      // Get summary statistics
      const { data: summaryData, error } = await supabaseAdmin
        .from('user_activity_logs')
        .select(`
          activity_type,
          user_id,
          created_at,
          activity_type_definitions!inner(display_name)
        `)
        .gte('created_at', cutoffTime);

      if (error) throw error;

      if (summaryData) {
        const totalActivities = summaryData.length;
        const uniqueUsers = new Set(summaryData.map(a => a.user_id)).size;
        
        // Top activities
        const activityCounts = summaryData.reduce((acc, activity) => {
          const key = activity.activity_type;
          acc[key] = (acc[key] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

        const topActivities = Object.entries(activityCounts)
          .map(([activity_type, count]) => ({
            activity_type,
            count,
            display_name: summaryData.find(a => a.activity_type === activity_type)?.activity_type_definitions?.display_name || activity_type
          }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5);

        // Hourly distribution
        const hourlyDistribution = Array.from({ length: 24 }, (_, hour) => ({
          hour,
          count: summaryData.filter(a => new Date(a.created_at).getHours() === hour).length
        }));

        setSummary({
          totalActivities,
          uniqueUsers,
          topActivities,
          hourlyDistribution
        });
      }
    } catch (error) {
      console.error('Error fetching summary:', error);
    }
  };

  const getHoursFromDateRange = (range: string): number => {
    switch (range) {
      case '1h': return 1;
      case '6h': return 6;
      case '24h': return 24;
      case '7d': return 168;
      case '30d': return 720;
      default: return 24;
    }
  };

  const getActivityIcon = (activityType: string) => {
    const typeDefinition = activityTypes.find(t => t.activity_type === activityType);
    const iconName = typeDefinition?.icon_name;
    
    // Map icon names to actual icon components
    const iconMap: Record<string, React.ComponentType<any>> = {
      LogIn: User,
      LogOut: User,
      UserPlus: Users,
      Eye: Eye,
      ArrowRight: TrendingUp,
      Navigation: Globe,
      Play: CheckCircle,
      CheckCircle: CheckCircle,
      Award: CheckCircle,
      Target: Activity,
      Send: Activity,
      TrendingUp: TrendingUp,
      Brain: Activity,
      Lightbulb: Activity,
      RefreshCw: RefreshCw,
      User: User,
      Edit: Activity,
      Calendar: Calendar,
      HelpCircle: AlertCircle,
      BookOpen: Activity,
      Shield: Activity,
      Settings: Activity
    };

    const IconComponent = iconMap[iconName || 'Activity'] || Activity;
    return <IconComponent className="h-4 w-4" />;
  };

  const getActivityColor = (activityType: string): string => {
    const typeDefinition = activityTypes.find(t => t.activity_type === activityType);
    return typeDefinition?.color_code || '#6B7280';
  };

  const getCategoryColor = (category: string): string => {
    const colors: Record<string, string> = {
      authentication: '#10B981',
      navigation: '#6B7280',
      onboarding: '#F59E0B',
      dev_journey: '#8B5CF6',
      ai_interaction: '#EC4899',
      profile: '#F97316',
      daily_update: '#84CC16',
      help: '#EF4444',
      admin: '#DC2626'
    };
    return colors[category] || '#6B7280';
  };

  // Memoized filtering for performance
  const filteredActivities = useMemo(() => {
    return activities.filter(activity => {
      const matchesSearch = !searchTerm ||
        activity.users?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.activity_type.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesUser = selectedUser === 'all' || activity.user_id === selectedUser;

      return matchesSearch && matchesUser;
    });
  }, [activities, searchTerm, selectedUser]);

  // Paginated activities for performance
  const paginatedActivities = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredActivities.slice(startIndex, endIndex);
  }, [filteredActivities, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredActivities.length / itemsPerPage);

  const exportActivities = useCallback(() => {
    const csvContent = [
      ['Timestamp', 'User', 'Email', 'Activity Type', 'Activity Data', 'IP Address', 'User Agent'].join(','),
      ...filteredActivities.map(activity => [
        format(new Date(activity.created_at!), 'yyyy-MM-dd HH:mm:ss'),
        activity.users?.full_name || 'Unknown',
        activity.users?.email || 'Unknown',
        activity.activity_type,
        JSON.stringify(activity.activity_data),
        activity.ip_address || '',
        activity.user_agent || ''
      ].map(field => `"${field}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `activity_log_${format(new Date(), 'yyyy-MM-dd_HH-mm')}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  }, [filteredActivities]);

  const uniqueUsers = Array.from(new Set(activities.map(a => a.user_id)))
    .map(userId => {
      const activity = activities.find(a => a.user_id === userId);
      return {
        id: userId,
        name: activity?.users?.full_name || 'Unknown',
        email: activity?.users?.email || 'Unknown'
      };
    });

  const categories = Array.from(new Set(activityTypes.map(t => t.category)));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-accent-white mb-2">Activity Monitor</h2>
          <p className="text-crust-beige/80">Real-time user activity tracking and analytics</p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={() => setRealTimeEnabled(!realTimeEnabled)}
            variant={realTimeEnabled ? "default" : "outline"}
            size="sm"
            className="bg-sauce-red/20 border-sauce-red/30 text-accent-white hover:bg-sauce-red/30"
          >
            <Activity className="h-4 w-4 mr-2" />
            {realTimeEnabled ? 'Live' : 'Paused'}
          </Button>
          <Button
            onClick={loadInitialData}
            variant="outline"
            size="sm"
            className="bg-accent-white/10 border-accent-white/30 text-accent-white hover:bg-accent-white/20"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            onClick={exportActivities}
            variant="outline"
            size="sm"
            className="bg-accent-white/10 border-accent-white/30 text-accent-white hover:bg-accent-white/20"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
            <Card className="bg-gradient-to-br from-blue-500/10 to-blue-600/10 border-blue-500/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-400 text-sm font-medium">Total Activities</p>
                    <p className="text-3xl font-bold text-accent-white">{summary.totalActivities.toLocaleString()}</p>
                    <p className="text-xs text-blue-400/60 mt-1">Last {dateRange}</p>
                  </div>
                  <Activity className="h-12 w-12 text-blue-400" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }}>
            <Card className="bg-gradient-to-br from-green-500/10 to-green-600/10 border-green-500/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-400 text-sm font-medium">Active Users</p>
                    <p className="text-3xl font-bold text-accent-white">{summary.uniqueUsers}</p>
                    <p className="text-xs text-green-400/60 mt-1">Unique users</p>
                  </div>
                  <Users className="h-12 w-12 text-green-400" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3 }}>
            <Card className="bg-gradient-to-br from-purple-500/10 to-purple-600/10 border-purple-500/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-400 text-sm font-medium">Top Activity</p>
                    <p className="text-lg font-bold text-accent-white">
                      {summary.topActivities[0]?.display_name || 'N/A'}
                    </p>
                    <p className="text-xs text-purple-400/60 mt-1">
                      {summary.topActivities[0]?.count || 0} occurrences
                    </p>
                  </div>
                  <TrendingUp className="h-12 w-12 text-purple-400" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.4 }}>
            <Card className="bg-gradient-to-br from-orange-500/10 to-orange-600/10 border-orange-500/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-400 text-sm font-medium">Peak Hour</p>
                    <p className="text-3xl font-bold text-accent-white">
                      {summary.hourlyDistribution.reduce((max, curr) =>
                        curr.count > max.count ? curr : max
                      ).hour}:00
                    </p>
                    <p className="text-xs text-orange-400/60 mt-1">Most active time</p>
                  </div>
                  <Clock className="h-12 w-12 text-orange-400" />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      )}

      {/* Filters */}
      <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-accent-white">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-accent-white/60" />
                <Input
                  placeholder="Search users, emails, activities..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-accent-white/10 border-accent-white/30 text-accent-white placeholder:text-accent-white/60"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-accent-white">Time Range</label>
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger className="bg-accent-white/10 border-accent-white/30 text-accent-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-oven-black border-accent-white/30">
                  <SelectItem value="1h">Last Hour</SelectItem>
                  <SelectItem value="6h">Last 6 Hours</SelectItem>
                  <SelectItem value="24h">Last 24 Hours</SelectItem>
                  <SelectItem value="7d">Last 7 Days</SelectItem>
                  <SelectItem value="30d">Last 30 Days</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-accent-white">Category</label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="bg-accent-white/10 border-accent-white/30 text-accent-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-oven-black border-accent-white/30">
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: getCategoryColor(category) }}
                        />
                        {category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-accent-white">User</label>
              <Select value={selectedUser} onValueChange={setSelectedUser}>
                <SelectTrigger className="bg-accent-white/10 border-accent-white/30 text-accent-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-oven-black border-accent-white/30">
                  <SelectItem value="all">All Users</SelectItem>
                  {uniqueUsers.map(user => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.name} ({user.email})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-accent-white">Results</label>
              <div className="text-sm text-accent-white/80 bg-accent-white/10 rounded-lg px-3 py-2 border border-accent-white/20">
                {filteredActivities.length} activities
                {totalPages > 1 && (
                  <div className="text-xs text-accent-white/60 mt-1">
                    Page {currentPage} of {totalPages}
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Activity Feed */}
      <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-accent-white flex items-center gap-2">
            <Activity className="h-6 w-6 text-sauce-red" />
            Activity Feed
            {realTimeEnabled && (
              <Badge variant="outline" className="ml-2 border-green-500/30 text-green-400">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse" />
                Live
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="h-8 w-8 text-accent-white/60 animate-spin" />
              <span className="ml-3 text-accent-white/60">Loading activities...</span>
            </div>
          ) : filteredActivities.length === 0 ? (
            <div className="text-center py-12">
              <Activity className="h-12 w-12 text-accent-white/40 mx-auto mb-4" />
              <p className="text-accent-white/60">No activities found for the selected filters</p>
            </div>
          ) : (
            <>
              <div className="space-y-3">
                <AnimatePresence>
                  {paginatedActivities.map((activity, index) => {
                  const activityType = activityTypes.find(t => t.activity_type === activity.activity_type);
                  const isExpanded = expandedActivity === activity.id;

                  return (
                    <motion.div
                      key={activity.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ delay: index * 0.05 }}
                      className="bg-accent-white/5 rounded-lg border border-accent-white/10 p-4 hover:bg-accent-white/10 transition-colors"
                    >
                      <div
                        className="flex items-start justify-between cursor-pointer"
                        onClick={() => setExpandedActivity(isExpanded ? null : activity.id!)}
                      >
                        <div className="flex items-start gap-3 flex-1">
                          <div
                            className="p-2 rounded-lg flex-shrink-0"
                            style={{ backgroundColor: `${getActivityColor(activity.activity_type)}20` }}
                          >
                            {getActivityIcon(activity.activity_type)}
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <Badge
                                variant="outline"
                                className="text-xs"
                                style={{
                                  borderColor: getActivityColor(activity.activity_type),
                                  color: getActivityColor(activity.activity_type)
                                }}
                              >
                                {activityType?.display_name || activity.activity_type}
                              </Badge>
                              <span className="text-xs text-accent-white/60">
                                {formatDistanceToNow(new Date(activity.created_at!), { addSuffix: true })}
                              </span>
                            </div>

                            <div className="flex items-center gap-2 mb-2">
                              <User className="h-4 w-4 text-accent-white/60" />
                              <span className="text-sm font-medium text-accent-white">
                                {activity.users?.full_name || 'Unknown User'}
                              </span>
                              <span className="text-xs text-accent-white/60">
                                ({activity.users?.email || '<EMAIL>'})
                              </span>
                              {activity.users?.track && (
                                <Badge variant="secondary" className="text-xs">
                                  {activity.users.track}
                                </Badge>
                              )}
                            </div>

                            {activity.activity_data?.action && (
                              <p className="text-sm text-accent-white/80">
                                {activity.activity_data.action}
                                {activity.activity_data?.target && (
                                  <span className="text-accent-white/60"> → {activity.activity_data.target}</span>
                                )}
                              </p>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          {activity.page_url && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-accent-white/60 hover:text-accent-white"
                              onClick={(e) => {
                                e.stopPropagation();
                                window.open(activity.page_url, '_blank');
                              }}
                            >
                              <ExternalLink className="h-4 w-4" />
                            </Button>
                          )}
                          {isExpanded ? (
                            <ChevronUp className="h-4 w-4 text-accent-white/60" />
                          ) : (
                            <ChevronDown className="h-4 w-4 text-accent-white/60" />
                          )}
                        </div>
                      </div>

                      <AnimatePresence>
                        {isExpanded && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="mt-4 pt-4 border-t border-accent-white/10"
                          >
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                              <div>
                                <h4 className="font-medium text-accent-white mb-2">Activity Details</h4>
                                <div className="space-y-1 text-accent-white/70">
                                  <div><span className="font-medium">Timestamp:</span> {format(new Date(activity.created_at!), 'PPpp')}</div>
                                  <div><span className="font-medium">Session ID:</span> {activity.session_id || 'N/A'}</div>
                                  <div><span className="font-medium">IP Address:</span> {activity.ip_address || 'N/A'}</div>
                                  <div><span className="font-medium">Page URL:</span> {activity.page_url || 'N/A'}</div>
                                </div>
                              </div>

                              <div>
                                <h4 className="font-medium text-accent-white mb-2">Activity Data</h4>
                                <div className="bg-oven-black/50 rounded p-2 text-accent-white/70 font-mono text-xs max-h-32 overflow-y-auto">
                                  <pre>{JSON.stringify(activity.activity_data, null, 2)}</pre>
                                </div>
                              </div>

                              {activity.user_agent && (
                                <div className="md:col-span-2">
                                  <h4 className="font-medium text-accent-white mb-2">User Agent</h4>
                                  <div className="text-accent-white/70 break-all">
                                    {activity.user_agent}
                                  </div>
                                </div>
                              )}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  );
                  })}
                </AnimatePresence>
              </div>

              {/* Pagination Controls */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between pt-4 border-t border-accent-white/10">
                  <div className="text-sm text-accent-white/60">
                    Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredActivities.length)} of {filteredActivities.length} activities
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="bg-accent-white/10 border-accent-white/30 text-accent-white hover:bg-accent-white/20"
                    >
                      Previous
                    </Button>
                    <span className="text-sm text-accent-white/80 px-3">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="bg-accent-white/10 border-accent-white/30 text-accent-white hover:bg-accent-white/20"
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminActivityMonitor;
