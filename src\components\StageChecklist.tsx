import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckCircle2, 
  Circle, 
  Clock, 
  ArrowRight,
  Target,
  Users,
  FileText,
  MessageSquare,
  Lightbulb
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import stageProgressService, { StageMilestone } from '@/services/stageProgressService';
import devJourneyService from '@/services/devJourneyService';
import MarkdownText from './MarkdownText';

interface StageChecklistProps {
  userId: string;
  currentStage: number;
  onMilestoneUpdate?: (progress: number) => void;
}

const StageChecklist: React.FC<StageChecklistProps> = ({ 
  userId, 
  currentStage, 
  onMilestoneUpdate 
}) => {
  const [milestones, setMilestones] = useState<StageMilestone[]>([]);
  const [loading, setLoading] = useState(true);
  const [progress, setProgress] = useState(0);

  const stage = devJourneyService.getStage(currentStage);

  // Icon mapping for different milestone types
  const getIconForMilestone = (milestoneId: string) => {
    if (milestoneId.includes('team')) return Users;
    if (milestoneId.includes('planning') || milestoneId.includes('roadmap')) return FileText;
    if (milestoneId.includes('feedback') || milestoneId.includes('user')) return MessageSquare;
    if (milestoneId.includes('problem') || milestoneId.includes('idea')) return Target;
    return Lightbulb;
  };

  const loadMilestones = async () => {
    try {
      setLoading(true);
      const milestonesWithProgress = await stageProgressService.getStageMilestonesWithProgress(
        userId, 
        currentStage
      );
      setMilestones(milestonesWithProgress);
      
      // Calculate progress
      const completedRequired = milestonesWithProgress.filter(m => m.required && m.completed).length;
      const totalRequired = milestonesWithProgress.filter(m => m.required).length;
      const progressPercentage = totalRequired > 0 ? Math.round((completedRequired / totalRequired) * 100) : 0;
      
      setProgress(progressPercentage);
      onMilestoneUpdate?.(progressPercentage);
    } catch (error) {
      console.error('Error loading milestones:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMilestones();
  }, [userId, currentStage]);

  // Auto-refresh every 5 seconds to catch updates
  useEffect(() => {
    const interval = setInterval(loadMilestones, 5000);
    return () => clearInterval(interval);
  }, [userId, currentStage]);

  const requiredMilestones = milestones.filter(m => m.required);
  const optionalMilestones = milestones.filter(m => !m.required);
  const completedRequired = requiredMilestones.filter(m => m.completed).length;

  if (loading) {
    return (
      <Card className="bg-gradient-to-br from-accent-white/10 to-accent-white/5 backdrop-blur-xl border border-accent-white/20">
        <CardHeader>
          <CardTitle className="text-accent-white">Loading Checklist...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-12 bg-accent-white/10 rounded-lg" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Progress Header */}
      <Card className="bg-gradient-to-br from-accent-white/10 to-accent-white/5 backdrop-blur-xl border border-accent-white/20 shadow-2xl rounded-2xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl text-accent-white flex items-center gap-3">
              <Target className="h-6 w-6 text-cheese-gold" />
              {stage?.name} Stage Requirements
            </CardTitle>
            <Badge 
              variant={progress >= 80 ? "default" : "secondary"}
              className={`px-3 py-1 ${
                progress >= 80 
                  ? 'bg-green-500/20 text-green-400 border-green-500/30' 
                  : 'bg-cheese-gold/20 text-cheese-gold border-cheese-gold/30'
              }`}
            >
              {progress}% Complete
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Progress 
              value={progress} 
              className="h-3 bg-accent-white/10"
            />
            <p className="text-accent-white/70 text-sm">
              Complete {completedRequired}/{requiredMilestones.length} required milestones to advance to the next stage
            </p>
            
            {progress >= 80 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="p-4 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-xl"
              >
                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-400" />
                  <div>
                    <p className="text-green-400 font-semibold">Ready to Advance!</p>
                    <p className="text-green-300 text-sm">You've completed the requirements for the next stage.</p>
                  </div>
                  <ArrowRight className="h-5 w-5 text-green-400 ml-auto" />
                </div>
              </motion.div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Required Milestones */}
      <Card className="bg-gradient-to-br from-accent-white/10 to-accent-white/5 backdrop-blur-xl border border-accent-white/20 shadow-2xl rounded-2xl">
        <CardHeader>
          <CardTitle className="text-lg text-accent-white">Required Milestones</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <AnimatePresence>
            {requiredMilestones.map((milestone, idx) => {
              const IconComponent = getIconForMilestone(milestone.id);
              return (
                <motion.div
                  key={milestone.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: idx * 0.1 }}
                  className={`flex items-center gap-4 p-4 rounded-xl border transition-all duration-300 ${
                    milestone.completed
                      ? 'bg-green-500/10 border-green-500/30 text-green-300'
                      : 'bg-accent-white/5 border-accent-white/10 text-accent-white/80 hover:border-cheese-gold/30'
                  }`}
                >
                  <div className="flex-shrink-0">
                    {milestone.completed ? (
                      <CheckCircle2 className="h-6 w-6 text-green-400" />
                    ) : (
                      <Circle className="h-6 w-6 text-accent-white/40" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <IconComponent className="h-4 w-4 text-cheese-gold" />
                      <h4 className={`font-semibold ${milestone.completed ? 'line-through' : ''}`}>
                        {milestone.title}
                      </h4>
                    </div>
                    <MarkdownText className="text-sm text-accent-white/60">
                      {milestone.description}
                    </MarkdownText>
                  </div>
                  
                  {milestone.completed && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="flex-shrink-0"
                    >
                      <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                        Complete
                      </Badge>
                    </motion.div>
                  )}
                </motion.div>
              );
            })}
          </AnimatePresence>
        </CardContent>
      </Card>

      {/* Optional Milestones */}
      {optionalMilestones.length > 0 && (
        <Card className="bg-gradient-to-br from-accent-white/5 to-accent-white/2 backdrop-blur-xl border border-accent-white/10 shadow-xl rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg text-accent-white/80 flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Optional Enhancements
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {optionalMilestones.map((milestone, idx) => {
              const IconComponent = getIconForMilestone(milestone.id);
              return (
                <motion.div
                  key={milestone.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: (requiredMilestones.length + idx) * 0.1 }}
                  className={`flex items-center gap-4 p-3 rounded-lg border transition-all duration-300 ${
                    milestone.completed
                      ? 'bg-blue-500/10 border-blue-500/30 text-blue-300'
                      : 'bg-accent-white/3 border-accent-white/5 text-accent-white/60 hover:border-accent-white/20'
                  }`}
                >
                  <div className="flex-shrink-0">
                    {milestone.completed ? (
                      <CheckCircle2 className="h-5 w-5 text-blue-400" />
                    ) : (
                      <Circle className="h-5 w-5 text-accent-white/30" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <IconComponent className="h-3 w-3 text-accent-white/50" />
                      <h4 className={`text-sm font-medium ${milestone.completed ? 'line-through' : ''}`}>
                        {milestone.title}
                      </h4>
                    </div>
                    <MarkdownText className="text-xs text-accent-white/50">
                      {milestone.description}
                    </MarkdownText>
                  </div>
                  
                  {milestone.completed && (
                    <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30 text-xs">
                      Bonus
                    </Badge>
                  )}
                </motion.div>
              );
            })}
          </CardContent>
        </Card>
      )}
    </motion.div>
  );
};

export default StageChecklist; 