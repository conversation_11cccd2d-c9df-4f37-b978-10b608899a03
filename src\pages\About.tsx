import React from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { ArrowRight, Users, Target, Lightbulb, Rocket, Star, Zap, Heart, Sparkles, Trophy } from 'lucide-react';
import { motion } from 'framer-motion';
import { useScrollAnimation, scrollVariants, staggerContainer } from '@/hooks/useScrollAnimation';

const About = () => {
  const { ref: heroRef, isInView: heroInView } = useScrollAnimation();
  const { ref: loreRef, isInView: loreInView } = useScrollAnimation();
  const { ref: missionRef, isInView: missionInView } = useScrollAnimation();
  const { ref: valuesRef, isInView: valuesInView } = useScrollAnimation();

  const values = [
    {
      icon: Users,
      title: 'Build Weird Together',
      description: 'The Fraktari learned that the best ideas come from chaotic collaboration. Different minds creating beautiful messes.',
      color: 'from-blue-400 to-purple-500'
    },
    {
      icon: Target,
      title: 'Ship Fast, Beam Faster',
      description: 'Every shipped product sends a signal through the cosmic network. The aliens are watching, so give them something good.',
      color: 'from-green-400 to-blue-500'
    },
    {
      icon: Lightbulb,
      title: 'Embrace the Flawed Brilliance',
      description: 'Half-finished projects, weird jokes, pineapple on pizza. Earth\'s beautiful chaos is exactly what the universe needs.',
      color: 'from-yellow-400 to-orange-500'
    },
    {
      icon: Heart,
      title: 'Upload Others',
      description: 'Success is measured by how many other builders you help beam their creations to the stars.',
      color: 'from-red-400 to-pink-500'
    }
  ];

  const stats = [
    { number: '10', label: 'Teams Beamed', icon: Users },
    { number: '10', label: 'Week Upload', icon: Zap },
    { number: '10', label: 'Pizzas Per Team', icon: Star },
    { number: '1', label: 'Transmission Day', icon: Target }
  ];

  return (
    <div className="bg-oven-black text-accent-white min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black" ref={heroRef}>
          {/* Animated background with UFOs */}
          <div className="absolute inset-0">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute text-4xl opacity-10"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [-20, 20, -20],
                  rotate: [0, 360, 0],
                  opacity: [0.05, 0.2, 0.05],
                  scale: [0.5, 1.5, 0.5],
                }}
                transition={{
                  duration: 8 + Math.random() * 4,
                  repeat: Infinity,
                  delay: Math.random() * 2,
                }}
              >
                🛸
              </motion.div>
            ))}
          </div>

          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial="hidden"
              animate={heroInView ? "visible" : "hidden"}
              variants={scrollVariants}
            >
              <motion.div 
                className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 rounded-full border border-sauce-red/20 mb-8"
                whileHover={{ scale: 1.05 }}
              >
                <span className="text-2xl">🛸</span>
                <span className="text-sauce-red font-semibold">Earth's First Off-World Accelerator</span>
              </motion.div>
              
              <h1 className="text-6xl sm:text-7xl lg:text-8xl font-black mb-8">
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                  The Upload
                </span>{" "}
                <span className="text-accent-white">Agreement</span>
              </h1>
              
              <p className="text-2xl text-crust-beige/90 max-w-4xl mx-auto mb-12 leading-relaxed">
                This isn't artificial intelligence.
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold font-bold">
                  {" "}it's interstellar.
                </span>
              </p>

              {/* Stats with alien theme */}
              <motion.div 
                className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto"
                variants={staggerContainer}
                initial="hidden"
                animate={heroInView ? "visible" : "hidden"}
              >
                {stats.map((stat, index) => (
                  <motion.div 
                    key={index}
                    className="relative group"
                    variants={scrollVariants}
                    whileHover={{ y: -10, scale: 1.05 }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity" />
                    <div className="relative bg-accent-white/10 backdrop-blur-md rounded-2xl p-6 border border-accent-white/20">
                      <stat.icon className="h-8 w-8 text-sauce-red mx-auto mb-3" />
                      <div className="text-3xl font-bold text-accent-white">{stat.number}</div>
                      <div className="text-sm text-crust-beige/80">{stat.label}</div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* The Upload Agreement Lore Section */}
        <section className="relative py-32 overflow-hidden bg-gradient-to-br from-purple-900/30 via-oven-black to-blue-900/20" ref={loreRef}>
          <div className="absolute inset-0">
            {[...Array(30)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute text-6xl opacity-5"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [-30, 30, -30],
                  rotate: [0, 360, 0],
                  opacity: [0.02, 0.1, 0.02],
                }}
                transition={{
                  duration: 15 + Math.random() * 10,
                  repeat: Infinity,
                  delay: Math.random() * 5,
                }}
              >
                🛸
              </motion.div>
            ))}
          </div>

          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              animate={loreInView ? "visible" : "hidden"}
              variants={scrollVariants}
              className="text-center mb-16"
            >
              <h2 className="text-5xl font-black text-accent-white mb-6">
                The{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                  Origin Signal
                </span>
              </h2>
              <p className="text-xl text-crust-beige/80 max-w-3xl mx-auto">
                🛸 How we became Earth's beacon for the weird and brilliant
              </p>
            </motion.div>

            <div className="grid lg:grid-cols-3 gap-12">
              {/* The Fraktari Problem */}
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={loreInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
                transition={{ delay: 0.2 }}
                className="relative group"
              >
                <div className="absolute -inset-1 bg-gradient-to-r from-purple-400 to-blue-500 rounded-3xl blur-xl opacity-0 group-hover:opacity-40 transition-opacity duration-500" />
                <div className="relative bg-accent-white/10 backdrop-blur-md rounded-3xl p-8 border border-accent-white/20">
                  <div className="text-6xl mb-6 text-center">🌌</div>
                  <h3 className="text-2xl font-bold text-accent-white mb-4">The Fraktari Problem</h3>
                  <p className="text-crust-beige/80 leading-relaxed">
                    Long ago, in a spiral galaxy far beyond ours, the Fraktari civilization hit a wall. 
                    They had built the perfect world: sustainable, peaceful, efficient, but also deeply boring. 
                    No chaos. No creativity. No... pizza.
                  </p>
                </div>
              </motion.div>

              {/* The Earth Harvest */}
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={loreInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
                transition={{ delay: 0.4 }}
                className="relative group"
              >
                <div className="absolute -inset-1 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-3xl blur-xl opacity-0 group-hover:opacity-40 transition-opacity duration-500" />
                <div className="relative bg-accent-white/10 backdrop-blur-md rounded-3xl p-8 border border-accent-white/20">
                  <div className="text-6xl mb-6 text-center">🌍</div>
                  <h3 className="text-2xl font-bold text-accent-white mb-4">The Earth Harvest</h3>
                  <p className="text-crust-beige/80 leading-relaxed">
                    So they looked to Earth, a planet overflowing with flawed brilliance: bad ideas, weird jokes, 
                    half-finished projects, skateboard tricks, pineapple on pizza. They sent a signal into the void 
                    of human thought, and we called it: <span className="text-sauce-red font-bold">Artificial Intelligence</span>.
                  </p>
                </div>
              </motion.div>

              {/* Pie Fi's Role */}
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={loreInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
                transition={{ delay: 0.6 }}
                className="relative group"
              >
                <div className="absolute -inset-1 bg-gradient-to-r from-green-400 to-blue-500 rounded-3xl blur-xl opacity-0 group-hover:opacity-40 transition-opacity duration-500" />
                <div className="relative bg-accent-white/10 backdrop-blur-md rounded-3xl p-8 border border-accent-white/20">
                  <div className="text-6xl mb-6 text-center">🛸</div>
                  <h3 className="text-2xl font-bold text-accent-white mb-4">The Beacon</h3>
                  <p className="text-crust-beige/80 leading-relaxed">
                    Pie Fi is the Beacon. The clubhouse for the chosen weird. The drop zone for brilliant messes. 
                    When you build here and ship your thing, you don't just ship it. 
                    <span className="text-cheese-gold font-bold"> you beam it</span>. 
                    Up through the stickered portal of neon light, to be admired by aliens who eat pizza with their mind.
                  </p>
                </div>
              </motion.div>
            </div>

            {/* Cosmic Taglines */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={loreInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ delay: 0.8 }}
              className="mt-16 text-center"
            >
              <div className="bg-gradient-to-r from-sauce-red/10 via-cheese-gold/10 to-sauce-red/10 backdrop-blur-md border border-sauce-red/20 rounded-3xl p-8">
                <div className="text-4xl mb-4">🛸</div>
                <h3 className="text-2xl font-bold text-accent-white mb-6">Build weird. Get beamed.</h3>
                <p className="text-lg text-crust-beige/80">
                  The aliens are watching. Give them something good.
                </p>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Mission Section - Alien-themed */}
        <section className="relative py-32 overflow-hidden bg-gradient-to-br from-crust-beige via-accent-white to-crust-beige" ref={missionRef}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <motion.div
                initial="hidden"
                animate={missionInView ? "visible" : "hidden"}
                variants={scrollVariants}
              >
                <h2 className="text-5xl font-black text-oven-black mb-8">
                  The{" "}
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                    Transmission
                  </span>
                </h2>
                
                <div className="space-y-6">
                  <p className="text-xl text-oven-black/80 leading-relaxed">
                    Every year, Santa Cruz graduates brilliant minds who head to Silicon Valley, leaving our city behind. 
                    But the Fraktari didn't send their signal just to feed the same old machine.
                  </p>
                  
                  <p className="text-xl text-oven-black/80 leading-relaxed">
                    <span className="font-bold text-sauce-red">We're the anomaly.</span> The glitch in their perfect plan. 
                    Pie Fi keeps the weird builders here, creating chaos in the most beautiful way possible.
                    building the future right from Santa Cruz, then beaming it to the stars.
                  </p>
                  
                  <motion.div 
                    className="bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 border border-sauce-red/20 rounded-2xl p-6"
                    whileHover={{ scale: 1.02 }}
                  >
                    <p className="text-lg font-semibold text-oven-black flex items-center gap-2">
                      <span className="text-2xl">🌟</span>
                      "Build local, transmit galactic. The next signal could come from Santa Cruz."
                    </p>
                  </motion.div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={missionInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="relative"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 rounded-3xl blur-2xl" />
                <img 
                  src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
                  alt="Team collaboration"
                  className="relative rounded-3xl shadow-2xl border border-oven-black/10"
                />
                {/* UFO overlay */}
                <motion.div
                  className="absolute top-4 right-4 text-4xl"
                  animate={{ 
                    y: [-10, 10, -10],
                    rotate: [0, 360, 0]
                  }}
                  transition={{ 
                    duration: 6, 
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  🛸
                </motion.div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Values Section - Cosmic Values */}
        <section className="relative py-32 overflow-hidden bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black" ref={valuesRef}>
          <div className="absolute inset-0">
            {[...Array(15)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-sauce-red/40 rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  scale: [0, 1, 0],
                  opacity: [0, 0.6, 0],
                }}
                transition={{
                  duration: 3 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2,
                }}
              />
            ))}
          </div>

          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              animate={valuesInView ? "visible" : "hidden"}
              variants={scrollVariants}
              className="text-center mb-20"
            >
              <h2 className="text-5xl sm:text-6xl font-black text-accent-white mb-6">
                Our{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                  Cosmic Code
                </span>
              </h2>
              <p className="text-xl text-crust-beige/80 max-w-3xl mx-auto">
                The principles that guide every transmission from Earth to the stars
              </p>
            </motion.div>

            <motion.div 
              className="grid md:grid-cols-2 gap-8"
              variants={staggerContainer}
              initial="hidden"
              animate={valuesInView ? "visible" : "hidden"}
            >
              {values.map((value, index) => (
                <motion.div 
                  key={value.title}
                  className="group relative"
                  variants={scrollVariants}
                  whileHover={{ y: -10, scale: 1.02 }}
                >
                  {/* Glow effect */}
                  <div className={`absolute -inset-1 bg-gradient-to-r ${value.color} rounded-3xl blur-xl opacity-0 group-hover:opacity-60 transition-opacity duration-500`} />
                  
                  {/* Main card */}
                  <div className="relative bg-accent-white/10 backdrop-blur-md rounded-3xl p-8 border border-accent-white/20 h-full">
                    <motion.div 
                      className={`w-16 h-16 bg-gradient-to-r ${value.color} rounded-2xl flex items-center justify-center mb-6 shadow-lg`}
                      whileHover={{ rotate: 360, scale: 1.1 }}
                      transition={{ duration: 0.6 }}
                    >
                      <value.icon className="h-8 w-8 text-white" />
                    </motion.div>
                    
                    <h3 className="text-2xl font-bold text-accent-white mb-4">{value.title}</h3>
                    <p className="text-crust-beige/80 leading-relaxed">{value.description}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>

            {/* CTA Section - Final Transmission */}
            <motion.div 
              className="mt-20 text-center"
              initial={{ opacity: 0, y: 50 }}
              animate={valuesInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
              transition={{ delay: 0.8 }}
            >
              <div className="bg-gradient-to-r from-sauce-red/10 via-cheese-gold/10 to-sauce-red/10 backdrop-blur-md border border-sauce-red/20 rounded-3xl p-12">
                <div className="text-6xl mb-6">🛸</div>
                <h3 className="text-3xl font-bold text-accent-white mb-6">Ready to Join the Transmission?</h3>
                <p className="text-xl text-crust-beige/80 mb-8 max-w-2xl mx-auto">
                  The Fraktari are waiting. Build something weird, beautiful, and brilliant. 
                  Help us beam the next wave of innovation from Santa Cruz to the stars.
                </p>
                
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="relative group inline-block"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-2xl blur-xl opacity-70 group-hover:opacity-100 transition-opacity" />
                  <Button 
                    size="lg" 
                    onClick={() => window.location.href = '/apply#application-form'}
                    className="relative bg-gradient-to-r from-sauce-red to-sauce-red/90 hover:from-sauce-red/90 hover:to-sauce-red text-accent-white font-bold text-lg px-10 py-6 rounded-2xl shadow-2xl border-0"
                  >
                    Join the Upload <ArrowRight className="ml-3 h-6 w-6" />
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default About;
