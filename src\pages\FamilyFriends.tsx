import React, { useState, useRef, useEffect } from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Users, 
  Heart, 
  DollarSign, 
  MessageCircle, 
  Share2, 
  Calendar,
  Mail,
  Coffee,
  Star,
  Target,
  Handshake,
  Sparkles,
  Phone,
  User,
  ChevronDown,
  Check,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { motion } from 'framer-motion';
import { useScrollAnimation, scrollVariants, staggerContainer } from '@/hooks/useScrollAnimation';
import { supabaseAdmin } from '@/integrations/supabase/admin-client';
import { useToast } from '@/hooks/use-toast';

// Custom Mobile Select Component
const MobileSelect = ({ value, onValueChange, placeholder, children, className, triggerClassName }: {
  value: string;
  onValueChange: (value: string) => void;
  placeholder: string;
  children: React.ReactNode;
  className?: string;
  triggerClassName?: string;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [openUpward, setOpenUpward] = useState(false);
  const triggerRef = useRef(null);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.matchMedia("(max-width: 768px)").matches);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target) && 
          triggerRef.current && !triggerRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  // Check if dropdown should open upward
  const checkDropdownPosition = () => {
    if (!triggerRef.current) return;
    
    const buttonRect = triggerRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const dropdownHeight = 320; // Increased for safety
    const submitButtonBuffer = 200; // Much larger buffer to avoid submit button overlap
    
    // Very aggressive positioning - open upward if in middle third of screen or below
    const spaceBelow = viewportHeight - buttonRect.bottom - submitButtonBuffer;
    const spaceAbove = buttonRect.top;
    
    // Open upward if we're in the bottom 50% of screen OR insufficient space below
    const isInBottomHalf = buttonRect.bottom > viewportHeight * 0.5;
    const hasInsufficientSpace = spaceBelow < dropdownHeight;
    
    // Also check if there's a submit button nearby (within 300px below)
    const submitButtonNearby = (viewportHeight - buttonRect.bottom) < 300;
    
    if (isInBottomHalf || hasInsufficientSpace || submitButtonNearby) {
      setOpenUpward(true);
    } else {
      setOpenUpward(false);
    }
  };

  const handleSelect = (newValue) => {
    onValueChange(newValue);
    setIsOpen(false);
  };

  const handleToggle = () => {
    if (!isOpen) {
      checkDropdownPosition();
    }
    setIsOpen(!isOpen);
  };

  const getSelectedLabel = () => {
    const selectedChild = React.Children.toArray(children).find(child => {
      if (React.isValidElement(child)) {
        return child.props.value === value;
      }
      return false;
    });
    return selectedChild && React.isValidElement(selectedChild) ? selectedChild.props.children : placeholder;
  };

  if (!isMobile) {
    return (
      <Select value={value} onValueChange={onValueChange}>
        <SelectTrigger className={triggerClassName}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent className={className}>
          {children}
        </SelectContent>
      </Select>
    );
  }

  return (
    <div className="relative">
      <button
        ref={triggerRef}
        type="button"
        onClick={handleToggle}
        className="w-full flex items-center justify-between bg-white/80 border border-oven-black/20 text-oven-black placeholder:text-oven-black/60 rounded-xl h-12 text-lg md:text-lg text-base focus:border-sauce-red focus:ring-sauce-red transition-all duration-300 px-3 py-2"
      >
        <span className={`${!value ? 'text-oven-black/60 text-sm md:text-base' : 'text-oven-black text-base md:text-lg'} truncate text-left`}>
          {getSelectedLabel()}
        </span>
        <ChevronDown className={`h-4 w-4 ml-2 flex-shrink-0 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>
      
      {isOpen && (
        <div
          ref={dropdownRef}
          className={`absolute left-0 right-0 z-[99999] bg-white/95 backdrop-blur-md border border-oven-black/20 rounded-xl shadow-lg p-1 max-h-60 overflow-auto ${
            openUpward ? 'bottom-full mb-1' : 'top-full mt-1'
          }`}
        >
          {React.Children.map(children, (child) => {
            if (!React.isValidElement(child)) return null;
            return (
              <button
                key={child.props.value}
                type="button"
                onClick={() => handleSelect(child.props.value)}
                className="w-full px-3 py-3 text-left hover:bg-gradient-to-r hover:from-sauce-red/30 hover:to-cheese-gold/20 focus:bg-gradient-to-r focus:from-sauce-red/30 focus:to-cheese-gold/20 text-oven-black flex items-center justify-between rounded-lg transition-all duration-200 hover:shadow-md"
              >
                <span className="text-oven-black font-medium">{child.props.children}</span>
                {value === child.props.value && <Check className="h-4 w-4 text-sauce-red" />}
              </button>
            );
          })}
        </div>
      )}
    </div>
  );
};

const FamilyFriends = () => {
  const { toast } = useToast();
  const { ref: heroRef, isInView: heroInView } = useScrollAnimation();
  const { ref: rolesRef, isInView: rolesInView } = useScrollAnimation();
  const { ref: programsRef, isInView: programsInView } = useScrollAnimation();
  const { ref: formRef, isInView: formInView } = useScrollAnimation();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    relationship: '',
    role: '',
    interests: '',
    availability: '',
    skills: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<{[key: string]: string | null}>({});
  const [fieldTouched, setFieldTouched] = useState<{[key: string]: boolean}>({});
  const [validFields, setValidFields] = useState<{[key: string]: boolean}>({});

  // Field Error Display Component
  const FieldError = ({ error }: { error: string | null }) => {
    if (!error) return null;
    return (
      <motion.div 
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center gap-2 mt-2 text-red-500 text-sm"
      >
        <AlertCircle className="h-4 w-4" />
        <span>{error}</span>
      </motion.div>
    );
  };

  // Field Success Display Component  
  const FieldSuccess = ({ show }: { show: boolean }) => {
    if (!show) return null;
    return (
      <motion.div 
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        className="flex items-center gap-2 mt-2 text-green-500 text-sm"
      >
        <CheckCircle className="h-4 w-4" />
        <span>Looks good!</span>
      </motion.div>
    );
  };

  // Real-time field validation
  const validateField = (field: string, value: string): string | null => {
    const requiredFields = ['name', 'email', 'relationship', 'role', 'interests'];
    
    // Required field check
    if (requiredFields.includes(field) && !value.trim()) {
      const fieldName = field === 'name' ? 'Full Name' : 
                       field === 'email' ? 'Email' :
                       field === 'relationship' ? 'Relationship to Pie Fi' :
                       field === 'role' ? 'Primary Interest' :
                       field === 'interests' ? 'How You\'d Like to Support' : field;
      return `${fieldName} is required`;
    }

    // Field-specific validation
    switch (field) {
      case 'email':
        if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          return 'Please enter a valid email address';
        }
        break;
        
      case 'phone':
        if (value) {
          const cleanPhone = value.replace(/[\s\-\(\)\.]/g, '');
          if (!/^[\+]?[1-9][\d]{0,15}$/.test(cleanPhone) || cleanPhone.length < 10) {
            return 'Please enter a valid phone number (at least 10 digits)';
          }
        }
        break;
        
      case 'name':
        if (value && value.length < 2) {
          return 'Please enter your full name';
        }
        break;
        
      case 'relationship':
        if (value && value.length < 3) {
          return 'Please describe your relationship to Pie Fi';
        }
        break;
        
      case 'interests':
      case 'skills':
      case 'message':
        if (value && value.length > 0 && value.length < 10) {
          return 'Please provide more detail (at least 10 characters)';
        }
        break;
    }
    
    return null;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Mark field as touched
    if (!fieldTouched[name]) {
      setFieldTouched(prev => ({ ...prev, [name]: true }));
    }
    
    // Real-time validation
    const error = validateField(name, value);
    setFieldErrors(prev => ({ ...prev, [name]: error }));
    setValidFields(prev => ({ ...prev, [name]: !error && value.trim() !== '' }));
  };

  const handleSelectChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Mark field as touched
    if (!fieldTouched[field]) {
      setFieldTouched(prev => ({ ...prev, [field]: true }));
    }
    
    // Real-time validation
    const error = validateField(field, value);
    setFieldErrors(prev => ({ ...prev, [field]: error }));
    setValidFields(prev => ({ ...prev, [field]: !error && value.trim() !== '' }));
  };

  const handleBlur = (field: string) => {
    setFieldTouched(prev => ({ ...prev, [field]: true }));
    const value = formData[field as keyof typeof formData];
    const error = validateField(field, value);
    setFieldErrors(prev => ({ ...prev, [field]: error }));
    setValidFields(prev => ({ ...prev, [field]: !error && value.trim() !== '' }));
  };

  // Enhanced form validation
  const validateForm = (): boolean => {
    const requiredFields = ['name', 'email', 'relationship', 'role', 'interests'];
    let hasErrors = false;
    const newErrors: {[key: string]: string | null} = {};

    // Validate all fields
    for (const field of Object.keys(formData)) {
      const error = validateField(field, formData[field as keyof typeof formData]);
      if (error) {
        newErrors[field] = error;
        hasErrors = true;
      }
    }

    // Mark all required fields as touched
    const allTouched = requiredFields.reduce((acc, field) => ({ ...acc, [field]: true }), {});
    setFieldTouched(prev => ({ ...prev, ...allTouched }));
    setFieldErrors(newErrors);

    if (hasErrors) {
      // Find first error field and scroll to it
      const firstErrorField = Object.keys(newErrors)[0];
      const element = document.getElementById(firstErrorField);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        element.focus();
      }
      
      toast({
        title: "Form Validation",
        description: "Please fix the errors in the form before submitting",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  // Get field styling based on validation state
  const getFieldStyling = (fieldId: string) => {
    const hasError = fieldTouched[fieldId] && fieldErrors[fieldId];
    const isValid = validFields[fieldId] && !fieldErrors[fieldId];
    
    let borderColor = 'border-oven-black/20';
    let focusColor = 'focus:border-sauce-red focus:ring-sauce-red';
    
    if (hasError) {
      borderColor = 'border-red-400';
      focusColor = 'focus:border-red-400 focus:ring-red-400';
    } else if (isValid) {
      borderColor = 'border-green-400';
      focusColor = 'focus:border-green-400 focus:ring-green-400';
    }
    
    return `bg-white/80 ${borderColor} text-oven-black h-12 text-lg md:text-lg text-base rounded-xl ${focusColor} transition-all duration-300 placeholder:text-sm md:placeholder:text-base`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Save supporter data to applications table with application_type 'supporter' for differentiation
      const supporterApplicationData = {
        full_name: formData.name.trim(),
        email: formData.email.trim().toLowerCase(),
        phone: formData.phone.trim() || null,
        university: 'Community Supporter',
        year: 'N/A',
        major: formData.relationship,
        graduation_year: null,
        skills: `${formData.role} - ${formData.interests}${formData.skills ? ` | Skills: ${formData.skills}` : ''}`,
        project_idea: formData.message || null,
        team_status: 'supporter',
        why_pie_fi: `Community supporter interested in: ${formData.role}. Relationship: ${formData.relationship}. ${formData.message || ''}`.trim(),
        linkedin: null,
        website: null,
        github: null,
        previous_projects: formData.availability ? `Availability: ${formData.availability}` : null,
        availability: formData.availability || null,
        references: `Supporter Type: ${formData.role} | Relationship: ${formData.relationship}`,
        resume_url: null,
        status: 'pending',
        application_type: 'supporter'
      };

      const { data: result, error } = await supabaseAdmin
        .from('applications')
        .insert([supporterApplicationData])
        .select();

      if (error) {
        throw error;
      }

      console.log('Supporter saved successfully:', result);
      toast({
        title: "Welcome!",
        description: "🎉 Welcome to the Pie Fi community! We'll be in touch soon with exciting ways to get involved.",
      });
      
      // Reset form
      setFormData({
        name: '',
        email: '',
        phone: '',
        relationship: '',
        role: '',
        interests: '',
        availability: '',
        skills: '',
        message: ''
      });
      setFieldErrors({});
      setFieldTouched({});
      setValidFields({});
      
    } catch (error) {
      console.error('Error submitting supporter form:', error);
      
      // Show more specific error messages
      if (error.code === '23505') {
        toast({
          title: "Duplicate Registration",
          description: "This email is already registered as a supporter. Thank you for your continued interest!",
          variant: "destructive",
        });
      } else if (error.message?.includes('rate limit')) {
        toast({
          title: "Rate Limit",
          description: "Too many submissions. Please wait a moment before trying again.",
          variant: "destructive",
        });
      } else if (error.message?.includes('network') || error.message?.includes('fetch')) {
        toast({
          title: "Network Error",
          description: "Network error. Please check your connection and try again.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Submission Error",
          description: "An error occurred while saving your information. Please try again or contact us directly.",
          variant: "destructive",
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const communityRoles = [
    {
      role: 'Backers',
      description: 'Provide financial support for builders or the program',
      benefits: 'Watch your investment fuel the next generation of local innovators while building meaningful connections',
      icon: DollarSign,
      color: 'from-green-400 to-green-600'
    },
    {
      role: 'Mentors',
      description: 'Share career or domain expertise with builders',
      benefits: 'Share your expertise, see it shape young founders, and stay connected to cutting-edge innovation',
      icon: Target,
      color: 'from-blue-400 to-blue-600'
    },
    {
      role: 'Advocates',
      description: 'Share Pie Fi with their networks and companies',
      benefits: 'Help grow a movement while expanding your own network and building your personal brand',
      icon: Share2,
      color: 'from-purple-400 to-purple-600'
    },
    {
      role: 'Hosts & Helpers',
      description: 'Help with logistics, space, food, event support',
      benefits: 'Get hands-on involvement in building something special while meeting amazing people',
      icon: Coffee,
      color: 'from-orange-400 to-orange-600'
    },
    {
      role: 'Advisory Voices',
      description: 'Join listening sessions or informal boards',
      benefits: 'Shape the future of entrepreneurship education in Santa Cruz while gaining fresh perspectives',
      icon: MessageCircle,
      color: 'from-red-400 to-red-600'
    }
  ];

  const engagementPrograms = [
    {
      format: 'Community Launch Event',
      description: 'Join us for the program kickoff and meet all the builders',
      benefits: 'Connect with fellow supporters and feel the excitement firsthand',
      icon: Calendar,
      color: 'from-pink-400 to-pink-600'
    },
    {
      format: 'Progress Updates',
      description: 'Monthly emails with builder stories, milestones, and wins',
      benefits: 'Stay connected to the journey and celebrate achievements',
      icon: Mail,
      color: 'from-teal-400 to-teal-600'
    },
    {
      format: 'Supporter Spotlights',
      description: 'Share your story and why you believe in local innovation',
      benefits: 'Inspire others to join while building your personal brand',
      icon: Star,
      color: 'from-yellow-400 to-yellow-600'
    },
    {
      format: 'Builder Sponsorship',
      description: 'Directly support a specific builder through their journey',
      benefits: 'Form a personal connection and mentorship opportunity',
      icon: Handshake,
      color: 'from-indigo-400 to-indigo-600'
    },
    {
      format: 'Supporter Community',
      description: 'Join our private group for updates, discussions, and networking',
      benefits: 'Connect with like-minded people building the local ecosystem',
      icon: MessageCircle,
      color: 'from-cyan-400 to-cyan-600'
    },
    {
      format: 'Intimate Gatherings',
      description: 'Small dinners and events for deeper conversations',
      benefits: 'Build lasting relationships and meaningful connections',
      icon: Users,
      color: 'from-violet-400 to-violet-600'
    }
  ];

  return (
    <div className="bg-oven-black text-accent-white min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black" ref={heroRef}>
          {/* Animated background */}
          <div className="absolute inset-0">
            {[...Array(25)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [-30, 30, -30],
                  x: [-20, 20, -20],
                  opacity: [0.2, 0.8, 0.2],
                  scale: [0.5, 1.2, 0.5],
                }}
                transition={{
                  duration: 6 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2,
                }}
              />
            ))}
          </div>

          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial="hidden"
              animate={heroInView ? "visible" : "hidden"}
              variants={scrollVariants}
            >
              <motion.div 
                className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 rounded-full border border-sauce-red/20 mb-8"
                whileHover={{ scale: 1.05 }}
              >
                <Heart className="w-4 h-4 text-sauce-red" />
                <span className="text-sauce-red font-semibold">Community Builders Program</span>
              </motion.div>
              
              <h1 className="text-6xl sm:text-7xl lg:text-8xl font-black mb-8">
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                  Foster the
                </span>{" "}
                <span className="text-accent-white">Community</span>
              </h1>
              
              <p className="text-2xl text-crust-beige/90 max-w-4xl mx-auto mb-12 leading-relaxed">
                Join us in creating something extraordinary in Santa Cruz.
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold font-bold">
                  {" "}a thriving ecosystem where brilliant ideas become reality and meaningful connections flourish.
                </span>
              </p>

              <motion.div 
                className="inline-flex items-center gap-3 bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 backdrop-blur-sm text-accent-white px-8 py-4 rounded-2xl border border-sauce-red/30"
                animate={{ 
                  boxShadow: [
                    "0 0 0 0 rgba(255, 71, 71, 0.3)", 
                    "0 0 0 20px rgba(255, 71, 71, 0)", 
                    "0 0 0 0 rgba(255, 71, 71, 0)"
                  ]
                }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <Users className="h-5 w-5 text-sauce-red" />
                <span className="font-semibold text-lg">Building the future, together</span>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* Community Roles Section */}
        <section className="relative py-16 sm:py-24 lg:py-32 overflow-hidden bg-gradient-to-br from-crust-beige via-accent-white to-crust-beige" ref={rolesRef}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              animate={rolesInView ? "visible" : "hidden"}
              variants={scrollVariants}
              className="text-center mb-8 sm:mb-12 lg:mb-20"
            >
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-black text-oven-black mb-4 sm:mb-6 px-2">
                Ways to{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                  Make an Impact
                </span>
              </h2>
              <p className="text-base sm:text-lg lg:text-xl text-oven-black/70 max-w-3xl mx-auto px-4">
                Every contribution creates ripple effects. Join a community of changemakers investing in Santa Cruz's entrepreneurial future.
              </p>
            </motion.div>

            <motion.div
              className="flex flex-wrap justify-center gap-4 sm:gap-6 lg:gap-8"
              variants={staggerContainer}
              initial="hidden"
              animate={rolesInView ? "visible" : "hidden"}
            >
              {communityRoles.map((role, index) => (
                <motion.div
                  key={role.role}
                  className="group relative w-full max-w-sm"
                  variants={scrollVariants}
                  whileHover={{ y: -10, scale: 1.02 }}
                >
                  {/* Glow effect */}
                  <div className={`absolute -inset-1 bg-gradient-to-r ${role.color} rounded-2xl sm:rounded-3xl blur-xl opacity-0 group-hover:opacity-40 transition-opacity duration-500`} />
                  
                  {/* Main card */}
                  <Card className="relative bg-accent-white/90 backdrop-blur-md rounded-2xl sm:rounded-3xl border border-oven-black/10 shadow-xl h-full">
                    <CardHeader className="pb-3 sm:pb-4 p-4 sm:p-6">
                      <div className="flex items-center gap-3 sm:gap-4 mb-3 sm:mb-4">
                        <motion.div 
                          className={`w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r ${role.color} rounded-xl sm:rounded-2xl flex items-center justify-center shadow-lg`}
                          whileHover={{ rotate: 360, scale: 1.1 }}
                          transition={{ duration: 0.6 }}
                        >
                          <role.icon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                        </motion.div>
                        <CardTitle className="text-lg sm:text-xl font-bold text-oven-black">{role.role}</CardTitle>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="space-y-3 sm:space-y-4 p-4 sm:p-6 pt-0">
                      <p className="text-oven-black/70 leading-relaxed text-sm sm:text-base">{role.description}</p>
                      <div className="p-3 sm:p-4 bg-gradient-to-r from-sauce-red/5 to-cheese-gold/5 rounded-xl border border-sauce-red/10">
                        <p className="text-xs sm:text-sm font-semibold text-oven-black/80 mb-1 sm:mb-2">Your Impact & Benefits:</p>
                        <p className="text-xs sm:text-sm text-oven-black/70">{role.benefits}</p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Engagement Programs Section */}
        <section className="relative py-16 sm:py-24 lg:py-32 overflow-hidden bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black" ref={programsRef}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              animate={programsInView ? "visible" : "hidden"}
              variants={scrollVariants}
              className="text-center mb-8 sm:mb-12 lg:mb-20"
            >
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-black text-accent-white mb-4 sm:mb-6 px-2">
                Stay{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                  Connected
                </span>
              </h2>
              <p className="text-base sm:text-lg lg:text-xl text-crust-beige/80 max-w-3xl mx-auto px-4">
                Multiple ways to stay engaged, build relationships, and be part of the journey
              </p>
            </motion.div>

            <motion.div 
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8"
              variants={staggerContainer}
              initial="hidden"
              animate={programsInView ? "visible" : "hidden"}
            >
              {engagementPrograms.map((program, index) => (
                <motion.div 
                  key={program.format}
                  className="group relative"
                  variants={scrollVariants}
                  whileHover={{ y: -10, scale: 1.02 }}
                >
                  {/* Glow effect */}
                  <div className={`absolute -inset-1 bg-gradient-to-r ${program.color} rounded-2xl sm:rounded-3xl blur-xl opacity-0 group-hover:opacity-40 transition-opacity duration-500`} />
                  
                  {/* Main card */}
                  <Card className="relative bg-accent-white/10 backdrop-blur-md rounded-2xl sm:rounded-3xl border border-accent-white/20 shadow-xl h-full">
                    <CardHeader className="pb-3 sm:pb-4 p-4 sm:p-6">
                      <div className="flex items-center gap-3 sm:gap-4 mb-3 sm:mb-4">
                        <motion.div 
                          className={`w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r ${program.color} rounded-xl sm:rounded-2xl flex items-center justify-center shadow-lg`}
                          whileHover={{ rotate: 360, scale: 1.1 }}
                          transition={{ duration: 0.6 }}
                        >
                          <program.icon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                        </motion.div>
                        <CardTitle className="text-lg sm:text-xl font-bold text-accent-white">{program.format}</CardTitle>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="space-y-3 sm:space-y-4 p-4 sm:p-6 pt-0">
                      <p className="text-crust-beige/80 leading-relaxed text-sm sm:text-base">{program.description}</p>
                      <div className="p-3 sm:p-4 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 rounded-xl border border-sauce-red/20">
                        <p className="text-xs sm:text-sm font-semibold text-accent-white mb-1 sm:mb-2">What You'll Get:</p>
                        <p className="text-xs sm:text-sm text-crust-beige/70">{program.benefits}</p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Get Involved Form Section */}
        <section className="relative py-16 sm:py-24 lg:py-32 overflow-hidden bg-gradient-to-br from-crust-beige via-accent-white to-crust-beige" ref={formRef}>
          {/* Animated background particles */}
          <div className="absolute inset-0">
            {[...Array(15)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-full opacity-30"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [-20, 20, -20],
                  x: [-15, 15, -15],
                  opacity: [0.2, 0.6, 0.2],
                }}
                transition={{
                  duration: 4 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2,
                }}
              />
            ))}
          </div>

          <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              animate={formInView ? "visible" : "hidden"}
              variants={scrollVariants}
              className="text-center mb-12"
            >
              <h2 className="text-5xl font-black text-oven-black mb-6">
                Get{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                  Involved
                </span>
              </h2>
              <p className="text-xl text-oven-black/70 max-w-2xl mx-auto">
                Ready to help build something amazing? Tell us about yourself and how you'd like to contribute to our community.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30, scale: 0.95 }}
              animate={formInView ? { opacity: 1, y: 0, scale: 1 } : { opacity: 0, y: 30, scale: 0.95 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="relative group"
            >
              {/* Glow effect */}
              <div className="absolute -inset-1 bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 rounded-3xl blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-500" />
              
              <Card className="relative bg-accent-white/95 backdrop-blur-md border border-oven-black/10 shadow-2xl rounded-3xl overflow-visible">
                <CardHeader className="bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 pb-6">
                  <CardTitle className="flex items-center space-x-3 text-oven-black text-2xl">
                    <motion.div
                      className="p-3 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-2xl"
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.5 }}
                    >
                      <Heart className="h-8 w-8 text-white" />
                    </motion.div>
                    <span className="font-black">Join Our Community</span>
                  </CardTitle>
                </CardHeader>
                
                <CardContent className="p-8">
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="group/field relative">
                        <div className="absolute -inset-1 bg-gradient-to-r from-blue-400 to-blue-600 rounded-2xl blur-lg opacity-0 group-hover/field:opacity-20 transition-opacity duration-300" />
                        <div className="relative">
                          <Label htmlFor="name" className="flex items-center gap-3 text-oven-black font-semibold mb-3 text-lg">
                            <motion.div 
                              className="p-2 bg-gradient-to-r from-blue-400 to-blue-600 rounded-xl"
                              whileHover={{ scale: 1.1, rotate: 10 }}
                            >
                              <User className="h-5 w-5 text-white" />
                            </motion.div>
                            Full Name *
                          </Label>
                          <Input
                            id="name"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            onBlur={() => handleBlur('name')}
                            required
                            className={getFieldStyling('name')}
                            placeholder="Your full name"
                          />
                          <FieldError error={fieldErrors['name']} />
                        </div>
                      </div>
                      <div className="group/field relative">
                        <div className="absolute -inset-1 bg-gradient-to-r from-green-400 to-green-600 rounded-2xl blur-lg opacity-0 group-hover/field:opacity-20 transition-opacity duration-300" />
                        <div className="relative">
                          <Label htmlFor="email" className="flex items-center gap-3 text-oven-black font-semibold mb-3 text-lg">
                            <motion.div 
                              className="p-2 bg-gradient-to-r from-green-400 to-green-600 rounded-xl"
                              whileHover={{ scale: 1.1, rotate: 10 }}
                            >
                              <Mail className="h-5 w-5 text-white" />
                            </motion.div>
                            Email *
                          </Label>
                          <Input
                            id="email"
                            name="email"
                            type="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            onBlur={() => handleBlur('email')}
                            required
                            className={getFieldStyling('email')}
                            placeholder="<EMAIL>"
                          />
                          <FieldError error={fieldErrors['email']} />
                        </div>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="group/field relative">
                        <div className="absolute -inset-1 bg-gradient-to-r from-cyan-400 to-cyan-600 rounded-2xl blur-lg opacity-0 group-hover/field:opacity-20 transition-opacity duration-300" />
                        <div className="relative">
                          <Label htmlFor="phone" className="flex items-center gap-3 text-oven-black font-semibold mb-3 text-lg">
                            <motion.div 
                              className="p-2 bg-gradient-to-r from-cyan-400 to-cyan-600 rounded-xl"
                              whileHover={{ scale: 1.1, rotate: 10 }}
                            >
                              <Phone className="h-5 w-5 text-white" />
                            </motion.div>
                            Phone
                          </Label>
                          <Input
                            id="phone"
                            name="phone"
                            value={formData.phone}
                            onChange={handleInputChange}
                            onBlur={() => handleBlur('phone')}
                            className={getFieldStyling('phone')}
                            placeholder="+****************"
                          />
                          <FieldError error={fieldErrors['phone']} />
                        </div>
                      </div>
                      <div className="group/field relative">
                        <div className="absolute -inset-1 bg-gradient-to-r from-purple-400 to-purple-600 rounded-2xl blur-lg opacity-0 group-hover/field:opacity-20 transition-opacity duration-300" />
                        <div className="relative">
                          <Label htmlFor="relationship" className="flex items-center gap-3 text-oven-black font-semibold mb-3 text-lg">
                            <motion.div 
                              className="p-2 bg-gradient-to-r from-purple-400 to-purple-600 rounded-xl"
                              whileHover={{ scale: 1.1, rotate: 10 }}
                            >
                              <Users className="h-5 w-5 text-white" />
                            </motion.div>
                            Relationship to Pie Fi *
                          </Label>
                          <Input
                            id="relationship"
                            name="relationship"
                            value={formData.relationship}
                            onChange={handleInputChange}
                            onBlur={() => handleBlur('relationship')}
                            required
                            className={getFieldStyling('relationship')}
                            placeholder="Parent, Friend, Alumni, Local Business Owner, etc."
                          />
                          <FieldError error={fieldErrors['relationship']} />
                        </div>
                      </div>
                    </div>

                    <div className="group/field relative">
                      <div className="absolute -inset-1 bg-gradient-to-r from-orange-400 to-orange-600 rounded-2xl blur-lg opacity-0 group-hover/field:opacity-20 transition-opacity duration-300" />
                      <div className="relative">
                        <Label htmlFor="role" className="flex items-center gap-3 text-oven-black font-semibold mb-3 text-lg">
                          <motion.div 
                            className="p-2 bg-gradient-to-r from-orange-400 to-orange-600 rounded-xl"
                            whileHover={{ scale: 1.1, rotate: 10 }}
                          >
                            <Target className="h-5 w-5 text-white" />
                          </motion.div>
                          Primary Interest *
                        </Label>
                        <div className="relative group/field">
                          <div className="absolute -inset-1 bg-gradient-to-r from-orange-400 to-orange-600 rounded-2xl blur-lg opacity-0 group-hover/field:opacity-20 transition-opacity duration-300" />
                          <div className="relative">
                            <MobileSelect
                              value={formData.role}
                              onValueChange={(value) => handleSelectChange('role', value)}
                              placeholder="Select your primary interest..."
                              className="bg-white/95 backdrop-blur-md border-oven-black/20 rounded-xl text-oven-black"
                              triggerClassName={getFieldStyling('role')}
                            >
                              <SelectItem value="backer" className="text-oven-black hover:bg-gradient-to-r hover:from-sauce-red/30 hover:to-cheese-gold/20 focus:bg-gradient-to-r focus:from-sauce-red/30 focus:to-cheese-gold/20">Backer - Financial Support</SelectItem>
                              <SelectItem value="mentor" className="text-oven-black hover:bg-gradient-to-r hover:from-sauce-red/30 hover:to-cheese-gold/20 focus:bg-gradient-to-r focus:from-sauce-red/30 focus:to-cheese-gold/20">Mentor - Share Expertise</SelectItem>
                              <SelectItem value="advocate" className="text-oven-black hover:bg-gradient-to-r hover:from-sauce-red/30 hover:to-cheese-gold/20 focus:bg-gradient-to-r focus:from-sauce-red/30 focus:to-cheese-gold/20">Advocate - Network & Outreach</SelectItem>
                              <SelectItem value="host-helper" className="text-oven-black hover:bg-gradient-to-r hover:from-sauce-red/30 hover:to-cheese-gold/20 focus:bg-gradient-to-r focus:from-sauce-red/30 focus:to-cheese-gold/20">Host & Helper - Logistics Support</SelectItem>
                              <SelectItem value="advisory" className="text-oven-black hover:bg-gradient-to-r hover:from-sauce-red/30 hover:to-cheese-gold/20 focus:bg-gradient-to-r focus:from-sauce-red/30 focus:to-cheese-gold/20">Advisory Voice - Guidance & Feedback</SelectItem>
                            </MobileSelect>
                            <FieldError error={fieldErrors['role']} />
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="group/field relative">
                      <div className="absolute -inset-1 bg-gradient-to-r from-pink-400 to-pink-600 rounded-2xl blur-lg opacity-0 group-hover/field:opacity-20 transition-opacity duration-300" />
                      <div className="relative">
                        <Label htmlFor="interests" className="flex items-center gap-3 text-oven-black font-semibold mb-3 text-lg">
                          <motion.div 
                            className="p-2 bg-gradient-to-r from-pink-400 to-pink-600 rounded-xl"
                            whileHover={{ scale: 1.1, rotate: 10 }}
                          >
                            <Heart className="h-5 w-5 text-white" />
                          </motion.div>
                          How You'd Like to Support *
                        </Label>
                        <Textarea
                          id="interests"
                          name="interests"
                          value={formData.interests}
                          onChange={handleInputChange}
                          onBlur={() => handleBlur('interests')}
                          required
                          autoResize={true}
                          className={getFieldStyling('interests')}
                          placeholder="Tell us about your interests and how you'd like to contribute (financial, mentoring, networking, events, etc.)"
                        />
                        <FieldError error={fieldErrors['interests']} />
                      </div>
                    </div>

                    <div className="group/field relative">
                      <div className="absolute -inset-1 bg-gradient-to-r from-indigo-400 to-indigo-600 rounded-2xl blur-lg opacity-0 group-hover/field:opacity-20 transition-opacity duration-300" />
                      <div className="relative">
                        <Label htmlFor="skills" className="flex items-center gap-3 text-oven-black font-semibold mb-3 text-lg">
                          <motion.div 
                            className="p-2 bg-gradient-to-r from-indigo-400 to-indigo-600 rounded-xl"
                            whileHover={{ scale: 1.1, rotate: 10 }}
                          >
                            <Sparkles className="h-5 w-5 text-white" />
                          </motion.div>
                          Skills & Expertise
                        </Label>
                        <Textarea
                          id="skills"
                          name="skills"
                          value={formData.skills}
                          onChange={handleInputChange}
                          onBlur={() => handleBlur('skills')}
                          autoResize={true}
                          className={getFieldStyling('skills')}
                          placeholder="What expertise could you share with our builders?"
                        />
                        <FieldError error={fieldErrors['skills']} />
                      </div>
                    </div>

                    <div className="group/field relative">
                      <div className="absolute -inset-1 bg-gradient-to-r from-teal-400 to-teal-600 rounded-2xl blur-lg opacity-0 group-hover/field:opacity-20 transition-opacity duration-300" />
                      <div className="relative">
                        <Label htmlFor="availability" className="flex items-center gap-3 text-oven-black font-semibold mb-3 text-lg">
                          <motion.div 
                            className="p-2 bg-gradient-to-r from-teal-400 to-teal-600 rounded-xl"
                            whileHover={{ scale: 1.1, rotate: 10 }}
                          >
                            <Calendar className="h-5 w-5 text-white" />
                          </motion.div>
                          Availability
                        </Label>
                        <Textarea
                          id="availability"
                          name="availability"
                          value={formData.availability}
                          onChange={handleInputChange}
                          onBlur={() => handleBlur('availability')}
                          autoResize={true}
                          className={getFieldStyling('availability')}
                          placeholder="How much time can you commit? Any specific preferences?"
                        />
                        <FieldError error={fieldErrors['availability']} />
                      </div>
                    </div>

                    <div className="group/field relative">
                      <div className="absolute -inset-1 bg-gradient-to-r from-rose-400 to-rose-600 rounded-2xl blur-lg opacity-0 group-hover/field:opacity-20 transition-opacity duration-300" />
                      <div className="relative">
                        <Label htmlFor="message" className="flex items-center gap-3 text-oven-black font-semibold mb-3 text-lg">
                          <motion.div 
                            className="p-2 bg-gradient-to-r from-rose-400 to-rose-600 rounded-xl"
                            whileHover={{ scale: 1.1, rotate: 10 }}
                          >
                            <MessageCircle className="h-5 w-5 text-white" />
                          </motion.div>
                          Message
                        </Label>
                        <Textarea
                          id="message"
                          name="message"
                          value={formData.message}
                          onChange={handleInputChange}
                          onBlur={() => handleBlur('message')}
                          autoResize={true}
                          className={getFieldStyling('message')}
                          placeholder="Tell us more about why you want to support Pie Fi and our builders"
                        />
                        <FieldError error={fieldErrors['message']} />
                      </div>
                    </div>

                    <div className="relative group/submit pt-4">
                      <div className="absolute inset-0 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-2xl blur-xl opacity-70 group-hover/submit:opacity-100 transition-opacity pointer-events-none z-0" />
                      <Button 
                        type="submit" 
                        size="lg" 
                        disabled={isSubmitting}
                        className="relative w-full bg-gradient-to-r from-sauce-red to-cheese-gold hover:from-sauce-red/90 hover:to-cheese-gold/90 text-oven-black font-bold text-xl py-8 rounded-2xl shadow-2xl border-0 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed z-10"
                      >
                        {isSubmitting ? (
                          <>
                            <motion.div
                              className="w-6 h-6 border-2 border-oven-black/30 border-t-oven-black rounded-full mr-3"
                              animate={{ rotate: 360 }}
                              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                            />
                            Submitting...
                          </>
                        ) : (
                          <>
                            <Heart className="mr-3 h-6 w-6" />
                            Join Our Community
                          </>
                        )}
                      </Button>
                      
                      <p className="text-sm text-oven-black/60 mt-4 text-center">
                        By submitting, you agree to be contacted about Pie Fi updates and community opportunities.
                      </p>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default FamilyFriends;
