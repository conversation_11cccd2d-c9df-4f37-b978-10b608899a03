import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { 
  Key, 
  Plus, 
  Edit, 
  Trash2, 
  Users, 
  TrendingUp, 
  Activity,
  Co<PERSON>,
  <PERSON>,
  <PERSON>O<PERSON>,
  CheckCircle,
  XCircle,
  <PERSON><PERSON><PERSON><PERSON>gle,
  Refresh<PERSON>w
} from 'lucide-react';
import { supabaseAdmin } from '@/integrations/supabase/admin-client';

interface AccessCode {
  id: string;
  code: string;
  description: string;
  usage_limit: number | null;
  usage_count: number;
  is_active: boolean;
  created_at: string;
  expires_at: string | null;
}

interface AccessCodeUsage {
  code: string;
  applications: {
    id: string;
    full_name: string;
    email: string;
    created_at: string;
  }[];
}

const AdminAccessCodes: React.FC = () => {
  const [accessCodes, setAccessCodes] = useState<AccessCode[]>([]);
  const [codeUsage, setCodeUsage] = useState<AccessCodeUsage[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [editingCode, setEditingCode] = useState<AccessCode | null>(null);
  const [newCode, setNewCode] = useState({
    code: '',
    description: '',
    usage_limit: 50,
    is_active: true,
    expires_at: null as string | null
  });
  const { toast } = useToast();

  useEffect(() => {
    fetchAccessCodes();
    fetchUsageData();
  }, []);

  const fetchAccessCodes = async () => {
    try {
      const { data, error } = await supabaseAdmin
        .from('access_codes')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      setAccessCodes(data || []);
    } catch (error) {
      console.error('Error fetching access codes:', error);
      toast({
        title: "Error",
        description: "Failed to load access codes",
        variant: "destructive",
      });
    }
  };

  const fetchUsageData = async () => {
    try {
      const { data: applications, error } = await supabaseAdmin
        .from('applications')
        .select('id, full_name, email, created_at, access_code_used')
        .not('access_code_used', 'is', null);

      if (error) throw error;

      // Group applications by access code
      const usageMap: { [key: string]: any[] } = {};
      applications?.forEach(app => {
        if (app.access_code_used) {
          if (!usageMap[app.access_code_used]) {
            usageMap[app.access_code_used] = [];
          }
          usageMap[app.access_code_used].push(app);
        }
      });

      const usageData = Object.entries(usageMap).map(([code, apps]) => ({
        code,
        applications: apps
      }));

      setCodeUsage(usageData);

      // Update usage counts in database and auto-disable codes that reach their limit
      for (const [code, apps] of Object.entries(usageMap)) {
        // Get the current access code data to check usage_limit
        const { data: currentCode } = await supabaseAdmin
          .from('access_codes')
          .select('usage_limit, is_active')
          .eq('code', code)
          .single();

        const updateData: any = { usage_count: apps.length };
        
        // If code has a usage limit and has reached or exceeded it, disable it
        if (currentCode?.usage_limit && apps.length >= currentCode.usage_limit && currentCode.is_active) {
          updateData.is_active = false;
          console.log(`Auto-disabling access code ${code} - reached usage limit (${apps.length}/${currentCode.usage_limit})`);
        }

        await supabaseAdmin
          .from('access_codes')
          .update(updateData)
          .eq('code', code);
      }

      // Refresh access codes to get updated counts
      await fetchAccessCodes();

    } catch (error) {
      console.error('Error fetching usage data:', error);
    } finally {
      setLoading(false);
    }
  };

  const createAccessCode = async () => {
    if (!newCode.code || !newCode.description) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsCreating(true);
      
      const { data, error } = await supabaseAdmin
        .from('access_codes')
        .insert([{
          code: newCode.code.toUpperCase(),
          description: newCode.description,
          usage_limit: newCode.usage_limit > 0 ? newCode.usage_limit : null,
          is_active: newCode.is_active,
          expires_at: newCode.expires_at
        }])
        .select()
        .single();

      if (error) {
        if (error.code === '23505') {
          toast({
            title: "Duplicate Code",
            description: "This access code already exists",
            variant: "destructive",
          });
        } else {
          throw error;
        }
        return;
      }

      await fetchAccessCodes();
      setNewCode({ code: '', description: '', usage_limit: 50, is_active: true, expires_at: null });
      
      toast({
        title: "Success",
        description: "Access code created successfully!",
      });
    } catch (error) {
      console.error('Error creating access code:', error);
      toast({
        title: "Error",
        description: "Failed to create access code",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const updateAccessCode = async (id: string, updates: Partial<AccessCode>) => {
    try {
      const { error } = await supabaseAdmin
        .from('access_codes')
        .update(updates)
        .eq('id', id);

      if (error) throw error;

      await fetchAccessCodes();
      setEditingCode(null);
      
      toast({
        title: "Success",
        description: "Access code updated successfully!",
      });
    } catch (error) {
      console.error('Error updating access code:', error);
      toast({
        title: "Error",
        description: "Failed to update access code",
        variant: "destructive",
      });
    }
  };

  const deleteAccessCode = async (id: string) => {
    try {
      const { error } = await supabaseAdmin
        .from('access_codes')
        .delete()
        .eq('id', id);

      if (error) throw error;

      await fetchAccessCodes();
      
      toast({
        title: "Success",
        description: "Access code deleted successfully!",
      });
    } catch (error) {
      console.error('Error deleting access code:', error);
      toast({
        title: "Error",
        description: "Failed to delete access code",
        variant: "destructive",
      });
    }
  };

  const copyToClipboard = (code: string) => {
    navigator.clipboard.writeText(code);
    toast({
      title: "Copied",
      description: "Access code copied to clipboard!",
    });
  };

  const refreshData = async () => {
    setLoading(true);
    await fetchAccessCodes();
    await fetchUsageData();
  };

  const enforceUsageLimits = async () => {
    try {
      setLoading(true);
      
      // Get all access codes that are active and have usage limits
      const { data: codes, error } = await supabaseAdmin
        .from('access_codes')
        .select('id, code, usage_count, usage_limit, is_active')
        .eq('is_active', true)
        .not('usage_limit', 'is', null);

      if (error) throw error;

      let disabledCount = 0;

      // Check each code and disable if over limit
      for (const code of codes || []) {
        if (code.usage_count >= code.usage_limit!) {
          await supabaseAdmin
            .from('access_codes')
            .update({ is_active: false })
            .eq('id', code.id);
          
          disabledCount++;
          console.log(`Auto-disabled access code ${code.code} - over limit (${code.usage_count}/${code.usage_limit})`);
        }
      }

      await fetchAccessCodes();
      
      toast({
        title: "Usage Limits Enforced",
        description: disabledCount > 0 
          ? `Disabled ${disabledCount} access code(s) that exceeded their limits`
          : "All codes are within their usage limits",
      });
      
    } catch (error) {
      console.error('Error enforcing usage limits:', error);
      toast({
        title: "Error",
        description: "Failed to enforce usage limits",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getUsagePercentage = (current: number, max: number | null) => {
    if (!max) return 0;
    return (current / max) * 100;
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-500';
    if (percentage >= 70) return 'text-yellow-500';
    return 'text-green-500';
  };

  const isCodeExpired = (expiresAt: string | null) => {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
  };

  const isCodeAtCapacity = (code: AccessCode) => {
    if (!code.usage_limit) return false;
    return code.usage_count >= code.usage_limit;
  };

  const getCodeStatus = (code: AccessCode) => {
    if (isCodeExpired(code.expires_at)) {
      return { status: 'expired', color: 'bg-gray-500/20 text-gray-400 border-gray-500/30', icon: XCircle };
    }
    if (isCodeAtCapacity(code)) {
      return { status: 'at_capacity', color: 'bg-orange-500/20 text-orange-400 border-orange-500/30', icon: AlertTriangle };
    }
    if (!code.is_active) {
      return { status: 'inactive', color: 'bg-red-500/20 text-red-400 border-red-500/30', icon: XCircle };
    }
    return { status: 'active', color: 'bg-green-500/20 text-green-400 border-green-500/30', icon: CheckCircle };
  };

  const totalCodes = accessCodes.length;
  const activeCodes = accessCodes.filter(code => 
    code.is_active && 
    !isCodeExpired(code.expires_at) && 
    !isCodeAtCapacity(code)
  ).length;
  const codesAtCapacity = accessCodes.filter(code => isCodeAtCapacity(code)).length;
  const totalUsage = accessCodes.reduce((sum, code) => sum + code.usage_count, 0);
  const totalCapacity = accessCodes.reduce((sum, code) => sum + (code.usage_limit || 0), 0);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
        <span className="ml-2 text-lg">Loading access codes...</span>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="space-y-8 text-accent-white"
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-accent-white flex items-center gap-3">
            <Key className="h-8 w-8 text-sauce-red" />
            Access Codes
          </h2>
          <p className="text-crust-beige/80 mt-2">Manage application access codes</p>
        </div>
        
        <div className="flex gap-2">
          <Button 
            onClick={refreshData}
            disabled={loading}
            className="bg-gradient-to-r from-sauce-red to-cheese-gold hover:from-sauce-red/90 hover:to-cheese-gold/90 text-oven-black font-bold"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button 
            onClick={enforceUsageLimits}
            disabled={loading}
            className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-bold"
          >
            <AlertTriangle className="h-4 w-4 mr-2" />
            Enforce Limits
          </Button>
          
          <Dialog>
            <DialogTrigger asChild>
              <Button className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white">
                <Plus className="w-4 h-4 mr-2" />
                New Code
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Access Code</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="code">Access Code *</Label>
                  <Input
                    id="code"
                    value={newCode.code}
                    onChange={(e) => setNewCode({ ...newCode, code: e.target.value.toUpperCase() })}
                    placeholder="e.g., SPECIAL2025"
                    className="uppercase"
                  />
                </div>
                
                <div>
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={newCode.description}
                    onChange={(e) => setNewCode({ ...newCode, description: e.target.value })}
                    placeholder="Description of this access code..."
                    rows={3}
                  />
                </div>
                
                <div>
                  <Label htmlFor="usage_limit">Usage Limit</Label>
                  <Input
                    id="usage_limit"
                    type="number"
                    value={newCode.usage_limit}
                    onChange={(e) => setNewCode({ ...newCode, usage_limit: parseInt(e.target.value) || 0 })}
                    placeholder="0 for unlimited"
                    min="0"
                  />
                  <p className="text-sm text-gray-500 mt-1">Set to 0 for unlimited usage</p>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_active"
                    checked={newCode.is_active}
                    onCheckedChange={(checked) => setNewCode({ ...newCode, is_active: checked })}
                  />
                  <Label htmlFor="is_active">Active</Label>
                </div>
                
                <div className="flex gap-2 pt-4">
                  <Button onClick={createAccessCode} disabled={isCreating} className="flex-1">
                    {isCreating ? 'Creating...' : 'Create Access Code'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {[
          { label: 'Total Codes', value: totalCodes, icon: Key, color: 'from-blue-400 to-blue-600' },
          { label: 'Active Codes', value: activeCodes, icon: CheckCircle, color: 'from-green-400 to-green-600' },
          { label: 'At Capacity', value: codesAtCapacity, icon: AlertTriangle, color: 'from-orange-400 to-orange-600' },
          { label: 'Total Usage', value: totalUsage, icon: Users, color: 'from-purple-400 to-purple-600' },
          { label: 'Capacity', value: totalCapacity || '∞', icon: TrendingUp, color: 'from-yellow-400 to-yellow-600' }
        ].map((stat, index) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="relative overflow-hidden border border-accent-white/20 bg-gradient-to-br from-accent-white/10 to-accent-white/5 backdrop-blur-md">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-crust-beige/80">{stat.label}</p>
                    <p className="text-3xl font-bold text-accent-white">{stat.value}</p>
                  </div>
                  <div className={`p-3 rounded-2xl bg-gradient-to-r ${stat.color}`}>
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Access Codes Table */}
      <Card className="bg-accent-white/10 backdrop-blur-md border border-accent-white/20 shadow-xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-accent-white">
            <Key className="h-5 w-5" />
            Access Codes ({accessCodes.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-sauce-red mx-auto"></div>
              <p className="mt-2 text-crust-beige/80">Loading access codes...</p>
            </div>
          ) : accessCodes.length === 0 ? (
            <div className="text-center py-8">
              <Key className="h-12 w-12 text-crust-beige/60 mx-auto mb-4" />
              <p className="text-crust-beige/80">No access codes found</p>
            </div>
          ) : (
            <div className="overflow-x-auto rounded-xl border border-accent-white/20">
              <Table>
                <TableHeader className="bg-accent-white/5">
                  <TableRow>
                    <TableHead className="text-accent-white font-semibold">Code</TableHead>
                    <TableHead className="text-accent-white font-semibold">Description</TableHead>
                    <TableHead className="text-accent-white font-semibold">Usage</TableHead>
                    <TableHead className="text-accent-white font-semibold">Status</TableHead>
                    <TableHead className="text-accent-white font-semibold">Created</TableHead>
                    <TableHead className="text-accent-white font-semibold">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {accessCodes.map((code, index) => (
                    <motion.tr
                      key={code.id}
                      className="hover:bg-accent-white/5 transition-colors border-accent-white/10"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                    >
                      <TableCell className="text-accent-white">
                        <div className="flex items-center gap-2">
                          <code className="bg-accent-white/20 px-2 py-1 rounded text-sm font-mono text-cheese-gold">
                            {code.code}
                          </code>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(code.code)}
                            className="h-6 w-6 p-0 text-crust-beige/60 hover:text-accent-white"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell className="text-crust-beige/80">{code.description}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-accent-white">
                            {code.usage_count} / {code.usage_limit || '∞'}
                          </span>
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full ${getUsageColor(getUsagePercentage(code.usage_count, code.usage_limit))}`}
                              style={{ width: `${Math.min(getUsagePercentage(code.usage_count, code.usage_limit), 100)}%` }}
                            />
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {(() => {
                          const status = getCodeStatus(code);
                          const StatusIcon = status.icon;
                          const statusText = {
                            active: 'Active',
                            inactive: 'Inactive',
                            at_capacity: 'At Capacity',
                            expired: 'Expired'
                          };
                          
                          return (
                            <Badge className={status.color}>
                              <StatusIcon className="w-3 h-3 mr-1" />
                              {statusText[status.status as keyof typeof statusText]}
                            </Badge>
                          );
                        })()}
                      </TableCell>
                      <TableCell className="text-crust-beige/80">{new Date(code.created_at).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingCode(code)}
                            className="h-8 w-8 p-0 text-blue-400 hover:text-blue-300 hover:bg-blue-500/20"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteAccessCode(code.id)}
                            className="h-8 w-8 p-0 text-red-400 hover:text-red-300 hover:bg-red-500/20"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </motion.tr>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Usage Details */}
      {codeUsage.length > 0 && (
        <Card className="bg-accent-white/10 backdrop-blur-md border border-accent-white/20 shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-accent-white">
              <Activity className="h-5 w-5" />
              Usage Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {codeUsage.map((usage) => (
                <div key={usage.code} className="space-y-2">
                  <h4 className="font-semibold text-accent-white">
                    Code: <code className="bg-accent-white/20 px-2 py-1 rounded text-sm">{usage.code}</code>
                  </h4>
                  <div className="bg-accent-white/5 rounded-lg p-4 border border-accent-white/10">
                    {usage.applications.length === 0 ? (
                      <p className="text-crust-beige/60 text-sm">No applications yet</p>
                    ) : (
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-crust-beige/80">
                          Applications ({usage.applications.length}):
                        </p>
                        {usage.applications.map((app) => (
                          <div key={app.id} className="flex justify-between items-center text-sm bg-accent-white/5 p-2 rounded border border-accent-white/10">
                            <span className="text-accent-white font-medium">{app.full_name}</span>
                            <span className="text-cheese-gold">{app.email}</span>
                            <span className="text-crust-beige/60">{new Date(app.created_at).toLocaleDateString()}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </motion.div>
  );
};

export default AdminAccessCodes; 