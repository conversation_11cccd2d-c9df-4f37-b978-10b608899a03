import * as React from "react"

const MOBILE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean>(false)

  React.useEffect(() => {
    // Set initial value immediately
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    mql.addEventListener("change", onChange)
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return isMobile
}

// Performance optimization hook for mobile animations
export function useMobileOptimizedAnimation() {
  const isMobile = useIsMobile()
  const prefersReducedMotion = React.useMemo(() => {
    if (typeof window === 'undefined') return false
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches
  }, [])

  const shouldReduceAnimations = isMobile || prefersReducedMotion

  // Return optimized animation props
  const getOptimizedAnimationProps = React.useCallback((desktopProps: any, mobileProps: any = {}) => {
    if (shouldReduceAnimations) {
      return {
        ...mobileProps,
        transition: { duration: 0.2, ease: "easeOut" },
        whileHover: undefined,
        whileTap: undefined
      }
    }
    return desktopProps
  }, [shouldReduceAnimations])

  return {
    isMobile,
    shouldReduceAnimations,
    getOptimizedAnimationProps
  }
}
