import React from 'react';

interface MarkdownTextProps {
  children?: string | undefined | null;
  content?: string | undefined | null;
  className?: string;
  inline?: boolean;
}

const MarkdownText: React.FC<MarkdownTextProps> = ({
  children,
  content,
  className = '',
  inline = false
}) => {
  const text = content || children;

  const formatText = (text: string | undefined | null) => {
    // Handle null, undefined, or non-string values
    if (!text || typeof text !== 'string') {
      return '';
    }

    // Handle bold text (**text**)
    let formatted = text.replace(/\*\*(.*?)\*\*/g, '<strong class="font-bold text-cheese-gold">$1</strong>');

    // Handle italic text (*text*) - improved regex to avoid conflicts with bold
    formatted = formatted.replace(/(?<!\*)\*([^*\n]+)\*(?!\*)/g, '<em class="italic text-accent-white/90">$1</em>');

    // Handle line breaks (only for block elements)
    if (!inline) {
      formatted = formatted.replace(/\n/g, '<br />');
    }

    return formatted;
  };

  const Element = inline ? 'span' : 'div';

  return (
    <Element
      className={className}
      dangerouslySetInnerHTML={{ __html: formatText(text) }}
    />
  );
};

export default MarkdownText; 