import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield<PERSON>heck, CloudLightning, Award, DollarSign, Home, Users as UsersIcon } from 'lucide-react';

const powerUpItems = [
  { lucideIcon: ShieldCheck, title: 'Legal Armor', description: 'Get legit, fast. Incorporation covered by pros.' },
  { lucideIcon: CloudLightning, title: 'Cloud Credits', description: 'AWS, OpenAI & more. Fuel your fire.' },
  { lucideIcon: Award, title: 'VIP Mentors', description: 'Guidance from SV & Bay Area legends. No gatekeeping.' },
  { lucideIcon: DollarSign, title: 'Real Cash Drops', description: 'Seed fuel for the most explosive projects. Case-by-case.' },
  { lucideIcon: Home, title: 'IRL Builder Space', description: 'Your Santa Cruz command center. Connect & conquer.' },
  { lucideIcon: UsersIcon, title: 'Public Build Hype', description: 'Leaderboards, progress walls. Get noticed.' },
  { lucideIcon: UsersIcon, title: 'Demo Day Stage', description: 'Pitch to investors, media, the world.' },
];

const PowerUps = () => {
  return (
    <section id="power-ups" className="animated-bg-light section-padding particles">
      <div className="max-w-5xl mx-auto">
        <h2 className="section-title text-oven-black">
          Your <span className="text-sauce-red">Arsenal</span>
        </h2>
        <p className="section-subtitle text-oven-black/80">
          Everything you need to go from zero to hero. No fluff, just firepower.
        </p>
        <div className="mt-12 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {powerUpItems.map((item) => (
            <Card key={item.title} className="card-consistent h-full flex flex-col justify-between hover:border-sauce-red/30 transition-all duration-300 transform hover:scale-105">
              <CardHeader className="flex-shrink-0">
                <div className="flex items-center space-x-3">
                  <div className="p-3 rounded-md bg-cheese-gold/10"> 
                    <item.lucideIcon className="h-8 w-8 text-cheese-gold" />
                  </div>
                  <CardTitle className="text-lg font-semibold text-oven-black">{item.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <p className="text-oven-black/70">{item.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PowerUps;
