// University normalization utility to group similar university names together

export interface UniversityMapping {
  canonical: string;
  variations: string[];
  shortName: string;
}

// Common university mappings - can be extended as more variations are encountered
export const UNIVERSITY_MAPPINGS: UniversityMapping[] = [
  {
    canonical: "UC Santa Cruz",
    shortName: "UCSC",
    variations: [
      "UC Santa Cruz",
      "University of California, Santa Cruz",
      "UCSC",
      "University of California Santa Cruz",
      "UC, Santa Cruz",
      "University of California - Santa Cruz",
      "Santa Cruz",
      "ucsc"
    ]
  },
  {
    canonical: "Stanford University",
    shortName: "Stanford",
    variations: [
      "Stanford",
      "Stanford University",
      "stanford",
      "stanford university"
    ]
  },
  {
    canonical: "UC Berkeley",
    shortName: "UCB",
    variations: [
      "UC Berkeley",
      "University of California, Berkeley",
      "UCB",
      "Berkeley",
      "University of California Berkeley",
      "UC, Berkeley",
      "Cal",
      "california berkeley"
    ]
  },
  {
    canonical: "San Jose State University",
    shortName: "SJSU",
    variations: [
      "San Jose State",
      "San Jose State University",
      "SJSU",
      "sjsu"
    ]
  },
  {
    canonical: "UC Davis",
    shortName: "UCD",
    variations: [
      "UC Davis",
      "University of California, Davis",
      "UCD",
      "Davis",
      "University of California Davis"
    ]
  },
  {
    canonical: "UC Los Angeles",
    shortName: "UCLA",
    variations: [
      "UCLA",
      "UC Los Angeles",
      "University of California, Los Angeles",
      "University of California Los Angeles",
      "UC, Los Angeles"
    ]
  },
  {
    canonical: "UC San Diego",
    shortName: "UCSD",
    variations: [
      "UCSD",
      "UC San Diego",
      "University of California, San Diego",
      "University of California San Diego",
      "UC, San Diego"
    ]
  },
  {
    canonical: "Santa Clara University",
    shortName: "SCU",
    variations: [
      "Santa Clara University",
      "SCU",
      "Santa Clara",
      "scu"
    ]
  },
  {
    canonical: "Cal Poly San Luis Obispo",
    shortName: "Cal Poly SLO",
    variations: [
      "Cal Poly",
      "Cal Poly San Luis Obispo",
      "Cal Poly SLO",
      "California Polytechnic State University",
      "California Polytechnic"
    ]
  }
];

/**
 * Normalizes a university name to its canonical form
 * @param universityName - The raw university name from user input
 * @returns The canonical university name
 */
export function normalizeUniversityName(universityName: string): string {
  if (!universityName) return universityName;

  const trimmed = universityName.trim();
  
  // Find matching mapping
  const mapping = UNIVERSITY_MAPPINGS.find(mapping => 
    mapping.variations.some(variation => 
      variation.toLowerCase() === trimmed.toLowerCase()
    )
  );

  return mapping ? mapping.canonical : trimmed;
}

/**
 * Gets the short name for a university
 * @param universityName - The university name (canonical or variation)
 * @returns The short name if available, otherwise the canonical name
 */
export function getUniversityShortName(universityName: string): string {
  const canonical = normalizeUniversityName(universityName);
  const mapping = UNIVERSITY_MAPPINGS.find(m => m.canonical === canonical);
  return mapping ? mapping.shortName : canonical;
}

/**
 * Groups applications by normalized university names
 * @param applications - Array of applications with university field
 * @returns Object with normalized university names as keys and application arrays as values
 */
export function groupApplicationsByUniversity<T extends { university: string }>(applications: T[]): Record<string, T[]> {
  return applications.reduce((acc, app) => {
    const normalizedName = normalizeUniversityName(app.university);
    if (!acc[normalizedName]) {
      acc[normalizedName] = [];
    }
    acc[normalizedName].push(app);
    return acc;
  }, {} as Record<string, T[]>);
}

/**
 * Gets unique normalized university names from applications
 * @param applications - Array of applications with university field
 * @returns Sorted array of unique normalized university names
 */
export function getUniqueNormalizedUniversities<T extends { university: string }>(applications: T[]): string[] {
  const normalizedNames = applications.map(app => normalizeUniversityName(app.university));
  return [...new Set(normalizedNames)].sort();
}

/**
 * Checks if a university name matches the filter (handles normalization)
 * @param universityName - The university name to check
 * @param filter - The filter value
 * @returns Whether the university matches the filter
 */
export function universityMatchesFilter(universityName: string, filter: string): boolean {
  if (filter === 'all') return true;
  return normalizeUniversityName(universityName) === filter;
} 