import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { 
  CheckCircle, 
  Circle, 
  ChevronDown, 
  ChevronUp,
  Target,
  Brain,
  Zap,
  Award,
  Clock,
  Send,
  Sparkles
} from 'lucide-react';
import { DevJourneyStage } from '@/services/devJourneyService';
import { StageAnalysis } from '@/services/adaptiveJourneyService';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import MarkdownText from './MarkdownText';
import milestoneSubmissionService from '@/services/milestoneSubmissionService';
import MilestoneCoachingModal from './MilestoneCoachingModal';

interface MilestoneSubmission {
  id: string;
  milestone_id: string;
  submission_content: string;
  submission_date: string;
  is_approved: boolean;
  ai_feedback?: string;
}

interface AdaptiveDevJourneyStageTabProps {
  stage: DevJourneyStage;
  stageAnalysis: StageAnalysis;
  isCurrentStage: boolean;
  isCompleted: boolean;
  userId: string;
  onStageUpdate?: () => void;
}

const AdaptiveDevJourneyStageTab: React.FC<AdaptiveDevJourneyStageTabProps> = ({
  stage,
  stageAnalysis,
  isCurrentStage,
  isCompleted,
  userId,
  onStageUpdate
}) => {
  const [isExpanded, setIsExpanded] = useState(isCurrentStage);
  const [milestones, setMilestones] = useState<MilestoneSubmission[]>([]);
  const [loading, setLoading] = useState(false);
  const [newMilestoneContent, setNewMilestoneContent] = useState<Record<string, string>>({});
  const [submittingMilestone, setSubmittingMilestone] = useState<string | null>(null);
  const [expandedMilestones, setExpandedMilestones] = useState<Set<string>>(new Set());
  const [coachingModal, setCoachingModal] = useState<{
    isOpen: boolean;
    milestone: any;
    followUpQuestions: string[];
    sessionId: string;
  }>({
    isOpen: false,
    milestone: null,
    followUpQuestions: [],
    sessionId: ''
  });

  // Fetch milestone data when expanded
  useEffect(() => {
    if (isExpanded) {
      fetchMilestones();
    }
  }, [isExpanded, userId, stage.id]);

  const fetchMilestones = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('milestone_submissions')
        .select('*')
        .eq('user_id', userId)
        .eq('stage_id', stage.id)
        .order('submission_date', { ascending: false });

      if (error) throw error;
      setMilestones(data || []);
    } catch (error) {
      console.error('Error fetching milestones:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMilestoneSubmission = async (milestoneId: string) => {
    const content = newMilestoneContent[milestoneId];

    setSubmittingMilestone(milestoneId);

    try {
      const result = await milestoneSubmissionService.submitMilestone({
        userId,
        stageId: stage.id,
        milestoneId,
        content,
        stageTitle: stage.title,
        milestoneTitle: milestoneId,
        milestoneDescription: `Stage ${stage.id} milestone: ${milestoneId}`,
        projectContext: ''
      });

      if (result.success) {
        // Check if AI analysis generated follow-up questions
        const hasFollowUpQuestions = result.aiAnalysis?.followUpQuestions?.length > 0;

        if (hasFollowUpQuestions) {
          // Close any existing coaching session before starting new one
          if (coachingModal.isOpen) {
            console.log('🔄 Closing previous coaching session to start new one');
            setCoachingModal(prev => ({ ...prev, isOpen: false }));
            // Small delay to ensure clean state transition
            setTimeout(() => {
              openNewCoachingSession();
            }, 100);
          } else {
            openNewCoachingSession();
          }

          function openNewCoachingSession() {
            const sessionId = `coaching_${milestoneId}_${Date.now()}`;
            setCoachingModal({
              isOpen: true,
              milestone: {
                id: result.submissionId,
                milestone_id: milestoneId,
                stage_id: stage.id,
                submission_content: content,
                ai_feedback: result.aiAnalysis.consolidatedFeedback || result.aiAnalysis.insights || result.aiAnalysis.coachingInsight
              },
              followUpQuestions: result.aiAnalysis.followUpQuestions,
              sessionId
            });
          }

          toast.success('Milestone submitted! Let\'s dive deeper with some follow-up questions.');
        } else {
          // Show detailed success message with AI analysis info
          if (result.aiAnalysis) {
            toast.success('Milestone submitted and analyzed successfully! Check your AI feedback below.');
          } else {
            toast.success('Milestone submitted successfully! AI analysis in progress...');
          }
        }

        setNewMilestoneContent(prev => ({ ...prev, [milestoneId]: '' }));
        await fetchMilestones();
        onStageUpdate?.();
      } else {
        toast.error(result.error || 'Failed to submit milestone');
      }
    } catch (error) {
      console.error('Unexpected error submitting milestone:', error);
      toast.error('An unexpected error occurred. Please try again.');
    } finally {
      setSubmittingMilestone(null);
    }
  };

  const getStageStatus = () => {
    if (isCompleted) return 'complete';
    if (isCurrentStage) return 'current';
    if (stageAnalysis.completion_percentage > 0) return 'in-progress';
    return 'not-started';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'complete': return 'border-green-400/30 bg-green-500/10';
      case 'current': return 'border-blue-400/30 bg-blue-500/10';
      case 'in-progress': return 'border-yellow-400/30 bg-yellow-500/10';
      default: return 'border-gray-400/30 bg-gray-500/10';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'complete': return <CheckCircle className="h-6 w-6 text-green-400" />;
      case 'current': return <Target className="h-6 w-6 text-blue-400" />;
      case 'in-progress': return <Circle className="h-6 w-6 text-yellow-400" />;
      default: return <Circle className="h-6 w-6 text-gray-400" />;
    }
  };

  const status = getStageStatus();

  return (
    <Card className={`${getStatusColor(status)} transition-all duration-300`}>
      <CardHeader 
        className="cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {getStatusIcon(status)}
            <div>
              <CardTitle className="text-accent-white">
                Stage {stage.id}: {stage.title}
              </CardTitle>
              <div className="flex items-center gap-4 mt-1">
                <span className="text-sm text-accent-white/70">
                  {stageAnalysis.completion_percentage}% Complete
                </span>
                <span className="text-sm text-accent-white/70">
                  Readiness: {stageAnalysis.readiness_score}%
                </span>
                {stageAnalysis.completed_milestones.length > 0 && (
                  <Badge className="bg-green-500/20 text-green-400 text-xs">
                    {stageAnalysis.completed_milestones.length} milestones
                  </Badge>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-right">
              <Progress value={stageAnalysis.completion_percentage} className="w-20 h-2" />
              <p className="text-xs text-accent-white/60 mt-1">
                {stageAnalysis.readiness_score}% ready
              </p>
            </div>
            {isExpanded ? 
              <ChevronUp className="h-5 w-5 text-accent-white/60" /> :
              <ChevronDown className="h-5 w-5 text-accent-white/60" />
            }
          </div>
        </div>
      </CardHeader>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <CardContent className="pt-0">
              {loading ? (
                <div className="text-center py-4">
                  <Sparkles className="h-6 w-6 animate-spin text-accent-white/60 mx-auto mb-2" />
                  <p className="text-sm text-accent-white/60">Loading stage details...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Stage Description */}
                  <div className="bg-accent-white/5 rounded-lg p-3">
                    <p className="text-sm text-accent-white/80">{stage.description}</p>
                  </div>

                  {/* Adaptive Insights */}
                  {(isCurrentStage || stageAnalysis.completion_percentage > 0) && (
                    <div className="bg-purple-500/10 border border-purple-400/30 rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-2">
                        <Brain className="h-4 w-4 text-purple-400" />
                        <span className="text-sm font-medium text-purple-400">Adaptive Analysis</span>
                      </div>
                      <div className="text-xs text-accent-white/70 space-y-1">
                        <p>• {stageAnalysis.required_milestones_completed}/{stageAnalysis.total_required_milestones} required milestones completed</p>
                        <p>• Readiness score: {stageAnalysis.readiness_score}% (based on prerequisites and progress)</p>
                        {isCurrentStage && <p>• This is your current focus stage based on adaptive analysis</p>}
                      </div>
                    </div>
                  )}

                  {/* Completed Milestones */}
                  {milestones.filter(m => m.is_approved || m.submission_content).length > 0 && (
                    <div>
                      <h6 className="text-sm font-medium text-green-400 mb-2 flex items-center gap-2">
                        <Award className="h-4 w-4" />
                        Your Milestone Achievements ({milestones.filter(m => m.is_approved || m.submission_content).length})
                      </h6>
                      <div className="space-y-3">
                        {milestones.filter(m => m.is_approved || m.submission_content).map((milestone) => (
                          <div key={milestone.id} className={`border rounded-lg transition-all ${
                            milestone.is_approved
                              ? 'bg-green-500/10 border-green-400/30'
                              : 'bg-yellow-500/10 border-yellow-400/30'
                          }`}>
                            <div
                              className="p-3 cursor-pointer hover:bg-accent-white/5"
                              onClick={() => {
                                const newExpanded = new Set(expandedMilestones);
                                if (newExpanded.has(milestone.id)) {
                                  newExpanded.delete(milestone.id);
                                } else {
                                  newExpanded.add(milestone.id);
                                }
                                setExpandedMilestones(newExpanded);
                              }}
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  {milestone.is_approved ? (
                                    <CheckCircle className="h-4 w-4 text-green-400" />
                                  ) : (
                                    <Clock className="h-4 w-4 text-yellow-400" />
                                  )}
                                  <span className="text-sm font-medium text-accent-white">
                                    {milestone.milestone_id.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                  </span>
                                  <Badge className={`text-xs ${
                                    milestone.is_approved
                                      ? 'bg-green-500/20 text-green-400'
                                      : 'bg-yellow-500/20 text-yellow-400'
                                  }`}>
                                    {milestone.is_approved ? 'Approved' : 'Under Review'}
                                  </Badge>
                                </div>
                                <div className="flex items-center gap-2">
                                  <span className="text-xs text-accent-white/60">
                                    {formatDistanceToNow(new Date(milestone.submission_date), { addSuffix: true })}
                                  </span>
                                  {expandedMilestones.has(milestone.id) ? (
                                    <ChevronUp className="h-4 w-4 text-accent-white/60" />
                                  ) : (
                                    <ChevronDown className="h-4 w-4 text-accent-white/60" />
                                  )}
                                </div>
                              </div>
                            </div>

                            {expandedMilestones.has(milestone.id) && (
                              <div className="px-3 pb-3 space-y-3">
                                {/* Submission Content */}
                                <div className="bg-accent-white/5 rounded-lg p-3">
                                  <h7 className="text-xs font-medium text-accent-white/80 mb-2 block">Your Submission:</h7>
                                  <MarkdownText className="text-sm text-accent-white/90">
                                    {milestone.submission_content}
                                  </MarkdownText>
                                </div>

                                {/* AI Feedback */}
                                {milestone.ai_feedback && (
                                  <div className="bg-purple-500/10 border border-purple-400/30 rounded-lg p-3">
                                    <div className="flex items-center gap-2 mb-2">
                                      <Brain className="h-4 w-4 text-purple-400" />
                                      <span className="text-xs font-medium text-purple-400">AI Analysis</span>
                                    </div>
                                    <MarkdownText className="text-sm text-accent-white/90">
                                      {milestone.ai_feedback}
                                    </MarkdownText>
                                  </div>
                                )}

                                {/* Follow-up Questions - Now handled by coaching modal */}
                                {(() => {
                                  try {
                                    const insights = milestone.ai_insights ? JSON.parse(milestone.ai_insights) : null;
                                    const followUpQuestions = insights?.followUpQuestions || [];

                                    if (followUpQuestions.length > 0) {
                                      return (
                                        <div className="bg-yellow-500/10 border border-yellow-400/30 rounded-lg p-3">
                                          <div className="flex items-center gap-2 mb-3">
                                            <Sparkles className="h-4 w-4 text-yellow-400" />
                                            <span className="text-xs font-medium text-yellow-400">AI Coaching Available</span>
                                          </div>
                                          <p className="text-sm text-accent-white/80 mb-3">
                                            I have some follow-up questions to help deepen your thinking about this milestone.
                                          </p>
                                          <Button
                                            size="sm"
                                            onClick={() => {
                                              // Close any existing session before starting new one
                                              if (coachingModal.isOpen) {
                                                setCoachingModal(prev => ({ ...prev, isOpen: false }));
                                                setTimeout(() => {
                                                  openExistingCoachingSession();
                                                }, 100);
                                              } else {
                                                openExistingCoachingSession();
                                              }

                                              function openExistingCoachingSession() {
                                                const sessionId = `coaching_${milestone.milestone_id}_${Date.now()}`;
                                                setCoachingModal({
                                                  isOpen: true,
                                                  milestone: {
                                                    id: milestone.id,
                                                    milestone_id: milestone.milestone_id,
                                                    stage_id: milestone.stage_id,
                                                    submission_content: milestone.submission_content,
                                                    ai_feedback: milestone.ai_feedback
                                                  },
                                                  followUpQuestions,
                                                  sessionId
                                                });
                                              }
                                            }}
                                            className="bg-yellow-500/20 border border-yellow-400/30 text-yellow-400 hover:bg-yellow-500/30"
                                          >
                                            <Brain className="h-4 w-4 mr-2" />
                                            Start Coaching Session
                                          </Button>
                                        </div>
                                      );
                                    }
                                  } catch (e) {
                                    // If parsing fails, don't show anything
                                  }
                                  return null;
                                })()}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Available Milestones */}
                  {stageAnalysis.missing_milestones.length > 0 && (
                    <div>
                      <h6 className="text-sm font-medium text-blue-400 mb-2 flex items-center gap-2">
                        <Target className="h-4 w-4" />
                        Available Milestones
                      </h6>
                      <div className="space-y-3">
                        {stageAnalysis.missing_milestones.slice(0, 2).map(milestoneId => (
                          <div key={milestoneId} className="bg-blue-500/10 border border-blue-400/30 rounded p-3">
                            <h6 className="text-sm font-medium text-accent-white mb-2">
                              {milestoneId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </h6>
                            <Textarea
                              placeholder={`Describe your progress on ${milestoneId.replace(/_/g, ' ')}...`}
                              value={newMilestoneContent[milestoneId] || ''}
                              onChange={(e) => setNewMilestoneContent(prev => ({
                                ...prev,
                                [milestoneId]: e.target.value
                              }))}
                              className="min-h-[80px] bg-accent-white/5 border-accent-white/20 text-accent-white text-sm mb-2"
                            />
                            <Button
                              onClick={() => handleMilestoneSubmission(milestoneId)}
                              disabled={submittingMilestone === milestoneId || !newMilestoneContent[milestoneId]?.trim()}
                              size="sm"
                              className="w-full"
                            >
                              {submittingMilestone === milestoneId ? (
                                <>
                                  <Sparkles className="h-3 w-3 mr-2 animate-spin" />
                                  Submitting...
                                </>
                              ) : (
                                <>
                                  <Send className="h-3 w-3 mr-2" />
                                  Submit Milestone
                                </>
                              )}
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Quick Recommendations */}
                  {isCurrentStage && (
                    <div className="bg-yellow-500/10 border border-yellow-400/30 rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-2">
                        <Zap className="h-4 w-4 text-yellow-400" />
                        <span className="text-sm font-medium text-yellow-400">Next Steps</span>
                      </div>
                      <div className="text-xs text-accent-white/80">
                        {stageAnalysis.missing_milestones.length > 0 ? (
                          <p>Focus on completing {stageAnalysis.missing_milestones[0].replace(/_/g, ' ')} to advance in this stage.</p>
                        ) : (
                          <p>Great work! You've completed this stage. Ready to move forward.</p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Coaching Modal */}
      <MilestoneCoachingModal
        isOpen={coachingModal.isOpen}
        onClose={() => setCoachingModal(prev => ({ ...prev, isOpen: false }))}
        milestone={coachingModal.milestone}
        followUpQuestions={coachingModal.followUpQuestions}
        userId={userId}
        onConversationComplete={async () => {
          setCoachingModal(prev => ({ ...prev, isOpen: false }));
          await fetchMilestones();
          onStageUpdate?.();
        }}
      />
    </Card>
  );
};

export default AdaptiveDevJourneyStageTab;
