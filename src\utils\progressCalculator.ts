export interface DailyUpdate {
  id: string;
  content: string;
  created_at: string;
  ai_insights?: any;
  sentiment_score?: number;
}

export interface Goal {
  id: string;
  title: string;
  completed: boolean;
  priority: 'high' | 'medium' | 'low';
  created_at: string;
}

export interface ProjectProgress {
  total_updates: number;
  last_update: string;
  milestones_completed: number;
  current_stage: string;
}

export function calculateWeeklyProgress(updates: DailyUpdate[], goals: Goal[] = []) {
  const now = new Date();
  const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

  // Filter updates from the last week
  const weeklyUpdates = updates.filter(update => 
    new Date(update.created_at) >= weekAgo
  );

  // Calculate unique days with updates
  const daysWithUpdates = new Set(
    weeklyUpdates.map(u => new Date(u.created_at).toDateString())
  ).size;

  // Calculate goal completion rate
  const completedGoals = goals.filter(g => g.completed).length;
  const totalGoals = goals.length;
  const goalProgress = totalGoals > 0 ? (completedGoals / totalGoals) * 100 : 0;

  // Calculate consistency score (days with updates out of 7)
  const consistencyScore = (daysWithUpdates / 7) * 100;

  // Calculate overall progress (weighted average)
  const overallProgress = Math.round(
    (consistencyScore * 0.6) + (goalProgress * 0.4)
  );

  return {
    overall: Math.min(overallProgress, 100),
    consistency: Math.round(consistencyScore),
    goals: Math.round(goalProgress),
    daysActive: daysWithUpdates,
    totalUpdates: weeklyUpdates.length,
    averageUpdatesPerDay: weeklyUpdates.length / 7
  };
}

export function calculateStreak(updates: DailyUpdate[]) {
  if (updates.length === 0) return 0;

  const sortedUpdates = updates.sort((a, b) => 
    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  let streak = 0;
  let currentDate = new Date(today);

  for (const update of sortedUpdates) {
    const updateDate = new Date(update.created_at);
    updateDate.setHours(0, 0, 0, 0);

    if (updateDate.getTime() === currentDate.getTime()) {
      streak++;
      currentDate.setDate(currentDate.getDate() - 1);
    } else if (updateDate.getTime() < currentDate.getTime()) {
      // Gap in updates, streak broken
      break;
    }
  }

  return streak;
}

export function calculateSentimentTrend(updates: DailyUpdate[]) {
  const recentUpdates = updates
    .filter(u => u.sentiment_score !== null && u.sentiment_score !== undefined)
    .slice(0, 10)
    .reverse(); // Get chronological order

  if (recentUpdates.length < 2) {
    return { trend: 'neutral', change: 0, average: 0.5 };
  }

  const scores = recentUpdates.map(u => u.sentiment_score || 0.5);
  const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
  
  // Calculate trend over last 5 updates vs previous 5
  const recent = scores.slice(-5);
  const previous = scores.slice(-10, -5);
  
  const recentAvg = recent.reduce((sum, score) => sum + score, 0) / recent.length;
  const previousAvg = previous.length > 0 
    ? previous.reduce((sum, score) => sum + score, 0) / previous.length 
    : recentAvg;

  const change = recentAvg - previousAvg;
  
  let trend: 'positive' | 'negative' | 'neutral';
  if (change > 0.1) trend = 'positive';
  else if (change < -0.1) trend = 'negative';
  else trend = 'neutral';

  return {
    trend,
    change: Math.round(change * 100) / 100,
    average: Math.round(average * 100) / 100,
    recentAverage: Math.round(recentAvg * 100) / 100
  };
}

export function generateProgressInsights(
  updates: DailyUpdate[], 
  goals: Goal[] = [],
  projectProgress?: ProjectProgress
) {
  const weeklyProgress = calculateWeeklyProgress(updates, goals);
  const streak = calculateStreak(updates);
  const sentimentTrend = calculateSentimentTrend(updates);

  const insights = [];

  // Consistency insights
  if (weeklyProgress.consistency >= 80) {
    insights.push({
      type: 'success',
      title: 'Amazing Consistency! 🔥',
      message: `You've been active ${weeklyProgress.daysActive} out of 7 days this week. Keep it up!`
    });
  } else if (weeklyProgress.consistency >= 50) {
    insights.push({
      type: 'info',
      title: 'Good Progress 📈',
      message: `You're doing well with ${weeklyProgress.daysActive} active days. Try for daily updates!`
    });
  } else {
    insights.push({
      type: 'warning',
      title: 'Let\'s Build Momentum 💪',
      message: 'Consistent daily updates will accelerate your progress. Start with just 5 minutes a day!'
    });
  }

  // Streak insights
  if (streak >= 7) {
    insights.push({
      type: 'success',
      title: 'Incredible Streak! ⚡',
      message: `${streak} days in a row! You're building unstoppable momentum.`
    });
  } else if (streak >= 3) {
    insights.push({
      type: 'info',
      title: 'Building Momentum 🚀',
      message: `${streak} day streak! Keep going to hit your weekly goal.`
    });
  }

  // Sentiment insights
  if (sentimentTrend.trend === 'positive') {
    insights.push({
      type: 'success',
      title: 'Positive Vibes Rising! ✨',
      message: 'Your updates show increasing optimism and confidence. That\'s the builder spirit!'
    });
  } else if (sentimentTrend.trend === 'negative') {
    insights.push({
      type: 'warning',
      title: 'Facing Challenges? 🤝',
      message: 'Tough times are part of building. Consider reaching out to mentors for support.'
    });
  }

  // Goal insights
  if (goals.length > 0) {
    const completionRate = (goals.filter(g => g.completed).length / goals.length) * 100;
    if (completionRate >= 80) {
      insights.push({
        type: 'success',
        title: 'Goal Crusher! 🎯',
        message: `${Math.round(completionRate)}% of your goals completed. Time to set more ambitious ones!`
      });
    } else if (completionRate < 30) {
      insights.push({
        type: 'info',
        title: 'Focus Time 🎯',
        message: 'Break down your goals into smaller, achievable tasks for better progress.'
      });
    }
  }

  return insights;
}

export function getProgressLevel(overallProgress: number) {
  if (overallProgress >= 90) return { level: 'Elite', emoji: '🏆', color: 'text-yellow-400' };
  if (overallProgress >= 75) return { level: 'Advanced', emoji: '🚀', color: 'text-purple-400' };
  if (overallProgress >= 50) return { level: 'Building', emoji: '⚡', color: 'text-blue-400' };
  if (overallProgress >= 25) return { level: 'Starting', emoji: '🌱', color: 'text-green-400' };
  return { level: 'New', emoji: '🥚', color: 'text-gray-400' };
} 