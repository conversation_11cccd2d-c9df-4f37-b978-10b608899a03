import { supabase } from '@/integrations/supabase/client';

export interface CachedAnalysis {
  id: string;
  user_id: string;
  analysis_type: string;
  content_hash: string;
  analysis_result: any;
  created_at: string;
  expires_at: string;
  metadata: any;
}

class AIAnalysisCacheService {
  
  // Generate a consistent hash for content
  private generateContentHash(content: string, context: any = {}): string {
    const contentToHash = `${content}${JSON.stringify(context)}`;
    
    // Simple hash function for browser compatibility
    let hash = 0;
    for (let i = 0; i < contentToHash.length; i++) {
      const char = contentToHash.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }

  // Check if we have a valid cached analysis
  async getCachedAnalysis(
    userId: string, 
    analysisType: string, 
    content: string, 
    context: any = {}
  ): Promise<any | null> {
    try {
      const contentHash = this.generateContentHash(content, context);
      
      const { data, error } = await supabase
        .from('cached_ai_analyses')
        .select('*')
        .eq('user_id', userId)
        .eq('analysis_type', analysisType)
        .eq('content_hash', contentHash)
        .gt('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (error) {
        console.error('Error fetching cached analysis:', error);
        return null;
      }

      if (data) {
        console.log(`✅ Using cached ${analysisType} analysis (saved ${new Date(data.created_at).toLocaleTimeString()})`);
        return data.analysis_result;
      }

      return null;
    } catch (error) {
      console.error('Error in getCachedAnalysis:', error);
      return null;
    }
  }

  // Store analysis result in cache
  async cacheAnalysisResult(
    userId: string,
    analysisType: string,
    content: string,
    analysisResult: any,
    context: any = {},
    expirationHours: number = 24
  ): Promise<void> {
    try {
      const contentHash = this.generateContentHash(content, context);
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + expirationHours);

      const { error } = await supabase
        .from('cached_ai_analyses')
        .upsert({
          user_id: userId,
          analysis_type: analysisType,
          content_hash: contentHash,
          analysis_result: analysisResult,
          expires_at: expiresAt.toISOString(),
          metadata: {
            context_summary: Object.keys(context),
            content_length: content.length,
            cached_at: new Date().toISOString()
          }
        }, {
          onConflict: 'user_id,analysis_type,content_hash'
        });

      if (error) {
        console.error('Error caching analysis result:', error);
      } else {
        console.log(`💾 Cached ${analysisType} analysis for user ${userId}`);
      }
    } catch (error) {
      console.error('Error in cacheAnalysisResult:', error);
    }
  }

  // Get or create analysis with caching
  async getOrCreateAnalysis(
    userId: string,
    analysisType: string,
    content: string,
    context: any,
    analysisFunction: () => Promise<any>,
    expirationHours: number = 24
  ): Promise<{ result: any; wasCached: boolean }> {
    // First, check for cached result
    const cached = await this.getCachedAnalysis(userId, analysisType, content, context);
    if (cached) {
      return { result: cached, wasCached: true };
    }

    // If no cache, run the analysis
    console.log(`🔄 Running new ${analysisType} analysis for user ${userId}`);
    try {
      const result = await analysisFunction();
      
      // Cache the result
      await this.cacheAnalysisResult(
        userId, 
        analysisType, 
        content, 
        result, 
        context, 
        expirationHours
      );
      
      return { result, wasCached: false };
    } catch (error) {
      console.error(`Error in ${analysisType} analysis:`, error);
      throw error;
    }
  }

  // Clear expired cache entries
  async cleanupExpiredCache(): Promise<void> {
    try {
      const { error } = await supabase
        .from('cached_ai_analyses')
        .delete()
        .lt('expires_at', new Date().toISOString());

      if (error) {
        console.error('Error cleaning up expired cache:', error);
      } else {
        console.log('🧹 Cleaned up expired cache entries');
      }
    } catch (error) {
      console.error('Error in cleanupExpiredCache:', error);
    }
  }

  // Invalidate cache for specific user and analysis type
  async invalidateCache(userId: string, analysisType?: string): Promise<void> {
    try {
      let query = supabase
        .from('cached_ai_analyses')
        .delete()
        .eq('user_id', userId);

      if (analysisType) {
        query = query.eq('analysis_type', analysisType);
      }

      const { error } = await query;

      if (error) {
        console.error('Error invalidating cache:', error);
      } else {
        console.log(`🗑️ Invalidated cache for user ${userId}${analysisType ? ` (${analysisType})` : ''}`);
      }
    } catch (error) {
      console.error('Error in invalidateCache:', error);
    }
  }

  // Get cache statistics for debugging
  async getCacheStats(userId: string): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('cached_ai_analyses')
        .select('analysis_type, created_at, expires_at')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error getting cache stats:', error);
        return null;
      }

      const stats = {
        total_entries: data.length,
        by_type: data.reduce((acc: any, entry: any) => {
          acc[entry.analysis_type] = (acc[entry.analysis_type] || 0) + 1;
          return acc;
        }, {}),
        expired_entries: data.filter(entry => new Date(entry.expires_at) < new Date()).length
      };

      return stats;
    } catch (error) {
      console.error('Error in getCacheStats:', error);
      return null;
    }
  }
}

const aiAnalysisCacheService = new AIAnalysisCacheService();
export default aiAnalysisCacheService; 