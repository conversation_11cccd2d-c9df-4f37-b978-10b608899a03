import { supabase } from '@/integrations/supabase/client';
import stageProgressService from './stageProgressService';
import userMemoryService from './userMemoryService';
import devJourneyService from './devJourneyService';
import geminiService from './geminiService';

interface OnboardingData {
  track: string;
  problem_description: string;
  solution_approach: string;
  target_audience: string;
  unique_value_proposition: string;
  technical_background: string;
  previous_experience: string;
  technical_skills: string;
  primary_goal: string;
  success_metrics: string;
}

interface ComprehensiveProfile {
  profile_summary: string;
  strengths: string[];
  knowledge_gaps: string[];
  recommended_next_steps: string[];
  project_viability_score: number;
}

interface AIConversation {
  conversation_data: any[];
  key_insights_extracted: any;
}

class OnboardingToJourneyService {
  
  /**
   * Populate dev journey milestones based on onboarding data
   */
  async populateJourneyFromOnboarding(userId: string): Promise<{
    success: boolean;
    milestonesPopulated: number;
    stageAssigned: number;
    message: string;
  }> {
    try {
      console.log('🚀 Populating dev journey from onboarding data...');

      // Get onboarding data
      const onboardingData = await this.getOnboardingData(userId);
      if (!onboardingData) {
        return {
          success: false,
          milestonesPopulated: 0,
          stageAssigned: 0,
          message: 'No onboarding data found'
        };
      }

      // Get comprehensive profile
      const profile = await this.getComprehensiveProfile(userId);
      
      // Get AI conversation insights
      const aiConversation = await this.getAIConversationData(userId);

      // Get user's current stage from database instead of recalculating
      const currentStage = await this.getUserCurrentStage(userId);
      const assignedStage = currentStage || this.determineInitialStage(onboardingData, profile);

      console.log(`🎯 Using stage ${assignedStage} for user ${userId}`);

      // Map onboarding responses to milestone completions across relevant stages
      const milestoneCompletions = await this.mapOnboardingToMilestones(
        onboardingData,
        profile,
        aiConversation,
        assignedStage
      );

      // Store milestone completions in the journey
      let milestonesPopulated = 0;
      const stageCompletions: { [stageId: number]: any[] } = {};

      for (const completion of milestoneCompletions) {
        // Determine which stage this milestone belongs to
        const milestoneStage = this.getMilestoneStage(completion.milestone_id);
        await this.storeMilestoneCompletion(userId, milestoneStage, completion, onboardingData);

        // Group completions by stage for progress updates
        if (!stageCompletions[milestoneStage]) {
          stageCompletions[milestoneStage] = [];
        }
        stageCompletions[milestoneStage].push(completion);
        milestonesPopulated++;
      }

      // Update stage progress for each stage that has completions
      for (const [stageId, completions] of Object.entries(stageCompletions)) {
        await this.updateStageProgress(userId, parseInt(stageId), completions);
      }

      // Store journey population event
      await userMemoryService.storeMemory({
        user_id: userId,
        memory_type: 'progress',
        content: {
          journey_populated_from_onboarding: true,
          milestones_populated: milestonesPopulated,
          stage_assigned: assignedStage,
          population_date: new Date().toISOString()
        },
        metadata: {
          stage_id: assignedStage,
          source: 'onboarding_population',
          milestones_count: milestonesPopulated
        }
      });

      return {
        success: true,
        milestonesPopulated,
        stageAssigned: assignedStage,
        message: `Successfully populated ${milestonesPopulated} milestones for stage ${assignedStage}`
      };

    } catch (error) {
      console.error('Error populating journey from onboarding:', error);
      return {
        success: false,
        milestonesPopulated: 0,
        stageAssigned: 0,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Get onboarding data for user
   */
  private async getOnboardingData(userId: string): Promise<OnboardingData | null> {
    const { data, error } = await supabase
      .from('onboarding_responses')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) {
      console.error('Error fetching onboarding data:', error);
      return null;
    }

    return data;
  }

  /**
   * Get comprehensive profile for user
   */
  private async getComprehensiveProfile(userId: string): Promise<ComprehensiveProfile | null> {
    const { data, error } = await supabase
      .from('comprehensive_user_profiles')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) {
      console.error('Error fetching comprehensive profile:', error);
      return null;
    }

    return data;
  }

  /**
   * Get AI conversation data
   */
  private async getAIConversationData(userId: string): Promise<AIConversation | null> {
    const { data, error } = await supabase
      .from('ai_followup_conversations')
      .select('conversation_data, key_insights_extracted')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) {
      console.error('Error fetching AI conversation:', error);
      return null;
    }

    return data;
  }

  /**
   * Get user's current stage from database
   */
  private async getUserCurrentStage(userId: string): Promise<number | null> {
    const { data, error } = await supabase
      .from('user_dev_journey_stages')
      .select('stage_id')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) {
      console.error('Error fetching user stage:', error);
      return null;
    }

    return data?.stage_id || null;
  }

  /**
   * Determine initial stage based on onboarding data
   */
  private determineInitialStage(onboarding: OnboardingData, profile: ComprehensiveProfile | null): number {
    // Use track-based mapping as primary indicator
    const trackToStage = {
      'newbie': 0,    // Spark
      'builder': 2,   // First Build  
      'scaler': 4     // Traction
    };

    let baseStage = trackToStage[onboarding.track as keyof typeof trackToStage] || 0;

    // Adjust based on responses and profile
    if (profile) {
      // If they have a high viability score and clear solution, bump up
      if (profile.project_viability_score > 70 && onboarding.solution_approach?.length > 100) {
        baseStage = Math.min(baseStage + 1, 5);
      }

      // If they have previous experience and technical skills, consider higher stage
      if (onboarding.previous_experience?.length > 50 && onboarding.technical_skills?.length > 30) {
        baseStage = Math.min(baseStage + 1, 5);
      }
    }

    return baseStage;
  }

  /**
   * Map onboarding responses to milestone completions
   */
  private async mapOnboardingToMilestones(
    onboarding: OnboardingData,
    profile: ComprehensiveProfile | null,
    aiConversation: AIConversation | null,
    stageId: number
  ): Promise<Array<{
    milestone_id: string;
    submission_content: string;
    ai_insight: string;
    completed_date: string;
  }>> {
    const completions: Array<{
      milestone_id: string;
      submission_content: string;
      ai_insight: string;
      completed_date: string;
    }> = [];

    const completedDate = new Date().toISOString();

    // Get milestones from stages 0 through current stage to populate foundational milestones
    const relevantStages = [];
    for (let i = 0; i <= stageId; i++) {
      relevantStages.push(i);
    }

    console.log(`🎯 Mapping onboarding to milestones for stages: ${relevantStages.join(', ')}`);

    // Generate fully personalized milestones based on project context
    const milestoneMapping = await this.generatePersonalizedMilestones(
      onboarding,
      profile,
      aiConversation,
      relevantStages
    );

    // Process each mapping
    for (const mapping of milestoneMapping) {
      // Only include milestones from relevant stages
      if (!relevantStages.includes(mapping.stage)) continue;

      // Check if condition is met
      if (!mapping.condition()) continue;

      const submission = mapping.getSubmission();
      if (!submission) continue;

      console.log(`✅ Mapping milestone: ${mapping.milestone_id} (stage ${mapping.stage})`);

      // Generate AI summary for display
      const summarizedSubmission = await this.generateAISummary(submission, mapping.milestone_id);

      // Generate personalized milestone content
      let personalizedTitle = '';
      let personalizedDescription = '';
      let personalizedDeliverables: string[] = [];

      try {
        if (mapping.getPersonalizedTitle) {
          personalizedTitle = await mapping.getPersonalizedTitle();
        }
        if (mapping.getPersonalizedDescription) {
          personalizedDescription = await mapping.getPersonalizedDescription();
        }
        if (mapping.getPersonalizedDeliverables) {
          personalizedDeliverables = await mapping.getPersonalizedDeliverables();
        }
      } catch (error) {
        console.error('Error generating personalized content:', error);
      }

      // Generate AI insight for this milestone
      let aiInsight = '';
      try {
        aiInsight = await this.generateMilestoneInsight(
          {
            id: mapping.milestone_id,
            title: mapping.milestone_id.replace('_', ' '),
            description: personalizedDescription || mapping.getContext()
          },
          submission,
          onboarding,
          profile,
          aiConversation
        );
      } catch (error) {
        console.error('Error generating AI insight:', error);
        aiInsight = `Milestone completed during onboarding. Great progress on ${mapping.milestone_id.replace('_', ' ')}.`;
      }

      completions.push({
        milestone_id: mapping.milestone_id,
        submission_content: summarizedSubmission, // Use AI-generated summary
        ai_insight: aiInsight,
        completed_date: completedDate,
        auto_populated: true, // Mark as auto-populated to prevent unchecking
        personalized_title: personalizedTitle,
        personalized_description: personalizedDescription,
        personalized_deliverables: personalizedDeliverables
      });
    }

    return completions;
  }

  /**
   * Store milestone completion
   */
  private async storeMilestoneCompletion(
    userId: string,
    stageId: number,
    completion: {
      milestone_id: string;
      submission_content: string;
      ai_insight: string;
      completed_date: string;
      personalized_description?: string;
      personalized_deliverables?: string[];
      personalized_title?: string;
    },
    onboardingData: any
  ): Promise<void> {
    // Store in milestone_submissions table
    await supabase
      .from('milestone_submissions')
      .upsert({
        user_id: userId,
        stage_id: stageId,
        milestone_id: completion.milestone_id,
        submission_content: completion.submission_content,
        ai_feedback: completion.ai_insight,
        submission_date: completion.completed_date,
        is_approved: true,
        approval_date: completion.completed_date,
        metadata: {
          source: 'onboarding_population',
          auto_populated: true,
          prevent_unchecking: true,
          personalized_title: completion.personalized_title || '',
          personalized_description: completion.personalized_description || '',
          personalized_deliverables: completion.personalized_deliverables || [],
          project_context: {
            problem_domain: onboardingData.problem_description,
            solution_type: onboardingData.solution_approach,
            target_users: onboardingData.target_audience,
            technical_background: onboardingData.technical_background,
            primary_goal: onboardingData.primary_goal
          }
        }
      }, {
        onConflict: 'user_id,stage_id,milestone_id'
      });

    // Store in user memory
    await userMemoryService.storeMemory({
      user_id: userId,
      memory_type: 'progress',
      content: {
        milestone_id: completion.milestone_id,
        submission: completion.submission_content,
        ai_insight: completion.ai_insight,
        source: 'onboarding_population'
      },
      metadata: {
        milestone_id: completion.milestone_id,
        source: 'onboarding_population',
        tags: ['milestone', 'onboarding']
      }
    });
  }

  /**
   * Update stage progress with populated milestones
   */
  private async updateStageProgress(
    userId: string, 
    stageId: number, 
    completions: Array<{ milestone_id: string }>
  ): Promise<void> {
    const milestoneIds = completions.map(c => c.milestone_id);
    const stageMilestones = stageProgressService.getStageMilestones(stageId);
    const progressPercentage = Math.round((milestoneIds.length / stageMilestones.length) * 100);

    await supabase
      .from('user_stage_progress')
      .upsert({
        user_id: userId,
        stage_id: stageId,
        milestones_completed: milestoneIds,
        total_milestones: stageMilestones.length,
        progress_percentage: progressPercentage,
        last_update: new Date().toISOString(),
        confidence_score: 85 // High confidence since populated from onboarding
      }, {
        onConflict: 'user_id,stage_id'
      });
  }

  /**
   * Generate fully personalized milestones based on project context
   */
  private async generatePersonalizedMilestones(
    onboarding: OnboardingData,
    profile: ComprehensiveProfile | null,
    aiConversation: AIConversation | null,
    relevantStages: number[]
  ): Promise<Array<{
    milestone_id: string;
    stage: number;
    condition: () => boolean;
    getSubmission: () => string;
    getContext: () => string;
    getPersonalizedTitle?: () => string;
    getPersonalizedDescription?: () => string;
    getPersonalizedDeliverables?: () => string[];
  }>> {
    // Extract dynamic project context instead of categorizing
    const projectContext = this.extractProjectContext(onboarding);
    const techLevel = onboarding.technical_background;
    const primaryGoal = onboarding.primary_goal;

    // Extract key project details
    const projectName = this.extractProjectName(onboarding);

    const milestones = [];

    // Stage 0: Spark - Problem & Validation
    if (relevantStages.includes(0)) {
      milestones.push({
        milestone_id: 'problem_identified',
        stage: 0,
        condition: () => onboarding.problem_description?.length > 20,
        getSubmission: () => onboarding.problem_description,
        getContext: () => `Problem: ${onboarding.problem_description.substring(0, 200)}...`,
        getPersonalizedTitle: () => `${projectName} Problem Definition`,
        getPersonalizedDescription: () => this.generateDynamicMilestoneContent('problem_identified', onboarding, projectContext),
        getPersonalizedDeliverables: () => this.generateDynamicDeliverables('problem_identified', onboarding, projectContext)
      });

      milestones.push({
        milestone_id: 'idea_validation',
        stage: 0,
        condition: () => onboarding.solution_approach?.length > 50,
        getSubmission: () => `Solution Concept: ${onboarding.solution_approach}`,
        getContext: () => `Validated solution approach: ${onboarding.solution_approach.substring(0, 200)}...`,
        getPersonalizedTitle: () => `${projectName} Solution Validation`,
        getPersonalizedDescription: () => this.generateDynamicMilestoneContent('idea_validation', onboarding, projectContext),
        getPersonalizedDeliverables: () => this.generateDynamicDeliverables('idea_validation', onboarding, projectContext)
      });
    }

    // Stage 1: Formation - Concept & Planning
    if (relevantStages.includes(1)) {
      milestones.push({
        milestone_id: 'concept_clarity',
        stage: 1,
        condition: () => onboarding.unique_value_proposition?.length > 30,
        getSubmission: () => onboarding.unique_value_proposition,
        getContext: () => `Value proposition: ${onboarding.unique_value_proposition.substring(0, 200)}...`,
        getPersonalizedTitle: () => `${projectName} Value Proposition`,
        getPersonalizedDescription: () => this.generateDynamicMilestoneContent('concept_clarity', onboarding, projectContext),
        getPersonalizedDeliverables: () => this.generateDynamicDeliverables('concept_clarity', onboarding, projectContext)
      });

      milestones.push({
        milestone_id: 'milestone_planning',
        stage: 1,
        condition: () => onboarding.primary_goal && onboarding.solution_approach,
        getSubmission: () => `Goal: ${primaryGoal}\nApproach: ${onboarding.solution_approach?.substring(0, 200)}`,
        getContext: () => `Planning roadmap for ${primaryGoal} goal`,
        getPersonalizedTitle: () => `${projectName} Development Roadmap`,
        getPersonalizedDescription: () => this.generateDynamicMilestoneContent('milestone_planning', onboarding, projectContext),
        getPersonalizedDeliverables: () => this.generateDynamicDeliverables('milestone_planning', onboarding, projectContext)
      });
    }

    // Stage 2: First Build - MVP & Implementation
    if (relevantStages.includes(2)) {
      milestones.push({
        milestone_id: 'mvp_designed',
        stage: 2,
        condition: () => onboarding.solution_approach?.length > 100 && techLevel !== 'beginner',
        getSubmission: () => `MVP Design: ${onboarding.solution_approach}`,
        getContext: () => `Technical approach: ${onboarding.solution_approach.substring(0, 200)}...`,
        getPersonalizedTitle: () => `${projectName} MVP Architecture`,
        getPersonalizedDescription: () => this.generateDynamicMilestoneContent('mvp_designed', onboarding, projectContext),
        getPersonalizedDeliverables: () => this.generateDynamicDeliverables('mvp_designed', onboarding, projectContext)
      });

      milestones.push({
        milestone_id: 'technical_challenges',
        stage: 2,
        condition: () => techLevel === 'intermediate' || techLevel === 'expert',
        getSubmission: () => `Technical Background: ${techLevel}\nApproach: ${onboarding.solution_approach}`,
        getContext: () => `Working on technical implementation with ${techLevel} background`,
        getPersonalizedTitle: () => `${projectName} Technical Implementation`,
        getPersonalizedDescription: () => this.generateDynamicMilestoneContent('technical_challenges', onboarding, projectContext),
        getPersonalizedDeliverables: () => this.generateDynamicDeliverables('technical_challenges', onboarding, projectContext)
      });
    }

    return milestones;
  }

  /**
   * Generate AI insight for a milestone based on onboarding data
   */
  private async generateMilestoneInsight(
    milestone: any,
    submissionContent: string,
    onboarding: OnboardingData,
    profile: ComprehensiveProfile | null,
    aiConversation: AIConversation | null
  ): Promise<string> {
    try {
      const prompt = `
You are a startup mentor providing balanced, constructive feedback on a milestone submission.

Milestone: "${milestone.title}"
User's Response: "${submissionContent}"

Project Context:
- Track: ${onboarding.track}
- Technical Background: ${onboarding.technical_background}
- Primary Goal: ${onboarding.primary_goal}
${profile ? `- Project Viability Score: ${profile.project_viability_score}/100` : ''}

Provide professional feedback (2-3 sentences) that:
1. Acknowledges what they've accomplished so far
2. Identifies one specific strength in their approach
3. Points out one area that needs development or validation
4. Suggests a concrete next step

Tone: Professional, honest, and constructive. Avoid overly enthusiastic language like "exceptional," "brilliant," or "fantastic." Focus on actionable insights and realistic assessment.
`;

      const result = await geminiService.model?.generateContent(prompt);
      const aiInsight = await result?.response.text();

      if (aiInsight && aiInsight.trim()) {
        return aiInsight.trim();
      } else {
        // Fallback to rule-based insight
        const projectContext = this.extractProjectContext(onboarding);
        return this.generatePersonalizedInsight(milestone.id, submissionContent, projectContext, onboarding);
      }
    } catch (error) {
      console.error('Error generating AI milestone insight:', error);
      // Fallback to rule-based insight
      const projectContext = this.extractProjectContext(onboarding);
      return this.generatePersonalizedInsight(milestone.id, submissionContent, projectContext, onboarding);
    }
  }

  /**
   * Generate AI summary of submission content
   */
  private async generateAISummary(content: string, milestoneId: string): Promise<string> {
    try {
      const prompt = `
Create a concise summary of this milestone response in 1-2 sentences (max 120 characters):

Milestone: "${milestoneId.replace('_', ' ')}"
Response: "${content}"

Extract the core concept and key details. Be specific and clear, avoiding generic language.
`;

      const result = await geminiService.model?.generateContent(prompt);
      const summary = await result?.response.text();

      if (summary && summary.trim()) {
        return summary.trim();
      } else {
        // Fallback to manual summarization
        return this.summarizeSubmission(content, milestoneId);
      }
    } catch (error) {
      console.error('Error generating AI summary:', error);
      // Fallback to manual summarization
      return this.summarizeSubmission(content, milestoneId);
    }
  }

  /**
   * Summarize submission content to be concise and clear
   */
  private summarizeSubmission(content: string, milestoneId: string): string {
    if (!content) return '';

    // For problem identification, extract the core problem
    if (milestoneId === 'problem_identified') {
      // Extract key phrases about the problem
      const problemKeywords = ['problem', 'issue', 'challenge', 'difficulty', 'pain point'];
      const sentences = content.split('.').filter(s => s.length > 10);
      const problemSentence = sentences.find(s =>
        problemKeywords.some(keyword => s.toLowerCase().includes(keyword))
      ) || sentences[0];

      return problemSentence?.trim().substring(0, 150) + (problemSentence?.length > 150 ? '...' : '');
    }

    // For other milestones, take first meaningful sentence
    const firstSentence = content.split('.')[0];
    return firstSentence.substring(0, 120) + (firstSentence.length > 120 ? '...' : '');
  }

  /**
   * Extract project name from onboarding data
   */
  private extractProjectName(onboarding: OnboardingData): string {
    const description = onboarding.problem_description?.toLowerCase() || '';

    if (description.includes('tremor') || description.includes('parkinson')) {
      return 'TremorStabilizer';
    } else if (description.includes('app')) {
      return 'YourApp';
    } else if (description.includes('platform')) {
      return 'YourPlatform';
    } else if (description.includes('device')) {
      return 'YourDevice';
    }

    return 'YourProject';
  }

  /**
   * Extract core technology from onboarding data
   */
  private extractCoreTechnology(onboarding: OnboardingData): string {
    const solution = onboarding.solution_approach?.toLowerCase() || '';

    if (solution.includes('sensor') || solution.includes('accelerometer') || solution.includes('gyroscope')) {
      return 'sensor technology';
    } else if (solution.includes('ai') || solution.includes('machine learning')) {
      return 'AI/ML';
    } else if (solution.includes('blockchain')) {
      return 'blockchain';
    } else if (solution.includes('mobile') || solution.includes('app')) {
      return 'mobile technology';
    } else if (solution.includes('web') || solution.includes('platform')) {
      return 'web platform';
    }

    return 'technology';
  }

  /**
   * Extract project context for dynamic personalization
   */
  private extractProjectContext(onboarding: OnboardingData): {
    problemDomain: string;
    solutionType: string;
    targetUsers: string;
    keyTechnologies: string[];
    primaryChallenge: string;
    successMetrics: string;
  } {
    return {
      problemDomain: onboarding.problem_description || 'unspecified domain',
      solutionType: onboarding.solution_approach || 'unspecified solution',
      targetUsers: onboarding.target_audience || 'unspecified users',
      keyTechnologies: this.extractTechnologies(onboarding),
      primaryChallenge: onboarding.biggest_challenge || 'unspecified challenge',
      successMetrics: onboarding.primary_goal || 'unspecified goal'
    };
  }

  /**
   * Extract technologies mentioned in the project
   */
  private extractTechnologies(onboarding: OnboardingData): string[] {
    const text = `${onboarding.problem_description || ''} ${onboarding.solution_approach || ''}`.toLowerCase();
    const technologies: string[] = [];

    // Extract any technology keywords mentioned
    const techKeywords = ['sensor', 'ai', 'ml', 'app', 'web', 'mobile', 'hardware', 'software', 'api', 'database', 'cloud', 'iot', 'blockchain', 'vr', 'ar'];
    techKeywords.forEach(tech => {
      if (text.includes(tech)) {
        technologies.push(tech);
      }
    });

    return technologies.length > 0 ? technologies : ['general technology'];
  }

  /**
   * Generate validation deliverables based on project type
   */
  private generateValidationDeliverables(projectType: string, targetUsers: string, coreTechnology: string): string[] {
    const base = [
      `Conduct 5-10 interviews with ${targetUsers}`,
      `Create simple prototype or mockup to test concept`,
      `Document user feedback and pain point validation`,
      `Refine solution based on user insights`
    ];

    if (projectType.includes('health')) {
      return [
        `Interview healthcare professionals and patients`,
        `Research medical device regulations and requirements`,
        `Validate ${coreTechnology} feasibility with experts`,
        `Document clinical need and potential impact`
      ];
    } else if (projectType.includes('hardware')) {
      return [
        `Build proof-of-concept with ${coreTechnology}`,
        `Test core functionality with target users`,
        `Validate technical feasibility and constraints`,
        `Document hardware requirements and specifications`
      ];
    }

    return base;
  }

  /**
   * Generate planning deliverables based on goal and project type
   */
  private generatePlanningDeliverables(primaryGoal: string, projectType: string, techLevel: string): string[] {
    const baseDeliverables = [
      'Define project scope and core features',
      'Create development timeline and milestones',
      'Identify required resources and skills',
      'Set up project tracking and communication'
    ];

    if (primaryGoal === 'build_mvp') {
      return [
        'Define MVP feature set and user stories',
        'Create technical architecture and design docs',
        'Plan development sprints and deliverables',
        'Set up development environment and tools'
      ];
    } else if (primaryGoal === 'find_users') {
      return [
        'Develop user acquisition strategy',
        'Create marketing and outreach plan',
        'Design user onboarding and feedback systems',
        'Plan community building and engagement'
      ];
    } else if (primaryGoal === 'learn') {
      return [
        'Identify key learning objectives and skills',
        'Create learning roadmap and resources',
        'Plan hands-on projects and experiments',
        'Set up mentorship and feedback loops'
      ];
    }

    return baseDeliverables;
  }

  /**
   * Generate MVP deliverables based on project specifics
   */
  private generateMVPDeliverables(projectType: string, coreTechnology: string, techLevel: string): string[] {
    if (projectType.includes('health') && coreTechnology.includes('sensor')) {
      return [
        'Design sensor calibration and data collection system',
        'Develop real-time signal processing algorithms',
        'Create user interface for device control and feedback',
        'Implement safety protocols and error handling',
        'Build prototype testing and validation framework'
      ];
    } else if (projectType.includes('hardware')) {
      return [
        'Design core hardware architecture and components',
        'Develop firmware for device functionality',
        'Create companion software/app interface',
        'Implement communication protocols and data handling',
        'Build testing and quality assurance processes'
      ];
    } else if (projectType.includes('software')) {
      return [
        'Develop core application features and user flows',
        'Design and implement user interface/experience',
        'Set up backend infrastructure and data management',
        'Implement user authentication and security',
        'Create testing and deployment pipeline'
      ];
    }

    return [
      'Design core system architecture',
      'Implement essential functionality',
      'Create user interface and experience',
      'Set up testing and validation processes',
      'Prepare for user feedback and iteration'
    ];
  }

  /**
   * Generate technical deliverables based on project specifics
   */
  private generateTechnicalDeliverables(projectType: string, coreTechnology: string, techLevel: string): string[] {
    if (projectType.includes('health') && coreTechnology.includes('sensor')) {
      return [
        'Optimize sensor fusion algorithms for accuracy',
        'Implement real-time tremor detection and response',
        'Develop calibration system for individual users',
        'Create data logging and analysis capabilities',
        'Ensure medical device compliance and safety'
      ];
    } else if (projectType.includes('hardware')) {
      return [
        'Solve power management and battery optimization',
        'Implement wireless communication and connectivity',
        'Optimize performance and reduce latency',
        'Design for manufacturability and cost efficiency',
        'Create robust testing and quality control systems'
      ];
    }

    return [
      'Optimize system performance and scalability',
      'Implement advanced features and functionality',
      'Solve integration and compatibility challenges',
      'Create monitoring and analytics capabilities',
      'Prepare for production deployment'
    ];
  }

  /**
   * Analyze validation evidence from user's onboarding responses
   */
  private analyzeValidationEvidence(onboarding: OnboardingData): {
    hasUserInterviews: boolean;
    hasCompetitiveResearch: boolean;
    hasProblemValidation: boolean;
    hasSolutionValidation: boolean;
    hasTechnicalValidation: boolean;
    hasMarketResearch: boolean;
    evidenceLevel: 'none' | 'minimal' | 'moderate' | 'strong';
    specificEvidence: string[];
  } {
    const evidence = {
      hasUserInterviews: false,
      hasCompetitiveResearch: false,
      hasProblemValidation: false,
      hasSolutionValidation: false,
      hasTechnicalValidation: false,
      hasMarketResearch: false,
      evidenceLevel: 'none' as const,
      specificEvidence: [] as string[]
    };

    const allText = `${onboarding.problem_description || ''} ${onboarding.solution_approach || ''} ${onboarding.unique_value_proposition || ''} ${onboarding.previous_experience || ''} ${onboarding.success_metrics || ''}`.toLowerCase();

    // Check for user interview evidence
    if (allText.includes('interview') || allText.includes('talked to') || allText.includes('spoke with') || allText.includes('feedback from') || allText.includes('user research') || allText.includes('survey')) {
      evidence.hasUserInterviews = true;
      evidence.specificEvidence.push('user interviews/research mentioned');
    }

    // Check for competitive research evidence
    if (allText.includes('competitor') || allText.includes('existing solution') || allText.includes('alternative') || allText.includes('compared to') || allText.includes('market analysis') || allText.includes('research')) {
      evidence.hasCompetitiveResearch = true;
      evidence.specificEvidence.push('competitive research mentioned');
    }

    // Check for problem validation evidence
    if (allText.includes('validated') || allText.includes('confirmed') || allText.includes('pain point') || allText.includes('struggle') || allText.includes('challenge') || allText.includes('frustration')) {
      evidence.hasProblemValidation = true;
      evidence.specificEvidence.push('problem validation evidence');
    }

    // Check for solution validation evidence
    if (allText.includes('prototype') || allText.includes('tested') || allText.includes('mvp') || allText.includes('pilot') || allText.includes('proof of concept') || allText.includes('demo')) {
      evidence.hasSolutionValidation = true;
      evidence.specificEvidence.push('solution validation evidence');
    }

    // Check for technical validation evidence
    if (allText.includes('built') || allText.includes('developed') || allText.includes('implemented') || allText.includes('technical') || allText.includes('code') || allText.includes('architecture')) {
      evidence.hasTechnicalValidation = true;
      evidence.specificEvidence.push('technical validation evidence');
    }

    // Check for market research evidence
    if (allText.includes('market') || allText.includes('industry') || allText.includes('trend') || allText.includes('opportunity') || allText.includes('demand') || allText.includes('size')) {
      evidence.hasMarketResearch = true;
      evidence.specificEvidence.push('market research evidence');
    }

    // Determine evidence level
    const evidenceCount = [
      evidence.hasUserInterviews,
      evidence.hasCompetitiveResearch,
      evidence.hasProblemValidation,
      evidence.hasSolutionValidation,
      evidence.hasTechnicalValidation,
      evidence.hasMarketResearch
    ].filter(Boolean).length;

    if (evidenceCount >= 4) evidence.evidenceLevel = 'strong';
    else if (evidenceCount >= 2) evidence.evidenceLevel = 'moderate';
    else if (evidenceCount >= 1) evidence.evidenceLevel = 'minimal';
    else evidence.evidenceLevel = 'none';

    console.log('🔍 Validation Evidence Analysis:', {
      evidenceLevel: evidence.evidenceLevel,
      evidenceCount,
      specificEvidence: evidence.specificEvidence,
      hasUserInterviews: evidence.hasUserInterviews,
      hasCompetitiveResearch: evidence.hasCompetitiveResearch,
      hasProblemValidation: evidence.hasProblemValidation
    });

    return evidence;
  }

  /**
   * Generate dynamic, project-specific milestone content using AI with evidence assessment
   */
  private async generateDynamicMilestoneContent(
    milestoneId: string,
    onboarding: OnboardingData,
    projectContext: {
      problemDomain: string;
      solutionType: string;
      targetUsers: string;
      keyTechnologies: string[];
      primaryChallenge: string;
      successMetrics: string;
    }
  ): Promise<string> {
    try {
      // Analyze what validation evidence the user has already provided
      const validationEvidence = this.analyzeValidationEvidence(onboarding);

      const prompt = `
You are a startup mentor creating a personalized milestone description for "${milestoneId.replace('_', ' ')}".

PROJECT CONTEXT:
- Problem: ${projectContext.problemDomain}
- Solution: ${projectContext.solutionType}
- Target Users: ${projectContext.targetUsers}
- Technologies: ${projectContext.keyTechnologies.join(', ')}
- Challenge: ${projectContext.primaryChallenge}
- Goal: ${projectContext.successMetrics}
- Technical Level: ${onboarding.technical_background}

VALIDATION EVIDENCE ANALYSIS:
- Evidence Level: ${validationEvidence.evidenceLevel}
- User Interviews: ${validationEvidence.hasUserInterviews ? 'Evidence found' : 'No evidence'}
- Competitive Research: ${validationEvidence.hasCompetitiveResearch ? 'Evidence found' : 'No evidence'}
- Problem Validation: ${validationEvidence.hasProblemValidation ? 'Evidence found' : 'No evidence'}
- Solution Validation: ${validationEvidence.hasSolutionValidation ? 'Evidence found' : 'No evidence'}
- Technical Validation: ${validationEvidence.hasTechnicalValidation ? 'Evidence found' : 'No evidence'}
- Market Research: ${validationEvidence.hasMarketResearch ? 'Evidence found' : 'No evidence'}
- Specific Evidence: ${validationEvidence.specificEvidence.join(', ') || 'None detected'}

INSTRUCTIONS:
1. Analyze their validation evidence level and create appropriate next steps
2. If evidence is STRONG: Build upon their existing work with advanced validation
3. If evidence is MODERATE: Expand and deepen their current validation efforts
4. If evidence is MINIMAL: Guide them to strengthen their validation foundation
5. If evidence is NONE: Start with basic validation activities

6. Be specific to their project (use actual problem/solution details)
7. Keep description to 1-2 sentences maximum
8. Reference their specific evidence when building upon existing work
9. Don't ask them to repeat validation work they've already mentioned

Generate a milestone description that appropriately matches their current validation progress.
`;

      const result = await geminiService.model?.generateContent(prompt);
      const content = await result?.response.text();

      if (content && content.trim()) {
        return content.trim();
      } else {
        return this.getFallbackMilestoneContent(milestoneId, onboarding, projectContext);
      }
    } catch (error) {
      console.error('Error generating dynamic milestone content:', error);
      return this.getFallbackMilestoneContent(milestoneId, onboarding, projectContext);
    }
  }

  /**
   * Generate dynamic, project-specific deliverables using AI
   */
  private async generateDynamicDeliverables(
    milestoneId: string,
    onboarding: OnboardingData,
    projectContext: {
      problemDomain: string;
      solutionType: string;
      targetUsers: string;
      keyTechnologies: string[];
      primaryChallenge: string;
      successMetrics: string;
    }
  ): Promise<string[]> {
    try {
      // Analyze validation evidence for deliverables
      const validationEvidence = this.analyzeValidationEvidence(onboarding);

      const prompt = `
Create 3-4 specific deliverables for "${milestoneId.replace('_', ' ')}" milestone:

PROJECT CONTEXT:
- Problem: ${projectContext.problemDomain}
- Solution: ${projectContext.solutionType}
- Target Users: ${projectContext.targetUsers}
- Technologies: ${projectContext.keyTechnologies.join(', ')}
- Challenge: ${projectContext.primaryChallenge}
- Goal: ${projectContext.successMetrics}
- Technical Level: ${onboarding.technical_background}

VALIDATION EVIDENCE ANALYSIS:
- Evidence Level: ${validationEvidence.evidenceLevel}
- User Interviews: ${validationEvidence.hasUserInterviews ? 'Evidence found' : 'No evidence'}
- Competitive Research: ${validationEvidence.hasCompetitiveResearch ? 'Evidence found' : 'No evidence'}
- Problem Validation: ${validationEvidence.hasProblemValidation ? 'Evidence found' : 'No evidence'}
- Solution Validation: ${validationEvidence.hasSolutionValidation ? 'Evidence found' : 'No evidence'}
- Technical Validation: ${validationEvidence.hasTechnicalValidation ? 'Evidence found' : 'No evidence'}
- Specific Evidence: ${validationEvidence.specificEvidence.join(', ') || 'None detected'}

DELIVERABLE INSTRUCTIONS:
1. Analyze their validation evidence level and create appropriate deliverables
2. If evidence is STRONG: Create advanced validation and execution deliverables
3. If evidence is MODERATE: Create deliverables that expand their current work
4. If evidence is MINIMAL: Create deliverables that strengthen their foundation
5. If evidence is NONE: Create basic validation deliverables

6. Be specific to their project (use actual problem/solution details)
7. Make deliverables actionable with clear, measurable outcomes
8. Consider their technical background (${onboarding.technical_background})
9. Build upon existing evidence rather than duplicating work already mentioned
10. Reference their specific technologies and target users

Generate deliverables that match their current validation progress and advance their work appropriately.

Format as a simple list, one deliverable per line, no bullet points or numbers.
`;

      const result = await geminiService.model?.generateContent(prompt);
      const content = await result?.response.text();

      if (content && content.trim()) {
        return content.trim().split('\n').filter(line => line.trim().length > 0);
      } else {
        return this.getFallbackDeliverables(milestoneId, onboarding, projectContext);
      }
    } catch (error) {
      console.error('Error generating dynamic deliverables:', error);
      return this.getFallbackDeliverables(milestoneId, onboarding, projectContext);
    }
  }

  /**
   * Fallback milestone content if AI generation fails
   */
  private getFallbackMilestoneContent(
    milestoneId: string,
    onboarding: OnboardingData,
    projectContext: {
      problemDomain: string;
      solutionType: string;
      targetUsers: string;
      keyTechnologies: string[];
      primaryChallenge: string;
      successMetrics: string;
    }
  ): string {
    // Analyze validation evidence for fallback content
    const validationEvidence = this.analyzeValidationEvidence(onboarding);

    // Evidence-aware fallbacks based on what they've already provided
    const fallbacks = {
      'problem_identified': validationEvidence.hasProblemValidation
        ? `Expand your ${projectContext.problemDomain} research by interviewing 3-5 additional ${projectContext.targetUsers} to validate your TremorStabilizer approach.`
        : `Interview 5-7 ${projectContext.targetUsers} to validate the specific challenges in ${projectContext.problemDomain} that your solution addresses.`,

      'idea_validation': validationEvidence.hasSolutionValidation
        ? `Test your ${projectContext.solutionType} prototype with ${projectContext.targetUsers} and iterate based on their feedback.`
        : `Create a simple prototype of your ${projectContext.solutionType} to validate core assumptions with ${projectContext.targetUsers}.`,

      'concept_clarity': validationEvidence.hasCompetitiveResearch
        ? `Refine your unique positioning for ${projectContext.solutionType} based on competitive insights and user feedback.`
        : `Research existing solutions in ${projectContext.problemDomain} and define what makes your approach uniquely valuable.`,

      'mvp_designed': validationEvidence.hasTechnicalValidation
        ? `Enhance your ${projectContext.solutionType} with advanced features using ${projectContext.keyTechnologies.join(' and ')}.`
        : `Design and build core features of your ${projectContext.solutionType} using ${projectContext.keyTechnologies.join(' and ')}.`,

      'technical_challenges': validationEvidence.hasTechnicalValidation
        ? `Optimize your ${projectContext.solutionType} implementation to address ${projectContext.primaryChallenge} more effectively.`
        : `Identify and solve the biggest technical hurdles for implementing ${projectContext.solutionType} given your ${onboarding.technical_background} background.`
    };

    return fallbacks[milestoneId as keyof typeof fallbacks] ||
           `Advance your ${milestoneId.replace('_', ' ')} work specific to ${projectContext.problemDomain} based on your current progress.`;
  }

  /**
   * Fallback deliverables if AI generation fails
   */
  private getFallbackDeliverables(
    milestoneId: string,
    onboarding: OnboardingData,
    projectContext: {
      problemDomain: string;
      solutionType: string;
      targetUsers: string;
      keyTechnologies: string[];
      primaryChallenge: string;
      successMetrics: string;
    }
  ): string[] {
    // Analyze validation evidence for fallback deliverables
    const validationEvidence = this.analyzeValidationEvidence(onboarding);

    // Evidence-aware fallback deliverables
    const fallbacks = {
      'problem_identified': validationEvidence.hasProblemValidation ? [
        `Expand your user research by interviewing 3-5 additional ${projectContext.targetUsers} about TremorStabilizer needs`,
        `Quantify the impact of tremor challenges on daily activities through structured surveys`,
        `Document specific use cases where your solution would provide the most value`,
        `Validate your technical approach with healthcare professionals or occupational therapists`
      ] : [
        `Interview 5-7 ${projectContext.targetUsers} about challenges in ${projectContext.problemDomain}`,
        `Document specific pain points and their impact on ${projectContext.targetUsers}`,
        `Research existing solutions in ${projectContext.problemDomain} and their limitations`,
        `Define success metrics for addressing ${projectContext.primaryChallenge}`
      ],
      'idea_validation': validationEvidence.hasSolutionValidation ? [
        `Test your ${projectContext.solutionType} prototype with 5-7 ${projectContext.targetUsers} and gather detailed feedback`,
        `Iterate on your design based on user testing results and technical constraints`,
        `Validate key assumptions about tremor patterns and device effectiveness`,
        `Document user experience insights and technical performance metrics`
      ] : [
        `Create simple prototype of ${projectContext.solutionType} to test with ${projectContext.targetUsers}`,
        `Conduct validation interviews about your approach to ${projectContext.problemDomain}`,
        `Test core assumptions about ${projectContext.targetUsers} needs and behavior`,
        `Document feedback on ${projectContext.solutionType} and iterate`
      ],
      'concept_clarity': validationEvidence.hasCompetitiveResearch ? [
        `Refine your unique positioning based on competitive analysis and user feedback`,
        `Develop clear messaging that differentiates your open-source approach`,
        `Create compelling value proposition for underserved communities`,
        `Test concept clarity with potential users and stakeholders`
      ] : [
        `Define unique value proposition of ${projectContext.solutionType} for ${projectContext.targetUsers}`,
        `Map competitive landscape in ${projectContext.problemDomain}`,
        `Create clear positioning for your approach to ${projectContext.primaryChallenge}`,
        `Validate concept resonance with ${projectContext.targetUsers}`
      ],
      'mvp_designed': [
        `Design core features of ${projectContext.solutionType} using ${projectContext.keyTechnologies.join(' and ')}`,
        `Create technical architecture for ${projectContext.solutionType}`,
        `Plan development roadmap to achieve ${projectContext.successMetrics}`,
        `Define testing strategy for ${projectContext.targetUsers}`
      ],
      'technical_challenges': [
        `Identify technical hurdles in implementing ${projectContext.solutionType}`,
        `Research solutions for ${projectContext.primaryChallenge}`,
        `Plan development approach using ${projectContext.keyTechnologies.join(' and ')}`,
        `Create risk mitigation strategy for technical implementation`
      ]
    };

    return fallbacks[milestoneId as keyof typeof fallbacks] || [
      `Define specific objectives for ${milestoneId.replace('_', ' ')} related to ${projectContext.problemDomain}`,
      `Create actionable plan to address ${projectContext.primaryChallenge}`,
      `Execute key activities relevant to ${projectContext.solutionType}`,
      `Document results and learnings for ${projectContext.targetUsers}`
    ];
  }

  /**
   * Generate personalized insight based on milestone and project context
   */
  private generatePersonalizedInsight(
    milestoneId: string,
    summary: string,
    projectContext: {
      problemDomain: string;
      solutionType: string;
      targetUsers: string;
      keyTechnologies: string[];
      primaryChallenge: string;
      successMetrics: string;
    },
    onboarding: OnboardingData
  ): string {
    // Generate dynamic insights based on actual project context
    const insights = {
      'problem_identified': `Your problem definition in ${projectContext.problemDomain} addresses a clear need for ${projectContext.targetUsers}. The focus on "${summary}" is well-targeted. Next step: validate the problem scope with ${projectContext.targetUsers} and quantify the impact on ${projectContext.primaryChallenge}.`,

      'idea_validation': `Your solution approach (${projectContext.solutionType}) shows understanding of ${projectContext.problemDomain}. The concept has merit but needs validation with ${projectContext.targetUsers}. Focus on testing core assumptions about ${projectContext.primaryChallenge} and user willingness to adopt your approach.`,

      'concept_clarity': `Your value proposition for ${projectContext.targetUsers} articulates clear benefits in ${projectContext.problemDomain}. The differentiation is apparent but needs market validation. Research how ${projectContext.targetUsers} currently address ${projectContext.primaryChallenge} and their willingness to switch.`,

      'mvp_designed': `Your MVP design using ${projectContext.keyTechnologies.join(' and ')} demonstrates clear technical thinking for ${projectContext.problemDomain}. The scope addresses ${projectContext.primaryChallenge} but consider simplifying to core functionality first. Focus on delivering value to ${projectContext.targetUsers} and plan for iterative development.`,

      'technical_challenges': `Your technical approach to ${projectContext.primaryChallenge} using ${projectContext.keyTechnologies.join(' and ')} shows good understanding of implementation requirements. The solution is feasible given your ${onboarding.technical_background} background. Consider breaking down complex challenges and validating technical assumptions early.`,

      'milestone_planning': `Your development roadmap toward ${projectContext.successMetrics} shows strategic thinking about ${projectContext.problemDomain}. The plan addresses key needs of ${projectContext.targetUsers}. Ensure milestones include regular validation points and feedback from ${projectContext.targetUsers}.`
    };

    return insights[milestoneId as keyof typeof insights] ||
           `Great work on ${milestoneId.replace('_', ' ')}! Your approach to ${projectContext.problemDomain} shows solid thinking and preparation for next steps with ${projectContext.targetUsers}.`;
  }

  /**
   * Get the stage that a milestone belongs to
   */
  private getMilestoneStage(milestoneId: string): number {
    const stageMapping: { [key: string]: number } = {
      // Stage 0 milestones
      'problem_identified': 0,
      'market_research': 0,
      'user_interviews': 0,
      'idea_validation': 0,

      // Stage 1 milestones
      'concept_clarity': 1,
      'team_building': 1,
      'milestone_planning': 1,
      'feedback_loops': 1,

      // Stage 2 milestones
      'mvp_designed': 2,
      'core_features': 2,
      'technical_challenges': 2,
      'user_testing': 2,

      // Add more as needed
    };

    return stageMapping[milestoneId] || 0;
  }

  /**
   * Check if journey has already been populated from onboarding
   */
  async hasJourneyBeenPopulated(userId: string): Promise<boolean> {
    const memories = await userMemoryService.getUserMemories(userId, 'progress', 10);
    return memories.some(memory =>
      memory.content?.journey_populated_from_onboarding === true
    );
  }
}

const onboardingToJourneyService = new OnboardingToJourneyService();
export default onboardingToJourneyService;
