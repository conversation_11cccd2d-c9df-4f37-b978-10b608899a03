import React from 'react';
import { Send } from 'lucide-react';
import { motion } from 'framer-motion';
import { useScrollAnimation, scrollVariants } from '@/hooks/useScrollAnimation';
import ApplicationFormComponent from './shared/ApplicationFormComponent';

const ApplicationForm = () => {
  const { ref, isInView } = useScrollAnimation();

  return (
    <section className="relative py-32 overflow-visible bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black" ref={ref} id="apply">
      {/* Animated background */}
      <div className="absolute inset-0">
        {[...Array(25)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-30, 30, -30],
              x: [-20, 20, -20],
              opacity: [0.2, 0.8, 0.2],
              scale: [0.5, 1.2, 0.5],
            }}
            transition={{
              duration: 6 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={scrollVariants}
          className="text-center mb-16"
        >
          <motion.div 
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 rounded-full border border-sauce-red/20 mb-6"
            whileHover={{ scale: 1.05 }}
          >
            <Send className="w-4 h-4 text-sauce-red" />
            <span className="text-sauce-red font-semibold">Ready to Build</span>
          </motion.div>
          
          <h2 className="text-5xl sm:text-6xl font-black text-accent-white mb-6">
            Apply to{" "}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
              Pie Fi
            </span>
          </h2>
          <p className="text-xl text-crust-beige/80 max-w-3xl mx-auto">
            Ready to build something amazing? Tell us about yourself.
          </p>
        </motion.div>

        <ApplicationFormComponent />
      </div>
    </section>
  );
};

export default ApplicationForm;
