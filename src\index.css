@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme (using Crust Beige as a base, <PERSON><PERSON> for text) */
    --background: 20 100% 93%; /* crust-beige: #FFF2DD */
    --foreground: 0 0% 9%;    /* oven-black: #181818 */

    --card: 20 100% 93%;
    --card-foreground: 0 0% 9%;

    --popover: 20 100% 93%;
    --popover-foreground: 0 0% 9%;

    --primary: 33 99% 62%; /* cheese-gold: #FFD23F */
    --primary-foreground: 0 0% 9%; /* oven-black for text on gold */

    --secondary: 0 100% 64%; /* sauce-red: #FF4747 */
    --secondary-foreground: 0 0% 100%; /* accent-white for text on red */

    --muted: 0 0% 90%; /* Lighter gray for muted */
    --muted-foreground: 0 0% 40%; /* Darker gray for muted text */

    --accent: 0 0% 95%; /* Lighter gray for accent */
    --accent-foreground: 0 0% 9%;

    --destructive: 0 100% 64%; /* sauce-red: #FF4747 */
    --destructive-foreground: 0 0% 100%; /* accent-white */

    --border: 0 0% 85%;
    --input: 0 0% 85%;
    --ring: 33 99% 62%; /* cheese-gold */

    --radius: 0.5rem;

    /* ------------------------------------------------------------------ */
    /* Tailwind CSS gradient & opacity variable fallbacks                */
    /* These ensure that declarations like                                */
    /*   background-image: linear-gradient(to right,var(--tw-gradient-stops))
    /* don't error in browsers (notably Firefox) when the position vars   */
    /* are absent. They do not change any visuals because utilities that  */
    /* explicitly set these vars will still override the defaults below.  */
    /* ------------------------------------------------------------------ */
    --tw-gradient-from-position: 0%;
    --tw-gradient-to-position: 100%;
    --tw-gradient-via-position: 50%;
    --tw-text-opacity: 1;
    --tw-bg-opacity: 1;
    --tw-border-opacity: 1;
  }

  .dark {
    --background: 0 0% 9%;    /* oven-black: #181818 */
    --foreground: 0 0% 100%;  /* accent-white: #FFFFFF */

    --card: 0 0% 14%; /* Slightly lighter black for cards: #232323 */
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 100%;

    --primary: 33 99% 62%; /* cheese-gold: #FFD23F */
    --primary-foreground: 0 0% 9%; /* oven-black for text on gold */

    --secondary: 0 100% 64%; /* sauce-red: #FF4747 */
    --secondary-foreground: 0 0% 100%; /* accent-white for text on red */

    --muted: 0 0% 14%; /* #232323 */
    --muted-foreground: 0 0% 60%; /* Lighter gray for muted text in dark */

    --accent: 0 0% 20%; /* Darker accent */
    --accent-foreground: 0 0% 100%;

    --destructive: 0 100% 64%; /* sauce-red: #FF4747 */
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 33 99% 62%; /* cheese-gold */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
    overflow-x: hidden;
    /* Mobile optimizations */
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    /* Smooth scrolling with reduced motion support */
    scroll-behavior: smooth;
    /* Prevent horizontal scrolling */
    max-width: 100vw;
  }
  
  /* Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
      animation-duration: 0.3s !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.3s !important;
    }
    
    body {
      scroll-behavior: auto;
    }
  }
  
  /* Apply scale to a wrapper div instead of body to avoid dropdown positioning issues */
  #root {
    min-height: 100vh; /* Minimum height to viewport */
  }
  
  /* Only apply scaling on large screens where it's beneficial */
  @media (min-width: 1200px) {
    /* Removed root scaling which was causing layout issues */
  }
  

  
  html {
    scroll-behavior: smooth;
    /* Mobile Safari specific fixes */
    -webkit-text-size-adjust: 100%;
    height: 100%;
  }
  
  /* Allow scrolling on body */
  body {
    height: auto;
    min-height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Mobile-specific optimizations */
  @media (max-width: 768px) {
    /* Only reduce specific heavy animations, not all */
    .blur-3xl {
      filter: blur(32px);
    }
    
    /* Disable parallax effects on mobile */
    .parallax {
      transform: none !important;
    }
    
    /* Optimize backdrop filters for mobile */
    .backdrop-blur-sm {
      backdrop-filter: blur(4px);
    }
    
    .backdrop-blur-md {
      backdrop-filter: blur(8px);
    }
    
    .backdrop-blur-lg {
      backdrop-filter: blur(12px);
    }
    
    .backdrop-blur-xl {
      backdrop-filter: blur(16px);
    }
    
    /* Reduce blur amounts on mobile for performance */
    .blur-3xl {
      filter: blur(32px);
    }
  }
  
  /* Prevent text selection on UI elements */
  .select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  /* Optimize touch interactions */
  .touch-manipulation {
    touch-action: manipulation;
  }
  
  /* Fix iOS form zoom */
  @media (max-width: 768px) {
    input[type="text"],
    input[type="email"],
    input[type="tel"],
    input[type="password"],
    input[type="url"],
    textarea,
    select {
      font-size: 16px !important;
    }
  }
}

@layer components {
  .section-padding {
    @apply py-16 md:py-24 px-4 sm:px-6 lg:px-8;
  }
  .section-title {
    @apply text-4xl sm:text-5xl lg:text-6xl font-extrabold tracking-tight text-center;
  }
  .section-subtitle {
    @apply mt-4 text-lg sm:text-xl text-muted-foreground text-center max-w-3xl mx-auto;
  }
  
  /* Optimized animated background patterns with GPU acceleration */
  .animated-bg {
    background: linear-gradient(-45deg, #181818, #232323, #181818, #1a1a1a);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    will-change: background-position;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
  
  .animated-bg-light {
    background: linear-gradient(-45deg, #FFF2DD, #F5F5DC, #FFF2DD, #FAF0E6);
    background-size: 400% 400%;
    animation: gradientShift 20s ease infinite;
    will-change: background-position;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
  
  /* Simplified floating particles effect for mobile */
  .particles {
    position: relative;
    overflow: hidden;
  }
  
  .particles::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(circle at 20% 80%, rgba(255, 210, 63, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 71, 71, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(255, 210, 63, 0.05) 0%, transparent 50%);
    animation: float 10s ease-in-out infinite;
    pointer-events: none;
    will-change: transform, opacity;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
  
  /* Disable particles animation on mobile to prevent flickering */
  @media (max-width: 768px) {
    .particles::before {
      animation: none;
      opacity: 0.3;
    }
  }
  
  /* Consistent card styling with GPU acceleration */
  .card-consistent {
    @apply bg-accent-white border border-oven-black/10 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col h-full;
    will-change: transform, box-shadow;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
  
  .card-consistent-dark {
    @apply bg-oven-black border border-cheese-gold/20 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col h-full;
    will-change: transform, box-shadow;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  /* Optimized animation utilities */
  .fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
    will-change: transform, opacity;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  .stagger-delay-1 {
    animation-delay: 0.1s;
  }

  .stagger-delay-2 {
    animation-delay: 0.2s;
  }

  .stagger-delay-3 {
    animation-delay: 0.3s;
  }

  .stagger-delay-4 {
    animation-delay: 0.4s;
  }

  .stagger-delay-5 {
    animation-delay: 0.5s;
  }

  .hover-lift {
    @apply transition-all duration-300 ease-out;
    will-change: transform, box-shadow;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  .hover-lift:hover {
    @apply transform -translate-y-2 shadow-xl;
  }

  /* Disable hover effects on touch devices */
  @media (hover: none) and (pointer: coarse) {
    .hover-lift:hover {
      transform: none !important;
    }
  }

  .slide-in-left {
    animation: slideInLeft 0.8s ease-out forwards;
    will-change: transform, opacity;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  .slide-in-right {
    animation: slideInRight 0.8s ease-out forwards;
    will-change: transform, opacity;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
  
  /* Mobile performance optimizations */
  @media (max-width: 768px) {
    .fade-in-up,
    .slide-in-left,
    .slide-in-right {
      animation-duration: 0.4s !important;
    }
    
    .hover-lift {
      transition-duration: 0.2s !important;
    }
  }
  
  /* Optimize transforms for mobile */
  .transform-gpu {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000;
    perspective: 1000;
  }
  
  /* Contain layout for performance */
  .contain-layout {
    contain: layout;
  }
  
  .contain-paint {
    contain: paint;
  }
  
  .contain-strict {
    contain: strict;
  }
}

/* Optimized keyframes with GPU acceleration */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) translateZ(0);
    opacity: 0.7;
  }
  33% {
    transform: translateY(-20px) rotate(1deg) translateZ(0);
    opacity: 0.9;
  }
  66% {
    transform: translateY(10px) rotate(-1deg) translateZ(0);
    opacity: 0.8;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateX(0) translateZ(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateX(0) translateZ(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(255, 71, 71, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 71, 71, 0.6);
  }
}

@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 300% 300%;
  animation: gradientFlow 3s ease-in-out infinite;
}

/* Mobile-optimized animations */
@media (max-width: 768px) {
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px) translateZ(0);
    }
    to {
      opacity: 1;
      transform: translateY(0) translateZ(0);
    }
  }
  
  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px) translateZ(0);
    }
    to {
      opacity: 1;
      transform: translateX(0) translateZ(0);
    }
  }
  
  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px) translateZ(0);
    }
    to {
      opacity: 1;
      transform: translateX(0) translateZ(0);
    }
  }
}

/* Remove experimental Radix overrides that caused mobile mis-positioning */

/* Performance monitoring indicator */
.perf-indicator {
  position: fixed;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 12px;
  z-index: 9999;
  display: none;
}

@media (max-width: 768px) {
  .perf-indicator {
    display: block;
  }
}
