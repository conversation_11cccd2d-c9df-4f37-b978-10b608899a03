import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { 
  User, 
  Settings, 
  Bell, 
  Link2, 
  ChevronLeft, 
  Save,
  Loader2,
  Pizza
} from 'lucide-react';
import { motion } from 'framer-motion';

const Profile = () => {
  const navigate = useNavigate();
  const { user, signOut } = useAuth();
  const [loading, setLoading] = useState(false);
  
  // Form states
  const [fullName, setFullName] = useState(user?.full_name || '');
  const [email, setEmail] = useState(user?.email || '');
  const [notificationSettings, setNotificationSettings] = useState({
    emailUpdates: true,
    weeklyDigest: true,
    communityNotifications: false,
  });

  const handleSaveProfile = async () => {
    setLoading(true);
    // TODO: Update user profile in database
    setTimeout(() => {
      setLoading(false);
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-oven-black">
      {/* Header */}
      <header className="bg-accent-white/5 border-b border-accent-white/10">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Button
              onClick={() => navigate('/dashboard')}
              variant="ghost"
              className="text-accent-white hover:text-cheese-gold"
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
            <Button 
              onClick={signOut}
              variant="outline" 
              className="border-accent-white/20 text-accent-white hover:bg-accent-white/10"
            >
              Sign Out
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-accent-white mb-2">Profile Settings</h1>
            <p className="text-accent-white/60">Manage your account and preferences</p>
          </div>

          <Tabs defaultValue="profile" className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-accent-white/10 mb-8">
              <TabsTrigger value="profile" className="text-accent-white data-[state=active]:bg-accent-white/20">
                <User className="h-4 w-4 mr-2" />
                Profile
              </TabsTrigger>
              <TabsTrigger value="notifications" className="text-accent-white data-[state=active]:bg-accent-white/20">
                <Bell className="h-4 w-4 mr-2" />
                Notifications
              </TabsTrigger>
              <TabsTrigger value="integrations" className="text-accent-white data-[state=active]:bg-accent-white/20">
                <Link2 className="h-4 w-4 mr-2" />
                Integrations
              </TabsTrigger>
            </TabsList>

            <TabsContent value="profile">
              <Card className="bg-accent-white/10 border-accent-white/20">
                <CardHeader>
                  <CardTitle className="text-accent-white">Personal Information</CardTitle>
                  <CardDescription className="text-accent-white/60">
                    Update your profile details
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="fullName" className="text-accent-white">
                      Full Name
                    </Label>
                    <Input
                      id="fullName"
                      value={fullName}
                      onChange={(e) => setFullName(e.target.value)}
                      className="bg-oven-black/50 border-accent-white/30 text-accent-white"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-accent-white">
                      Email
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="bg-oven-black/50 border-accent-white/30 text-accent-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-accent-white">Current Track</Label>
                    <div className="flex items-center gap-2">
                      <Badge className="bg-cheese-gold/20 text-cheese-gold">
                        {user?.track || 'Not Selected'}
                      </Badge>
                      <span className="text-sm text-accent-white/60">
                        Contact support to change tracks
                      </span>
                    </div>
                  </div>

                  <Button
                    onClick={handleSaveProfile}
                    disabled={loading}
                    className="bg-gradient-to-r from-sauce-red to-cheese-gold hover:opacity-90"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notifications">
              <Card className="bg-accent-white/10 border-accent-white/20">
                <CardHeader>
                  <CardTitle className="text-accent-white">Notification Preferences</CardTitle>
                  <CardDescription className="text-accent-white/60">
                    Control how and when we contact you
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="emailUpdates" className="text-accent-white">
                        Email Updates
                      </Label>
                      <p className="text-sm text-accent-white/60">
                        Receive important updates about your progress
                      </p>
                    </div>
                    <Switch
                      id="emailUpdates"
                      checked={notificationSettings.emailUpdates}
                      onCheckedChange={(checked) => 
                        setNotificationSettings(prev => ({ ...prev, emailUpdates: checked }))
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="weeklyDigest" className="text-accent-white">
                        Weekly Digest
                      </Label>
                      <p className="text-sm text-accent-white/60">
                        Summary of your week's progress and insights
                      </p>
                    </div>
                    <Switch
                      id="weeklyDigest"
                      checked={notificationSettings.weeklyDigest}
                      onCheckedChange={(checked) => 
                        setNotificationSettings(prev => ({ ...prev, weeklyDigest: checked }))
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="communityNotifications" className="text-accent-white">
                        Community Notifications
                      </Label>
                      <p className="text-sm text-accent-white/60">
                        Updates from other Pie Fi builders
                      </p>
                    </div>
                    <Switch
                      id="communityNotifications"
                      checked={notificationSettings.communityNotifications}
                      onCheckedChange={(checked) => 
                        setNotificationSettings(prev => ({ ...prev, communityNotifications: checked }))
                      }
                    />
                  </div>

                  <Button
                    onClick={handleSaveProfile}
                    disabled={loading}
                    className="bg-gradient-to-r from-sauce-red to-cheese-gold hover:opacity-90"
                  >
                    Save Preferences
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="integrations">
              <Card className="bg-accent-white/10 border-accent-white/20">
                <CardHeader>
                  <CardTitle className="text-accent-white">Connected Services</CardTitle>
                  <CardDescription className="text-accent-white/60">
                    Manage your external integrations
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-oven-black/50 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-gray-900 to-gray-700 flex items-center justify-center">
                          <span className="text-white font-bold">N</span>
                        </div>
                        <div>
                          <p className="font-medium text-accent-white">Notion</p>
                          <p className="text-sm text-accent-white/60">
                            {user?.notion_workspace_id ? 'Connected' : 'Not connected'}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-accent-white/20 text-accent-white hover:bg-accent-white/10"
                      >
                        {user?.notion_workspace_id ? 'Disconnect' : 'Connect'}
                      </Button>
                    </div>
                  </div>

                  <div className="bg-oven-black/50 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-indigo-600 to-purple-600 flex items-center justify-center">
                          <span className="text-white font-bold">D</span>
                        </div>
                        <div>
                          <p className="font-medium text-accent-white">Discord</p>
                          <p className="text-sm text-accent-white/60">
                            {user?.discord_user_id ? 'Connected' : 'Not connected'}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-accent-white/20 text-accent-white hover:bg-accent-white/10"
                      >
                        {user?.discord_user_id ? 'Disconnect' : 'Connect'}
                      </Button>
                    </div>
                  </div>

                  <div className="mt-6 p-4 bg-cheese-gold/10 rounded-lg">
                    <p className="text-sm text-accent-white">
                      <Pizza className="inline h-4 w-4 mr-1" />
                      <strong>Pro tip:</strong> Connect your services to get automatic updates and better collaboration!
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </motion.div>
      </main>
    </div>
  );
};

export default Profile; 