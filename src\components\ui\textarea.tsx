import * as React from "react"

import { cn } from "@/lib/utils"

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  autoResize?: boolean;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, autoResize = false, ...props }, ref) => {
    const textareaRef = React.useRef<HTMLTextAreaElement>(null);
    const [isAtMaxHeight, setIsAtMaxHeight] = React.useState(false);
    
    // Combine refs
    React.useImperativeHandle(ref, () => textareaRef.current!);

    const adjustHeight = React.useCallback(() => {
      const textarea = textareaRef.current;
      if (!textarea || !autoResize) return;

      // Reset height to measure scrollHeight accurately
      textarea.style.height = 'auto';
      
      // Set height to scrollHeight, with min and max constraints
      const minHeight = 80; // min-h-[80px] equivalent
      const maxHeight = 400; // Reasonable max to prevent excessive growth
      const newHeight = Math.min(Math.max(textarea.scrollHeight, minHeight), maxHeight);
      
      // Check if we've reached max height and content overflows
      const isAtMax = textarea.scrollHeight > maxHeight;
      setIsAtMaxHeight(isAtMax);
      
      textarea.style.height = `${newHeight}px`;
    }, [autoResize]);

    React.useEffect(() => {
      if (autoResize) {
        adjustHeight();
      }
    }, [props.value, autoResize, adjustHeight]);

    const handleInput = (e: React.FormEvent<HTMLTextAreaElement>) => {
      if (autoResize) {
        adjustHeight();
      }
      if (props.onInput) {
        props.onInput(e);
      }
    };

    return (
      <textarea
        ref={textareaRef}
        className={cn(
          "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          // Mobile-specific improvements
          "md:text-sm text-base placeholder:text-xs md:placeholder:text-sm", // Smaller placeholder on mobile
          autoResize && "resize-none", // Disable manual resize when auto-resizing
          // Enable scrolling when at max height, hide overflow otherwise
          autoResize && isAtMaxHeight ? "overflow-y-auto" : autoResize ? "overflow-hidden" : "",
          className
        )}
        onInput={handleInput}
        {...props}
      />
    )
  }
)
Textarea.displayName = "Textarea"

export { Textarea }
