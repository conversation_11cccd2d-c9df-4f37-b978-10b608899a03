# Environment Variables for Production Deployment
# Copy this to .env.local for development or set in Vercel dashboard for production

# Supabase Configuration
VITE_SUPABASE_URL=https://xmqexzoockycregsmyur.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhtcWV4em9vY2t5Y3JlZ3NteXVyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyMDUxMzYsImV4cCI6MjA2NTc4MTEzNn0.O6XyGxYvWHxXBPOnfUZEqp88Xq1qkOTHJy6RzZ_rOh0
VITE_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhtcWV4em9vY2t5Y3JlZ3NteXVyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDIwNTEzNiwiZXhwIjoyMDY1NzgxMTM2fQ.E9EwcsEymEXKEJ8IXbLN0NCEWU1XGUPU2Pd1I3QDME0

# Admin Authentication
VITE_ADMIN_PASSWORD=BuildWithPieFi

# Application Access Code
VITE_ACCESS_CODE=PIEFI2025

# Development Environment
NODE_ENV=production

# Supabase S3 credentials
AWS_ACCESS_KEY_ID=205bd904761075b92e467c1738764356
AWS_SECRET_ACCESS_KEY=1a0e32d00c2e6591f09167ef6358d287f784af7a5a262890dd2874a9d1896f0b
SUPABASE_STORAGE_ENDPOINT=https://xmqexzoockycregsmyur.supabase.co/storage/v1/s3 

# Gemini AI API Key
VITE_GEMINI_API_KEY=AIzaSyDqKXaFG-ZCSHtzRfycaukfEJF87SptnOY

# Notion Integration (optional)
VITE_NOTION_API_KEY=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

# Discord Webhook (optional)
VITE_DISCORD_WEBHOOK_ID=your-webhook-id
VITE_DISCORD_WEBHOOK_TOKEN=your-webhook-token 