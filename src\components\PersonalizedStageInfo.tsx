import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Target, 
  CheckCircle2, 
  AlertCircle, 
  Lightbulb,
  ArrowRight,
  User,
  Building,
  Calendar
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import devJourneyService from '@/services/devJourneyService';
import MarkdownText from './MarkdownText';

interface PersonalizedStageInfoProps {
  userId: string;
  currentStage: number;
  stageConfidence: number;
}

const PersonalizedStageInfo: React.FC<PersonalizedStageInfoProps> = ({
  userId,
  currentStage,
  stageConfidence
}) => {
  const [userProfile, setUserProfile] = useState<any>(null);
  const [onboardingData, setOnboardingData] = useState<any>(null);
  const [projectInfo, setProjectInfo] = useState<any>(null);
  const [recentUpdates, setRecentUpdates] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  const stage = devJourneyService.getStage(currentStage);

  useEffect(() => {
    fetchUserData();
  }, [userId, currentStage]);

  const fetchUserData = async () => {
    try {
      setLoading(true);

      // Fetch comprehensive user profile
      const { data: profile } = await supabase
        .from('comprehensive_user_profiles')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      // Fetch onboarding responses
      const { data: onboarding } = await supabase
        .from('onboarding_responses')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      // Fetch recent daily updates
      const { data: updates } = await supabase
        .from('daily_updates')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(5);

      setUserProfile(profile);
      setOnboardingData(onboarding);
      setRecentUpdates(updates || []);

      // Extract project info from profile or onboarding
      if (profile) {
        setProjectInfo({
          name: profile.project_name || onboarding?.project_name,
          description: profile.project_description || onboarding?.project_description,
          stage: profile.current_stage || onboarding?.product_stage,
          target_users: profile.target_users || onboarding?.target_users,
          problem_statement: profile.problem_statement || onboarding?.problem_statement
        });
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPersonalizedCharacteristics = () => {
    if (!projectInfo || !onboardingData) return stage?.characteristics || [];

    const baseCharacteristics = stage?.characteristics || [];
    const personalized = [];

    // Add personalized characteristics based on user's actual situation
    if (projectInfo.name) {
      personalized.push(`Working on "${projectInfo.name}" - your ${projectInfo.stage || 'early-stage'} project`);
    }

    if (projectInfo.problem_statement) {
      personalized.push(`Solving: ${projectInfo.problem_statement.substring(0, 80)}...`);
    }

    if (projectInfo.target_users) {
      personalized.push(`Target users: ${projectInfo.target_users.substring(0, 60)}...`);
    }

    // Add recent progress indicators
    if (recentUpdates.length > 0) {
      const recentProgress = recentUpdates[0];
      if (recentProgress.ai_insights) {
        const insights = typeof recentProgress.ai_insights === 'string' 
          ? recentProgress.ai_insights 
          : recentProgress.ai_insights.summary || '';
        personalized.push(`Recent progress: ${insights.substring(0, 80)}...`);
      }
    }

    return personalized.length > 0 ? personalized : baseCharacteristics;
  };

  const getPersonalizedSupport = () => {
    if (!projectInfo || !onboardingData) return stage?.supportNeeded || [];

    const personalized = [];

    // Customize support based on current stage and user's project
    switch (currentStage) {
      case 0: // Spark
        if (!projectInfo.problem_statement) {
          personalized.push('🎯 Define your core problem statement clearly');
        }
        if (!projectInfo.target_users) {
          personalized.push('👥 Identify your specific target users');
        }
        personalized.push('🔍 Validate your idea with potential users');
        personalized.push('📝 Create a compelling project narrative');
        break;

      case 1: // Formation
        if (onboardingData.team_size === 'solo') {
          personalized.push('🤝 Consider finding a co-founder or key team member');
        }
        personalized.push('📋 Create a detailed development roadmap');
        personalized.push('💬 Set up user feedback collection systems');
        personalized.push('🎨 Develop brand identity and messaging');
        break;

      case 2: // First Build
        personalized.push('⚡ Build your MVP focusing on core functionality');
        personalized.push('🧪 Set up testing and validation processes');
        personalized.push('📊 Implement basic analytics');
        break;

      default:
        return stage?.supportNeeded || [];
    }

    return personalized;
  };

  const getRecentProgressHighlights = () => {
    if (recentUpdates.length === 0) return null;

    const recent = recentUpdates[0];
    const achievements = recent.ai_insights?.achievements || [];
    const nextActions = recent.ai_insights?.nextActions || [];

    return {
      lastUpdate: recent.created_at,
      achievements: achievements.slice(0, 3),
      nextActions: nextActions.slice(0, 3),
      sentiment: recent.ai_insights?.sentiment || 0.5
    };
  };

  if (loading) {
    return (
      <Card className="bg-gradient-to-br from-accent-white/10 to-accent-white/5 backdrop-blur-xl border border-accent-white/20">
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-accent-white/20 rounded w-3/4" />
            <div className="space-y-2">
              <div className="h-4 bg-accent-white/10 rounded" />
              <div className="h-4 bg-accent-white/10 rounded w-5/6" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const personalizedCharacteristics = getPersonalizedCharacteristics();
  const personalizedSupport = getPersonalizedSupport();
  const progressHighlights = getRecentProgressHighlights();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      {/* Project Overview */}
      {projectInfo && (
        <Card className="bg-gradient-to-br from-accent-white/10 to-accent-white/5 backdrop-blur-xl border border-accent-white/20 shadow-2xl rounded-2xl">
          <CardHeader>
            <CardTitle className="text-xl text-accent-white flex items-center gap-3">
              <Building className="h-6 w-6 text-cheese-gold" />
              Your Project: {projectInfo.name || 'Untitled Project'}
              <Badge className="ml-auto bg-sauce-red/20 text-sauce-red border-sauce-red/30">
                {stage?.name} Stage
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {projectInfo.description && (
              <div>
                <h4 className="text-accent-white/80 font-medium mb-2">Description:</h4>
                <MarkdownText className="text-accent-white/70 text-sm leading-relaxed">
                  {projectInfo.description}
                </MarkdownText>
              </div>
            )}

            {projectInfo.problem_statement && (
              <div>
                <h4 className="text-accent-white/80 font-medium mb-2">Problem You're Solving:</h4>
                <MarkdownText className="text-accent-white/70 text-sm leading-relaxed">
                  {projectInfo.problem_statement}
                </MarkdownText>
              </div>
            )}

            {projectInfo.target_users && (
              <div>
                <h4 className="text-accent-white/80 font-medium mb-2">Target Users:</h4>
                <MarkdownText className="text-accent-white/70 text-sm leading-relaxed">
                  {projectInfo.target_users}
                </MarkdownText>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Current Stage Progress */}
      <Card className="bg-gradient-to-br from-accent-white/10 to-accent-white/5 backdrop-blur-xl border border-accent-white/20 shadow-2xl rounded-2xl">
        <CardHeader>
          <CardTitle className="text-xl text-accent-white flex items-center gap-3">
            <Target className="h-6 w-6 text-cheese-gold" />
            Your {stage?.name} Stage Status
            <Badge 
              className={`ml-auto ${
                stageConfidence >= 80 ? 'bg-green-500/20 text-green-400 border-green-500/30' :
                stageConfidence >= 60 ? 'bg-cheese-gold/20 text-cheese-gold border-cheese-gold/30' :
                'bg-orange-500/20 text-orange-400 border-orange-500/30'
              }`}
            >
              {stageConfidence}% Confidence
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Current Situation */}
          <div>
            <h4 className="text-accent-white font-semibold mb-3 flex items-center gap-2">
              <User className="h-4 w-4 text-cheese-gold" />
              Your Current Situation:
            </h4>
            <div className="space-y-2">
              {personalizedCharacteristics.map((char, idx) => (
                <motion.div
                  key={idx}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: idx * 0.1 }}
                  className="flex items-start gap-3 p-3 rounded-lg bg-accent-white/5 border border-accent-white/10"
                >
                  <CheckCircle2 className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                  <MarkdownText className="text-accent-white/80 text-sm">
                    {char}
                  </MarkdownText>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Personalized Next Steps */}
          <div>
            <h4 className="text-accent-white font-semibold mb-3 flex items-center gap-2">
              <ArrowRight className="h-4 w-4 text-cheese-gold" />
              Your Next Steps:
            </h4>
            <div className="space-y-2">
              {personalizedSupport.map((support, idx) => (
                <motion.div
                  key={idx}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: idx * 0.1 + 0.3 }}
                  className="flex items-start gap-3 p-3 rounded-lg bg-gradient-to-r from-sauce-red/5 to-cheese-gold/5 border border-sauce-red/20 hover:border-cheese-gold/30 transition-colors"
                >
                  <AlertCircle className="h-4 w-4 text-sauce-red mt-0.5 flex-shrink-0" />
                  <MarkdownText className="text-accent-white/80 text-sm">
                    {support}
                  </MarkdownText>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Recent Progress */}
          {progressHighlights && (
            <div>
              <h4 className="text-accent-white font-semibold mb-3 flex items-center gap-2">
                <Calendar className="h-4 w-4 text-cheese-gold" />
                Recent Progress:
              </h4>
              <div className="space-y-3">
                {progressHighlights.achievements.length > 0 && (
                  <div>
                    <p className="text-accent-white/70 text-sm mb-2">Recent Achievements:</p>
                    {progressHighlights.achievements.map((achievement: string, idx: number) => (
                      <div key={idx} className="flex items-start gap-2 text-green-400 text-sm">
                        <CheckCircle2 className="h-3 w-3 mt-0.5 flex-shrink-0" />
                        <MarkdownText className="text-green-300">
                          {achievement}
                        </MarkdownText>
                      </div>
                    ))}
                  </div>
                )}

                {progressHighlights.nextActions.length > 0 && (
                  <div>
                    <p className="text-accent-white/70 text-sm mb-2">AI Recommended Actions:</p>
                    {progressHighlights.nextActions.map((action: string, idx: number) => (
                      <div key={idx} className="flex items-start gap-2 text-cheese-gold text-sm">
                        <Lightbulb className="h-3 w-3 mt-0.5 flex-shrink-0" />
                        <MarkdownText className="text-accent-white/80">
                          {action}
                        </MarkdownText>
                      </div>
                    ))}
                  </div>
                )}

                <p className="text-accent-white/60 text-xs">
                  Last updated: {new Date(progressHighlights.lastUpdate).toLocaleDateString()}
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default PersonalizedStageInfo; 