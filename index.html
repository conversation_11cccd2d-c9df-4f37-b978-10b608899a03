<!doctype html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico?v=2" />
    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico?v=2" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="theme-color" content="#181818" />
    <title>Pie Fi | Santa Cruz Startup Accelerator</title>
    <meta name="description" content="Pie Fi: Santa Cruz's next-generation student–founder accelerator and talent retention experiment." />
    <meta name="author" content="Pie Fi" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://santa-cruz-builds-accelerate.vercel.app/" />
    <meta property="og:title" content="Pie Fi: Santa Cruz Builds" />
    <meta property="og:description" content="Santa Cruz's next-generation student–founder accelerator and talent retention experiment." />
    <meta property="og:image" content="/piefi.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@piefi_accelerator" />
    <meta property="twitter:title" content="Pie Fi: Santa Cruz Builds" />
    <meta property="twitter:description" content="Santa Cruz's next-generation student–founder accelerator and talent retention experiment." />
    <meta property="twitter:image" content="/piefi.png" />
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
      /* Prevent FOUC and ensure smooth loading */
      html {
        background-color: #181818;
        overflow-x: hidden;
      }
      
      /* Loading state optimization */
      #root {
        min-height: 100vh;
        background-color: #181818;
      }
      
      /* Prevent mobile bounce scrolling issues */
      body {
        position: fixed;
        overflow: hidden;
        width: 100%;
        height: 100%;
      }
      
      /* Reset body position after page load */
      body.loaded {
        position: static;
        overflow: visible;
        height: auto;
      }
      
      /* Mobile-specific optimizations */
      @media (max-width: 768px) {
        * {
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          text-rendering: optimizeSpeed;
        }
        
        /* Reduce motion for better performance */
        *, *::before, *::after {
          animation-duration: 0.3s !important;
          transition-duration: 0.3s !important;
        }
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <script>
      // Remove loading restrictions once page is loaded
      window.addEventListener('load', function() {
        document.body.classList.add('loaded');
      });
      
      // Prevent zoom on double tap for better UX
      let lastTouchEnd = 0;
      document.addEventListener('touchend', function (event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      }, false);
    </script>
  </body>
</html>
