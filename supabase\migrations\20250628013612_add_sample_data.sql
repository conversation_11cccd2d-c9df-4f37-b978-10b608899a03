-- Insert sample mentors
INSERT INTO pie_fi_mentors (name, expertise, bio, availability, email, discord_handle) VALUES
  ('<PERSON>', ARRAY['user research', 'validation', 'customer discovery', 'design thinking'], 'Expert in user-centered design with 8+ years at Google. Helped launch 3 successful B2C products reaching 10M+ users.', 'Wednesdays 2-4pm PST', '<EMAIL>', 'sarahchen#1234'),
  ('<PERSON>', ARRAY['technical', 'architecture', 'scaling', 'backend', 'databases'], 'Full-stack engineer turned CTO. Built systems handling 100M+ requests/day. Previously at Stripe and Airbnb.', 'Fridays 1-3pm PST', '<EMAIL>', 'alexwang#5678'),
  ('<PERSON>', ARRAY['product management', 'go-to-market', 'growth', 'analytics'], 'Former PM at Notion. Led product growth from 1M to 20M users. Expert in PLG strategies.', 'Mondays 10am-12pm PST', '<EMAIL>', 'mariarodriguez#9012'),
  ('<PERSON>', ARRAY['fundraising', 'business strategy', 'legal', 'operations'], 'Serial entrepreneur with 2 exits. Partner at early-stage VC fund. Expert in seed to Series A fundraising.', 'Thursdays 3-5pm PST', '<EMAIL>', 'davidkim#3456'),
  ('Priya Patel', ARRAY['marketing', 'content', 'social media', 'community'], 'Built 100K+ follower communities. Former Head of Marketing at fast-growing B2B SaaS (0-$10M ARR).', 'Tuesdays 11am-1pm PST', '<EMAIL>', 'priyapatel#7890');

-- Insert sample resources
INSERT INTO pie_fi_resources (title, description, resource_type, url, tags, track_relevance) VALUES
  ('Customer Interview Template', 'A comprehensive 5-step guide to conducting effective user interviews with script templates and follow-up questions.', 'template', '/resources/customer-interview-template', ARRAY['validation', 'research', 'interviews', 'customer discovery'], ARRAY['newbie', 'builder']),
  ('MVP Development Guide', 'Step-by-step framework for building your first version fast. Includes tech stack recommendations and timeline.', 'guide', '/resources/mvp-guide', ARRAY['building', 'technical', 'mvp', 'development'], ARRAY['builder']),
  ('Startup Pitch Deck Template', 'Proven pitch deck template used by 50+ successful startups. Includes investor feedback examples.', 'template', '/resources/pitch-deck-template', ARRAY['fundraising', 'pitch', 'investors'], ARRAY['builder', 'scaler']),
  ('Growth Marketing Playbook', 'Complete guide to growth loops, viral mechanics, and scaling user acquisition from 0 to 1M users.', 'guide', '/resources/growth-playbook', ARRAY['growth', 'marketing', 'acquisition', 'scaling'], ARRAY['scaler']),
  ('User Research Toolkit', 'Collection of surveys, interview scripts, and analysis frameworks for understanding your users deeply.', 'tool', '/resources/user-research-toolkit', ARRAY['research', 'users', 'feedback', 'validation'], ARRAY['newbie', 'builder']),
  ('Financial Model Template', 'Comprehensive Excel/Sheets model for SaaS, marketplace, and e-commerce businesses with scenario planning.', 'template', '/resources/financial-model', ARRAY['finance', 'planning', 'fundraising'], ARRAY['builder', 'scaler']),
  ('Product Launch Checklist', '50-point checklist covering everything from pre-launch to post-launch metrics and iteration.', 'guide', '/resources/launch-checklist', ARRAY['launch', 'product', 'go-to-market'], ARRAY['builder', 'scaler']),
  ('No-Code App Builder Course', '4-hour video course on building apps without coding using tools like Bubble, Webflow, and Airtable.', 'course', '/resources/no-code-course', ARRAY['no-code', 'building', 'prototyping'], ARRAY['newbie', 'builder']),
  ('Founder Mental Health Guide', 'Evidence-based strategies for managing stress, avoiding burnout, and maintaining peak performance.', 'guide', '/resources/mental-health-guide', ARRAY['wellness', 'productivity', 'mental health'], ARRAY['newbie', 'builder', 'scaler']),
  ('Notion Project Management Template', 'Complete workspace template for managing your startup with kanban boards, OKRs, and team collaboration.', 'template', '/resources/notion-template', ARRAY['productivity', 'organization', 'project management'], ARRAY['newbie', 'builder', 'scaler']);

-- Insert sample track-specific goals
INSERT INTO pie_fi_resources (title, description, resource_type, url, tags, track_relevance) VALUES
  ('Problem Validation Framework', 'Systematic approach to validating whether your identified problem is real and worth solving.', 'guide', '/resources/problem-validation', ARRAY['validation', 'problem-solving', 'research'], ARRAY['newbie']),
  ('Technical Architecture Patterns', 'Common scalable architecture patterns for web applications, APIs, and databases.', 'guide', '/resources/tech-architecture', ARRAY['technical', 'architecture', 'scalability'], ARRAY['builder', 'scaler']),
  ('Series A Fundraising Guide', 'Complete guide to raising Series A including deck preparation, investor outreach, and term negotiation.', 'guide', '/resources/series-a-guide', ARRAY['fundraising', 'series-a', 'investors'], ARRAY['scaler']);

-- Create some sample AI conversation starters for each track
INSERT INTO ai_conversations (user_id, conversation_type, messages, context) VALUES
  ('00000000-0000-0000-0000-000000000000', 'onboarding', 
   '[{"role": "assistant", "content": "Welcome to Pie Fi! I''m excited to help you on your entrepreneurial journey. Let''s start by understanding what brings you here today."}, {"role": "user", "content": "I have this idea for a productivity app but I''m not sure if it''s any good."}, {"role": "assistant", "content": "That''s a great starting point! Productivity is a huge space with lots of opportunities. What specific productivity problem are you trying to solve? Tell me about a time when you felt really unproductive and wished there was a better solution."}]',
   '{"track": "newbie", "stage": "ideation", "focus_areas": ["problem_validation", "user_research"]}'
  );

-- Note: The user_id above is a placeholder. In practice, this would be populated when real users go through onboarding. 