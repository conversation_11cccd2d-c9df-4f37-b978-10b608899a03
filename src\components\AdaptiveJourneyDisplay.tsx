import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  CheckCircle,
  Circle,
  ArrowRight,
  Target,
  TrendingUp,
  AlertTriangle,
  Lightbulb,
  ChevronDown,
  ChevronUp,
  Star,
  Zap,
  Calendar,
  MessageSquare,
  Send,
  FileText,
  Clock,
  Award,
  Brain,
  User,
  Sparkles
} from 'lucide-react';
import { AdaptiveJourneyState, StageAnalysis } from '@/services/adaptiveJourneyService';
import devJourneyService from '@/services/devJourneyService';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import MarkdownText from './MarkdownText';
import milestoneSubmissionService from '@/services/milestoneSubmissionService';
import MilestoneCoachingModal from './MilestoneCoachingModal';

interface MilestoneSubmission {
  id: string;
  milestone_id: string;
  stage_id: number;
  submission_content: string;
  submission_date: string;
  is_approved: boolean;
  ai_feedback?: string;
  approval_date?: string;
}

interface UserResponse {
  id: string;
  stage_id: number;
  response_type: string;
  response_content: string;
  created_at: string;
}

interface StageContextData {
  milestones: MilestoneSubmission[];
  responses: UserResponse[];
  personalizedGuidance?: string;
  nextSteps?: string[];
}

interface AdaptiveJourneyDisplayProps {
  journeyState: AdaptiveJourneyState;
  userId: string;
  onStageUpdate?: () => void;
}

const AdaptiveJourneyDisplay: React.FC<AdaptiveJourneyDisplayProps> = ({
  journeyState,
  userId,
  onStageUpdate
}) => {
  const [expandedStages, setExpandedStages] = useState<Set<number>>(
    new Set([journeyState.current_effective_stage])
  );
  const [stageContextData, setStageContextData] = useState<Record<number, StageContextData>>({});
  const [loading, setLoading] = useState(false);
  const [newMilestoneContent, setNewMilestoneContent] = useState<Record<string, string>>({});
  const [submittingMilestone, setSubmittingMilestone] = useState<string | null>(null);
  const [coachingModal, setCoachingModal] = useState<{
    isOpen: boolean;
    milestone: any;
    followUpQuestions: string[];
  }>({
    isOpen: false,
    milestone: null,
    followUpQuestions: []
  });

  // Fetch contextual data for a stage when expanded
  const fetchStageContext = async (stageId: number) => {
    if (stageContextData[stageId]) return; // Already loaded

    setLoading(true);
    try {
      // Fetch milestone submissions for this stage
      const { data: milestones, error: milestonesError } = await supabase
        .from('milestone_submissions')
        .select('*')
        .eq('user_id', userId)
        .eq('stage_id', stageId)
        .order('submission_date', { ascending: false });

      if (milestonesError) throw milestonesError;

      // Note: user_stage_responses table doesn't exist, using empty array
      const userResponses: UserResponse[] = [];

      setStageContextData(prev => ({
        ...prev,
        [stageId]: {
          milestones: milestones || [],
          responses: userResponses,
        }
      }));
    } catch (error) {
      console.error('Error fetching stage context:', error);
      toast.error('Failed to load stage details');
    } finally {
      setLoading(false);
    }
  };

  const toggleStageExpansion = async (stageId: number) => {
    const newExpanded = new Set(expandedStages);
    if (newExpanded.has(stageId)) {
      newExpanded.delete(stageId);
    } else {
      newExpanded.add(stageId);
      // Fetch context data when expanding
      await fetchStageContext(stageId);
    }
    setExpandedStages(newExpanded);
  };

  const getStageStatus = (analysis: StageAnalysis) => {
    if (analysis.is_stage_complete) return 'complete';
    if (analysis.stage_id === journeyState.current_effective_stage) return 'current';
    if (analysis.completion_percentage > 0) return 'in-progress';
    return 'not-started';
  };

  const getStageIcon = (status: string, stageId: number) => {
    switch (status) {
      case 'complete':
        return <CheckCircle className="h-6 w-6 text-green-400" />;
      case 'current':
        return <Target className="h-6 w-6 text-blue-400" />;
      case 'in-progress':
        return <Circle className="h-6 w-6 text-yellow-400" />;
      default:
        return <Circle className="h-6 w-6 text-gray-400" />;
    }
  };

  const getStageColor = (status: string) => {
    switch (status) {
      case 'complete':
        return 'border-green-400/30 bg-green-500/10';
      case 'current':
        return 'border-blue-400/30 bg-blue-500/10';
      case 'in-progress':
        return 'border-yellow-400/30 bg-yellow-500/10';
      default:
        return 'border-gray-400/30 bg-gray-500/10';
    }
  };

  // Handle milestone submission
  const handleMilestoneSubmission = async (stageId: number, milestoneId: string) => {
    const content = newMilestoneContent[`${stageId}-${milestoneId}`];

    setSubmittingMilestone(`${stageId}-${milestoneId}`);

    try {
      const result = await milestoneSubmissionService.submitMilestone({
        userId,
        stageId,
        milestoneId,
        content,
        stageTitle: `Stage ${stageId}`,
        milestoneTitle: milestoneId,
        milestoneDescription: `Stage ${stageId} milestone: ${milestoneId}`,
        projectContext: ''
      });

      if (result.success) {
        // Check if AI analysis generated follow-up questions
        const hasFollowUpQuestions = result.aiAnalysis?.followUpQuestions?.length > 0;

        if (hasFollowUpQuestions) {
          // Open coaching modal for interactive session
          setCoachingModal({
            isOpen: true,
            milestone: {
              id: result.submissionId,
              milestone_id: milestoneId,
              stage_id: stageId,
              submission_content: content,
              ai_feedback: result.aiAnalysis.consolidatedFeedback || result.aiAnalysis.insights || result.aiAnalysis.coachingInsight
            },
            followUpQuestions: result.aiAnalysis.followUpQuestions
          });

          toast.success('Milestone submitted! Let\'s dive deeper with some follow-up questions.');
        } else {
          // Show detailed success message with AI analysis info
          if (result.aiAnalysis) {
            toast.success('Milestone submitted and analyzed successfully! Check your AI feedback below.');
          } else {
            toast.success('Milestone submitted successfully! AI analysis in progress...');
          }
        }

        setNewMilestoneContent(prev => ({
          ...prev,
          [`${stageId}-${milestoneId}`]: ''
        }));

        // Refresh stage context
        delete stageContextData[stageId];
        await fetchStageContext(stageId);

        // Trigger journey update
        onStageUpdate?.();
      } else {
        toast.error(result.error || 'Failed to submit milestone');
      }
    } catch (error) {
      console.error('Unexpected error submitting milestone:', error);
      toast.error('An unexpected error occurred. Please try again.');
    } finally {
      setSubmittingMilestone(null);
    }
  };

  // Load context for current stage on mount
  useEffect(() => {
    fetchStageContext(journeyState.current_effective_stage);
  }, [journeyState.current_effective_stage, userId]);

  return (
    <div className="space-y-6">
      {/* Journey Overview */}
      <Card className="bg-gradient-to-r from-purple-500/20 to-blue-500/20 border-purple-400/30">
        <CardHeader>
          <CardTitle className="text-accent-white flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Your Adaptive Journey
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-lg font-semibold text-accent-white">
                Currently in Stage {journeyState.current_effective_stage}
              </p>
              <p className="text-sm text-accent-white/70">
                {journeyState.reasoning}
              </p>
            </div>
            <Badge className="bg-blue-500/20 text-blue-400 text-lg px-3 py-1">
              {journeyState.confidence_score}% Confidence
            </Badge>
          </div>

          {/* Journey Summary */}
          <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg p-4 border border-blue-400/20">
            <h4 className="text-sm font-medium text-accent-white mb-3 flex items-center gap-2">
              <User className="h-4 w-4" />
              Your Journey Story
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <p className="text-2xl font-bold text-green-400">
                  {journeyState.completed_stages.length}
                </p>
                <p className="text-accent-white/70">Stages Completed</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-400">
                  {journeyState.stage_analyses.reduce((sum, stage) =>
                    sum + stage.completed_milestones.length, 0
                  )}
                </p>
                <p className="text-accent-white/70">Total Milestones</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-400">
                  {Math.round(journeyState.stage_analyses.reduce((sum, stage) =>
                    sum + stage.completion_percentage, 0) / journeyState.stage_analyses.length
                  )}%
                </p>
                <p className="text-accent-white/70">Overall Progress</p>
              </div>
            </div>
          </div>

          {/* Cross-stage insights */}
          {journeyState.cross_stage_insights.length > 0 && (
            <div className="bg-accent-white/10 rounded-lg p-3">
              <h4 className="text-sm font-medium text-accent-white mb-2 flex items-center gap-2">
                <Lightbulb className="h-4 w-4" />
                Journey Insights
              </h4>
              {journeyState.cross_stage_insights.map((insight, index) => (
                <p key={index} className="text-sm text-accent-white/80 mb-1">
                  • {insight}
                </p>
              ))}
            </div>
          )}

          {/* Quick Actions */}
          {journeyState.next_recommended_actions.length > 0 && (
            <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg p-4 border border-green-400/20">
              <h4 className="text-sm font-medium text-accent-white mb-3 flex items-center gap-2">
                <Zap className="h-4 w-4 text-yellow-400" />
                Quick Actions for You
              </h4>
              <div className="space-y-2">
                {journeyState.next_recommended_actions.map((action, index) => (
                  <div key={index} className="flex items-start gap-3 p-2 rounded bg-accent-white/5">
                    <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center mt-0.5">
                      <span className="text-xs font-bold text-blue-400">{index + 1}</span>
                    </div>
                    <p className="text-sm text-accent-white/80 leading-relaxed">
                      {action}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Gaps to address */}
          {journeyState.gaps_to_address.length > 0 && (
            <div className="bg-orange-500/10 rounded-lg p-3">
              <h4 className="text-sm font-medium text-orange-400 mb-2 flex items-center gap-2">
                <AlertTriangle className="h-4 w-4" />
                Areas to Strengthen
              </h4>
              {journeyState.gaps_to_address.map((gap, index) => (
                <p key={index} className="text-sm text-accent-white/80 mb-1">
                  • {gap}
                </p>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Stage-by-Stage Analysis */}
      <div className="space-y-4">
        <h3 className="text-xl font-bold text-accent-white">Stage Progress Analysis</h3>
        
        {journeyState.stage_analyses.map((analysis, index) => {
          const status = getStageStatus(analysis);
          const isExpanded = expandedStages.has(analysis.stage_id);
          const stageInfo = devJourneyService.getStage(analysis.stage_id);
          
          return (
            <motion.div
              key={analysis.stage_id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className={`${getStageColor(status)} transition-all duration-300`}>
                <CardHeader 
                  className="cursor-pointer"
                  onClick={() => toggleStageExpansion(analysis.stage_id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getStageIcon(status, analysis.stage_id)}
                      <div>
                        <CardTitle className="text-accent-white">
                          Stage {analysis.stage_id}: {analysis.stage_title}
                        </CardTitle>
                        <p className="text-sm text-accent-white/70">
                          {analysis.completion_percentage}% Complete • 
                          {analysis.completed_milestones.length} milestones done
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge 
                        className={`${
                          status === 'complete' ? 'bg-green-500/20 text-green-400' :
                          status === 'current' ? 'bg-blue-500/20 text-blue-400' :
                          status === 'in-progress' ? 'bg-yellow-500/20 text-yellow-400' :
                          'bg-gray-500/20 text-gray-400'
                        }`}
                      >
                        {status === 'complete' ? 'Complete' :
                         status === 'current' ? 'Current Focus' :
                         status === 'in-progress' ? 'In Progress' :
                         'Not Started'}
                      </Badge>
                      {isExpanded ? 
                        <ChevronUp className="h-5 w-5 text-accent-white/60" /> :
                        <ChevronDown className="h-5 w-5 text-accent-white/60" />
                      }
                    </div>
                  </div>
                </CardHeader>

                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <CardContent className="pt-0">
                        {loading && !stageContextData[analysis.stage_id] ? (
                          <div className="text-center py-8">
                            <Sparkles className="h-8 w-8 animate-spin text-accent-white/60 mx-auto mb-3" />
                            <p className="text-accent-white/60">Loading stage details...</p>
                          </div>
                        ) : (
                        <div className="space-y-6">
                          {/* Adaptive Analysis Overview */}
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <div className="flex justify-between text-sm text-accent-white/70 mb-2">
                                <span>Completion Progress</span>
                                <span>{analysis.completion_percentage}%</span>
                              </div>
                              <Progress value={analysis.completion_percentage} className="h-2" />
                            </div>
                            <div>
                              <div className="flex justify-between text-sm text-accent-white/70 mb-2">
                                <span>Readiness Score</span>
                                <span>{analysis.readiness_score}%</span>
                              </div>
                              <Progress value={analysis.readiness_score} className="h-2" />
                            </div>
                          </div>

                          {/* User's Milestone Achievements */}
                          {stageContextData[analysis.stage_id]?.milestones &&
                           stageContextData[analysis.stage_id].milestones.length > 0 && (
                            <div>
                              <h5 className="text-lg font-semibold text-accent-white mb-4 flex items-center gap-2">
                                <Award className="h-5 w-5 text-yellow-400" />
                                Your Milestone Achievements
                              </h5>
                              <div className="space-y-4">
                                {stageContextData[analysis.stage_id].milestones.map((milestone) => (
                                  <div
                                    key={milestone.id}
                                    className={`p-4 rounded-lg border ${
                                      milestone.is_approved
                                        ? 'bg-green-500/10 border-green-400/30'
                                        : 'bg-yellow-500/10 border-yellow-400/30'
                                    }`}
                                  >
                                    <div className="flex items-start justify-between mb-3">
                                      <div className="flex items-center gap-2">
                                        {milestone.is_approved ? (
                                          <CheckCircle className="h-5 w-5 text-green-400" />
                                        ) : (
                                          <Clock className="h-5 w-5 text-yellow-400" />
                                        )}
                                        <h6 className="font-medium text-accent-white">
                                          {milestone.milestone_id.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                        </h6>
                                      </div>
                                      <div className="text-right">
                                        <Badge className={`text-xs ${
                                          milestone.is_approved
                                            ? 'bg-green-500/20 text-green-400'
                                            : 'bg-yellow-500/20 text-yellow-400'
                                        }`}>
                                          {milestone.is_approved ? 'Approved' : 'Under Review'}
                                        </Badge>
                                        <p className="text-xs text-accent-white/60 mt-1">
                                          {formatDistanceToNow(new Date(milestone.submission_date), { addSuffix: true })}
                                        </p>
                                      </div>
                                    </div>

                                    <div className="bg-accent-white/5 rounded-lg p-3 mb-3">
                                      <p className="text-sm text-accent-white/80 leading-relaxed">
                                        <MarkdownText content={milestone.submission_content} />
                                      </p>
                                    </div>

                                    {/* AI Feedback */}
                                    {milestone.ai_feedback && (
                                      <div className="bg-purple-500/10 border border-purple-400/30 rounded-lg p-3">
                                        <div className="flex items-center gap-2 mb-2">
                                          <Brain className="h-4 w-4 text-purple-400" />
                                          <span className="text-sm font-medium text-purple-400">AI Analysis</span>
                                        </div>
                                        <p className="text-sm text-accent-white/80">
                                          <MarkdownText content={milestone.ai_feedback} />
                                        </p>
                                      </div>
                                    )}

                                    {/* Follow-up Questions - Now handled by coaching modal */}
                                    {(() => {
                                      try {
                                        const insights = milestone.ai_insights ? JSON.parse(milestone.ai_insights) : null;
                                        const followUpQuestions = insights?.followUpQuestions || [];

                                        if (followUpQuestions.length > 0) {
                                          return (
                                            <div className="bg-yellow-500/10 border border-yellow-400/30 rounded-lg p-3 mt-3">
                                              <div className="flex items-center gap-2 mb-3">
                                                <Sparkles className="h-4 w-4 text-yellow-400" />
                                                <span className="text-sm font-medium text-yellow-400">AI Coaching Available</span>
                                              </div>
                                              <p className="text-sm text-accent-white/80 mb-3">
                                                I have some follow-up questions to help deepen your thinking about this milestone.
                                              </p>
                                              <Button
                                                size="sm"
                                                onClick={() => {
                                                  setCoachingModal({
                                                    isOpen: true,
                                                    milestone: {
                                                      id: milestone.id,
                                                      milestone_id: milestone.milestone_id,
                                                      stage_id: analysis.stage_id,
                                                      submission_content: milestone.submission_content,
                                                      ai_feedback: milestone.ai_feedback
                                                    },
                                                    followUpQuestions
                                                  });
                                                }}
                                                className="bg-yellow-500/20 border border-yellow-400/30 text-yellow-400 hover:bg-yellow-500/30"
                                              >
                                                <Brain className="h-4 w-4 mr-2" />
                                                Start Coaching Session
                                              </Button>
                                            </div>
                                          );
                                        }
                                      } catch (e) {
                                        // If parsing fails, don't show anything
                                      }
                                      return null;
                                    })()}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Available Milestones for Submission */}
                          {analysis.missing_milestones.length > 0 && (
                            <div>
                              <h5 className="text-lg font-semibold text-accent-white mb-4 flex items-center gap-2">
                                <Target className="h-5 w-5 text-blue-400" />
                                Available Milestones
                              </h5>
                              <div className="space-y-4">
                                {analysis.missing_milestones.slice(0, 3).map(milestoneId => (
                                  <div
                                    key={milestoneId}
                                    className="p-4 rounded-lg border border-blue-400/30 bg-blue-500/10"
                                  >
                                    <div className="flex items-center justify-between mb-3">
                                      <h6 className="font-medium text-accent-white">
                                        {milestoneId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                      </h6>
                                      <Badge className="bg-blue-500/20 text-blue-400 text-xs">
                                        Ready to Submit
                                      </Badge>
                                    </div>

                                    <div className="space-y-3">
                                      <Textarea
                                        placeholder={`Describe your progress on ${milestoneId.replace(/_/g, ' ')}...`}
                                        value={newMilestoneContent[`${analysis.stage_id}-${milestoneId}`] || ''}
                                        onChange={(e) => setNewMilestoneContent(prev => ({
                                          ...prev,
                                          [`${analysis.stage_id}-${milestoneId}`]: e.target.value
                                        }))}
                                        className="min-h-[100px] bg-accent-white/5 border-accent-white/20 text-accent-white"
                                      />
                                      <Button
                                        onClick={() => handleMilestoneSubmission(analysis.stage_id, milestoneId)}
                                        disabled={submittingMilestone === `${analysis.stage_id}-${milestoneId}` ||
                                                 !newMilestoneContent[`${analysis.stage_id}-${milestoneId}`]?.trim()}
                                        className="w-full"
                                        size="sm"
                                      >
                                        {submittingMilestone === `${analysis.stage_id}-${milestoneId}` ? (
                                          <>
                                            <Sparkles className="h-4 w-4 mr-2 animate-spin" />
                                            Submitting...
                                          </>
                                        ) : (
                                          <>
                                            <Send className="h-4 w-4 mr-2" />
                                            Submit Milestone
                                          </>
                                        )}
                                      </Button>
                                    </div>
                                  </div>
                                ))}

                                {analysis.missing_milestones.length > 3 && (
                                  <div className="text-center py-4">
                                    <p className="text-sm text-accent-white/60">
                                      +{analysis.missing_milestones.length - 3} more milestones available
                                    </p>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Personalized Stage Guidance */}
                          {stageInfo && (
                            <div>
                              <h5 className="text-lg font-semibold text-accent-white mb-4 flex items-center gap-2">
                                <Lightbulb className="h-5 w-5 text-yellow-400" />
                                Personalized Guidance
                              </h5>

                              <div className="space-y-4">
                                {/* Stage Overview */}
                                <div className="bg-accent-white/5 rounded-lg p-4">
                                  <h6 className="font-medium text-accent-white mb-2">Stage Overview</h6>
                                  <p className="text-sm text-accent-white/80 leading-relaxed">
                                    {stageInfo.description}
                                  </p>
                                </div>

                                {/* Adaptive Insights */}
                                <div className="bg-purple-500/10 border border-purple-400/30 rounded-lg p-4">
                                  <h6 className="font-medium text-purple-400 mb-2 flex items-center gap-2">
                                    <Brain className="h-4 w-4" />
                                    Your Progress Analysis
                                  </h6>
                                  <div className="space-y-2 text-sm text-accent-white/80">
                                    <p>• <strong>Completion:</strong> {analysis.completion_percentage}% of required milestones</p>
                                    <p>• <strong>Readiness:</strong> {analysis.readiness_score}% prepared for this stage</p>
                                    {analysis.stage_id === journeyState.current_effective_stage && (
                                      <p>• <strong>Status:</strong> This is your current focus stage</p>
                                    )}
                                    {analysis.is_stage_complete && (
                                      <p>• <strong>Achievement:</strong> Stage completed! Ready to advance</p>
                                    )}
                                  </div>
                                </div>

                                {/* Stage Characteristics */}
                                {stageInfo.characteristics && stageInfo.characteristics.length > 0 && (
                                  <div className="bg-blue-500/10 border border-blue-400/30 rounded-lg p-4">
                                    <h6 className="font-medium text-blue-400 mb-2">Key Characteristics</h6>
                                    <ul className="text-sm text-accent-white/80 space-y-1">
                                      {stageInfo.characteristics.map((char, idx) => (
                                        <li key={idx}>• {char}</li>
                                      ))}
                                    </ul>
                                  </div>
                                )}

                                {/* Support Needed */}
                                {stageInfo.supportNeeded && stageInfo.supportNeeded.length > 0 && (
                                  <div className="bg-orange-500/10 border border-orange-400/30 rounded-lg p-4">
                                    <h6 className="font-medium text-orange-400 mb-2">Support You Might Need</h6>
                                    <ul className="text-sm text-accent-white/80 space-y-1">
                                      {stageInfo.supportNeeded.map((support, idx) => (
                                        <li key={idx}>• {support}</li>
                                      ))}
                                    </ul>
                                  </div>
                                )}

                                {/* Frameworks */}
                                {stageInfo.frameworks && Object.keys(stageInfo.frameworks).length > 0 && (
                                  <div className="bg-green-500/10 border border-green-400/30 rounded-lg p-4">
                                    <h6 className="font-medium text-green-400 mb-2">Recommended Frameworks</h6>
                                    <div className="space-y-2">
                                      {Object.entries(stageInfo.frameworks).map(([framework, description]) => (
                                        <div key={framework} className="text-sm">
                                          <span className="font-medium text-accent-white">{framework}:</span>
                                          <span className="text-accent-white/80 ml-2">{description}</span>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                        )}
                      </CardContent>
                    </motion.div>
                  )}
                </AnimatePresence>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Coaching Modal */}
      <MilestoneCoachingModal
        isOpen={coachingModal.isOpen}
        onClose={() => setCoachingModal(prev => ({ ...prev, isOpen: false }))}
        milestone={coachingModal.milestone}
        followUpQuestions={coachingModal.followUpQuestions}
        userId={userId}
        onConversationComplete={async () => {
          setCoachingModal(prev => ({ ...prev, isOpen: false }));
          // Refresh the stage context to show updated milestone data
          if (coachingModal.milestone?.stage_id) {
            delete stageContextData[coachingModal.milestone.stage_id];
            await fetchStageContext(coachingModal.milestone.stage_id);
          }
          onStageUpdate?.();
        }}
      />
    </div>
  );
};

export default AdaptiveJourneyDisplay;
