import { useEffect, useState, useCallback, useRef } from 'react';
import { useInView } from 'react-intersection-observer';
import { useIsMobile } from './use-mobile';

// Performance monitoring
const performanceMonitor = {
  animations: new Map<string, number>(),
  
  trackAnimation(id: string) {
    this.animations.set(id, performance.now());
  },
  
  getAnimationDuration(id: string): number {
    const startTime = this.animations.get(id);
    if (!startTime) return 0;
    return performance.now() - startTime;
  },
  
  clearAnimation(id: string) {
    this.animations.delete(id);
  }
};

// Check if user prefers reduced motion
export const prefersReducedMotion = () => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// Mobile-optimized intersection observer hook - ONLY for new components
export const useMobileInView = (options: {
  threshold?: number | number[];
  rootMargin?: string;
  triggerOnce?: boolean;
  fallbackInView?: boolean;
  delay?: number;
} = {}) => {
  const isMobile = useIsMobile();
  const [hasBeenInView, setHasBeenInView] = useState(false);
  const animationId = useRef(`anim-${Math.random().toString(36).substr(2, 9)}`);
  
  // Safe defaults that won't interfere with existing systems
  const safeOptions = {
    threshold: options.threshold || (isMobile ? 0.05 : 0.1),
    rootMargin: options.rootMargin || '100px',
    triggerOnce: options.triggerOnce !== false,
    fallbackInView: options.fallbackInView || false,
    delay: options.delay || 0,
  };
  
  const { ref, inView, entry } = useInView(safeOptions);
  
  // Track when element has been in view
  useEffect(() => {
    if (inView && !hasBeenInView) {
      setHasBeenInView(true);
      performanceMonitor.trackAnimation(animationId.current);
    }
  }, [inView, hasBeenInView]);
  
  // Clean up performance tracking
  useEffect(() => {
    return () => {
      performanceMonitor.clearAnimation(animationId.current);
    };
  }, []);
  
  // Return state that maintains existing behavior for desktop
  return {
    ref,
    inView: safeOptions.triggerOnce ? (hasBeenInView || inView) : inView,
    entry,
    hasBeenInView,
    isMobile
  };
};

// Lazy loading component wrapper - non-intrusive
export const LazyLoadWrapper: React.FC<{
  children: React.ReactNode;
  className?: string;
  rootMargin?: string;
  placeholder?: React.ReactNode;
  onLoad?: () => void;
  disabled?: boolean;
}> = ({ children, className = '', rootMargin = '200px', placeholder = null, onLoad, disabled = false }) => {
  const { ref, inView, hasBeenInView } = useMobileInView({
    triggerOnce: true,
    rootMargin,
  });
  
  useEffect(() => {
    if (hasBeenInView && onLoad) {
      onLoad();
    }
  }, [hasBeenInView, onLoad]);
  
  // If disabled, just render children directly
  if (disabled) {
    return <div className={className}>{children}</div>;
  }
  
  return (
    <div ref={ref} className={className}>
      {hasBeenInView ? children : (placeholder || children)}
    </div>
  );
};

// Mobile-optimized animation variants generator - safe fallbacks
export const getMobileOptimizedVariants = (
  desktopVariants: any,
  mobileVariants?: any
) => {
  if (typeof window === 'undefined') return desktopVariants;
  
  const isMobile = window.innerWidth < 768;
  const reducedMotion = prefersReducedMotion();
  
  // Preserve existing behavior if reduced motion is preferred
  if (reducedMotion) {
    return {
      hidden: { opacity: 1 },
      visible: { opacity: 1 },
    };
  }
  
  // Only apply mobile optimizations on actual mobile devices
  if (isMobile && mobileVariants) {
    return mobileVariants;
  } else if (isMobile) {
    // Apply subtle optimizations for mobile
    const optimized = { ...desktopVariants };
    if (optimized.visible?.transition) {
      optimized.visible.transition = {
        ...optimized.visible.transition,
        duration: Math.min(optimized.visible.transition.duration || 0.6, 0.4)
      };
    }
    return optimized;
  }
  
  // Return original variants for desktop
  return desktopVariants;
};

// Performance-optimized scroll handler
export const useOptimizedScroll = (callback: () => void, delay = 100) => {
  const timeoutRef = useRef<NodeJS.Timeout>();
  const rafRef = useRef<number>();
  
  const optimizedCallback = useCallback(() => {
    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current);
    }
    
    rafRef.current = requestAnimationFrame(() => {
      callback();
    });
  }, [callback]);
  
  useEffect(() => {
    const handleScroll = () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(optimizedCallback, delay);
    };
    
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
    };
  }, [optimizedCallback, delay]);
};

// Image lazy loading with intersection observer
export const LazyImage: React.FC<{
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  placeholderColor?: string;
}> = ({ src, alt, width, height, className = '', placeholderColor = '#f3f4f6' }) => {
  const { ref, inView } = useMobileInView({
    triggerOnce: true,
    rootMargin: '200px',
  });
  
  const [loaded, setLoaded] = useState(false);
  
  return (
    <div
      ref={ref}
      className={`relative ${className}`}
      style={{
        paddingBottom: width && height ? `${(height / width) * 100}%` : undefined,
        backgroundColor: !loaded ? placeholderColor : undefined,
      }}
    >
      {inView && (
        <img
          src={src}
          alt={alt}
          width={width}
          height={height}
          onLoad={() => setLoaded(true)}
          className={`
            ${width && height ? 'absolute inset-0 w-full h-full object-cover' : ''}
            transition-opacity duration-300
            ${loaded ? 'opacity-100' : 'opacity-0'}
          `}
        />
      )}
    </div>
  );
};

// Export performance utilities
export const mobilePerformance = {
  monitor: performanceMonitor,
  prefersReducedMotion,
}; 