import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import React, { Suspense } from "react";
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/react";

import './App.css'
import { AuthProvider } from './hooks/useAuth'

const queryClient = new QueryClient();

// Lazy load all components for performance
const Index = React.lazy(() => import("./pages/Index"));
const About = React.lazy(() => import("./pages/About"));
const Apply = React.lazy(() => import("./pages/Apply"));
const FamilyFriends = React.lazy(() => import("./pages/FamilyFriends"));
const NotFound = React.lazy(() => import("./pages/NotFound"));
const TenInTen = React.lazy(() => import("./pages/TenInTen"));
const Admin = React.lazy(() => import("./pages/Admin"));
const Program = React.lazy(() => import("./pages/Program"));

// New lazy loaded components for user dashboard
const Login = React.lazy(() => import("./pages/Login"));
const Register = React.lazy(() => import("./pages/Register"));
const Dashboard = React.lazy(() => import("./pages/Dashboard"));
const Onboarding = React.lazy(() => import("./pages/Onboarding"));
const Profile = React.lazy(() => import("./pages/Profile"));
const ProtectedRoute = React.lazy(() => import("./components/auth/ProtectedRoute"));

// Loading component
const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen bg-oven-black">
    <div className="text-center">
      <div className="w-16 h-16 border-4 border-sauce-red border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
      <p className="text-accent-white text-lg">Loading...</p>
    </div>
  </div>
);

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <AuthProvider>
          <Router>
            <Routes>
              <Route path="/" element={<Suspense fallback={<LoadingFallback />}><Index /></Suspense>} />
              <Route path="/apply" element={<Suspense fallback={<LoadingFallback />}><Apply /></Suspense>} />
              <Route path="/admin" element={<Suspense fallback={<LoadingFallback />}><Admin /></Suspense>} />
              <Route path="/program" element={<Suspense fallback={<LoadingFallback />}><Program /></Suspense>} />
              <Route path="/about" element={<Suspense fallback={<LoadingFallback />}><About /></Suspense>} />
              <Route path="/family-friends" element={<Suspense fallback={<LoadingFallback />}><FamilyFriends /></Suspense>} />
              <Route path="/ten-in-ten" element={<Suspense fallback={<LoadingFallback />}><TenInTen /></Suspense>} />
              
              {/* Authentication Routes */}
              <Route path="/login" element={<Suspense fallback={<LoadingFallback />}><Login /></Suspense>} />
              <Route path="/register" element={<Suspense fallback={<LoadingFallback />}><Register /></Suspense>} />
              
              {/* Protected User Routes */}
              <Route path="/dashboard" element={
                <ProtectedRoute>
                  <Suspense fallback={<LoadingFallback />}><Dashboard /></Suspense>
                </ProtectedRoute>
              } />
              <Route path="/onboarding" element={
                <ProtectedRoute>
                  <Suspense fallback={<LoadingFallback />}><Onboarding /></Suspense>
                </ProtectedRoute>
              } />
              <Route path="/profile" element={
                <ProtectedRoute>
                  <Suspense fallback={<LoadingFallback />}><Profile /></Suspense>
                </ProtectedRoute>
              } />
              
              <Route path="*" element={<Suspense fallback={<LoadingFallback />}><NotFound /></Suspense>} />
            </Routes>
          </Router>
        </AuthProvider>
      </TooltipProvider>
      <Analytics />
      <SpeedInsights />
    </QueryClientProvider>
  )
}

export default App;
