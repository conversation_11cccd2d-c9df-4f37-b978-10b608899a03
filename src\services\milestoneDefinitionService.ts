/**
 * Service for getting detailed milestone definitions, requirements, and success criteria
 * This ensures AI coaching questions are specific to each milestone's learning objectives
 */

interface MilestoneDefinition {
  id: string;
  title: string;
  description: string;
  learningObjectives: string[];
  successCriteria: string[];
  keyQuestions: string[];
  deliverables: string[];
  stage: number;
  validationPoints: string[];
}

class MilestoneDefinitionService {
  private milestoneDefinitions: Record<string, MilestoneDefinition> = {
    // Stage 0: Spark - Problem & Validation
    'problem_identified': {
      id: 'problem_identified',
      title: 'Problem Identification',
      description: 'Clearly define a specific problem or opportunity that affects real users',
      learningObjectives: [
        'Identify a specific, well-defined problem',
        'Understand who experiences this problem',
        'Articulate why this problem matters',
        'Validate that the problem is worth solving'
      ],
      successCriteria: [
        'Problem is specific and measurable',
        'Target users are clearly identified',
        'Problem impact is quantified or described',
        'Evidence of problem existence is provided'
      ],
      keyQuestions: [
        'What specific problem are you solving?',
        'Who exactly experiences this problem?',
        'How do you know this problem exists?',
        'What evidence do you have that people care about solving this?',
        'How are people currently dealing with this problem?'
      ],
      deliverables: [
        'Clear problem statement',
        'Target user description',
        'Problem validation evidence',
        'Current solution analysis'
      ],
      stage: 0,
      validationPoints: [
        'Problem specificity and clarity',
        'User research or evidence',
        'Market need validation',
        'Problem-solution fit potential'
      ]
    },

    'idea_validation': {
      id: 'idea_validation',
      title: 'Solution Concept Validation',
      description: 'Validate your solution approach with potential users and stakeholders',
      learningObjectives: [
        'Test solution concept with target users',
        'Gather feedback on solution approach',
        'Validate solution-problem fit',
        'Identify potential challenges early'
      ],
      successCriteria: [
        'Solution concept is tested with real users',
        'Feedback is collected and analyzed',
        'Solution addresses the core problem',
        'Key assumptions are validated or challenged'
      ],
      keyQuestions: [
        'How have you tested your solution concept?',
        'What feedback did you receive from potential users?',
        'How does your solution uniquely address the problem?',
        'What assumptions about your solution have you validated?',
        'What would make users choose your solution over alternatives?'
      ],
      deliverables: [
        'User feedback summary',
        'Solution concept description',
        'Validation test results',
        'Assumption testing outcomes'
      ],
      stage: 0,
      validationPoints: [
        'User testing evidence',
        'Solution differentiation',
        'Assumption validation',
        'Market fit indicators'
      ]
    },

    // Stage 1: Formation - Concept & Planning
    'concept_clarity': {
      id: 'concept_clarity',
      title: 'Value Proposition Clarity',
      description: 'Develop a clear, compelling value proposition that resonates with your target market',
      learningObjectives: [
        'Articulate unique value proposition',
        'Understand competitive landscape',
        'Define target market clearly',
        'Validate value proposition with users'
      ],
      successCriteria: [
        'Value proposition is clear and compelling',
        'Competitive advantages are identified',
        'Target market is well-defined',
        'Value proposition is validated with users'
      ],
      keyQuestions: [
        'What unique value do you provide to users?',
        'How is your solution different from existing alternatives?',
        'Who is your specific target market?',
        'How have you validated your value proposition?',
        'What would make users pay for your solution?'
      ],
      deliverables: [
        'Value proposition statement',
        'Competitive analysis',
        'Target market definition',
        'Value validation evidence'
      ],
      stage: 1,
      validationPoints: [
        'Value proposition clarity',
        'Competitive differentiation',
        'Market positioning',
        'User value validation'
      ]
    },

    'team_building': {
      id: 'team_building',
      title: 'Team Assembly & Collaboration',
      description: 'Build or plan your team structure and establish effective collaboration processes',
      learningObjectives: [
        'Identify required skills and roles',
        'Plan team structure and responsibilities',
        'Establish collaboration processes',
        'Address team building challenges'
      ],
      successCriteria: [
        'Team roles and responsibilities are defined',
        'Required skills are identified',
        'Collaboration processes are established',
        'Team building strategy is clear'
      ],
      keyQuestions: [
        'What skills and roles do you need for your team?',
        'How are you planning to find and recruit team members?',
        'What collaboration tools and processes will you use?',
        'How will you handle decision-making and conflict resolution?',
        'What is your strategy for maintaining team motivation and alignment?'
      ],
      deliverables: [
        'Team structure plan',
        'Role and responsibility matrix',
        'Collaboration framework',
        'Team building strategy'
      ],
      stage: 1,
      validationPoints: [
        'Team planning completeness',
        'Skill gap analysis',
        'Collaboration strategy',
        'Team building approach'
      ]
    },

    'milestone_planning': {
      id: 'milestone_planning',
      title: 'Development Roadmap Planning',
      description: 'Create a detailed roadmap with clear milestones and success metrics',
      learningObjectives: [
        'Break down project into manageable milestones',
        'Set realistic timelines and goals',
        'Define success metrics for each milestone',
        'Plan resource allocation and dependencies'
      ],
      successCriteria: [
        'Roadmap has clear, achievable milestones',
        'Timelines are realistic and well-planned',
        'Success metrics are defined',
        'Dependencies and risks are identified'
      ],
      keyQuestions: [
        'What are the key milestones in your development roadmap?',
        'How did you determine realistic timelines for each milestone?',
        'What metrics will you use to measure success?',
        'What dependencies and risks have you identified?',
        'How will you adapt your roadmap based on learnings?'
      ],
      deliverables: [
        'Detailed project roadmap',
        'Milestone timeline',
        'Success metrics definition',
        'Risk and dependency analysis'
      ],
      stage: 1,
      validationPoints: [
        'Roadmap feasibility',
        'Timeline realism',
        'Metric definition',
        'Risk planning'
      ]
    },

    // Stage 2: First Build - MVP & Implementation
    'mvp_designed': {
      id: 'mvp_designed',
      title: 'MVP Architecture & Design',
      description: 'Design your minimum viable product with clear technical architecture',
      learningObjectives: [
        'Define MVP scope and core features',
        'Design technical architecture',
        'Plan implementation approach',
        'Validate technical feasibility'
      ],
      successCriteria: [
        'MVP scope is clearly defined',
        'Technical architecture is documented',
        'Implementation plan is realistic',
        'Technical feasibility is validated'
      ],
      keyQuestions: [
        'What are the core features of your MVP?',
        'How did you decide what to include vs. exclude?',
        'What is your technical architecture and why?',
        'How have you validated the technical feasibility?',
        'What are the key technical risks and how will you mitigate them?'
      ],
      deliverables: [
        'MVP feature specification',
        'Technical architecture diagram',
        'Implementation plan',
        'Technical feasibility analysis'
      ],
      stage: 2,
      validationPoints: [
        'MVP scope appropriateness',
        'Architecture soundness',
        'Implementation feasibility',
        'Technical risk assessment'
      ]
    }
  };

  /**
   * Get detailed definition for a specific milestone
   */
  getMilestoneDefinition(milestoneId: string): MilestoneDefinition | null {
    return this.milestoneDefinitions[milestoneId] || null;
  }

  /**
   * Get all milestones for a specific stage
   */
  getMilestonesForStage(stageId: number): MilestoneDefinition[] {
    return Object.values(this.milestoneDefinitions).filter(m => m.stage === stageId);
  }

  /**
   * Get milestone-specific context for AI analysis
   */
  getMilestoneContext(milestoneId: string): {
    definition: MilestoneDefinition | null;
    contextPrompt: string;
  } {
    const definition = this.getMilestoneDefinition(milestoneId);
    
    if (!definition) {
      return {
        definition: null,
        contextPrompt: `Milestone: ${milestoneId} (definition not found)`
      };
    }

    const contextPrompt = `
MILESTONE: ${definition.title}
DESCRIPTION: ${definition.description}

LEARNING OBJECTIVES:
${definition.learningObjectives.map(obj => `- ${obj}`).join('\n')}

SUCCESS CRITERIA:
${definition.successCriteria.map(criteria => `- ${criteria}`).join('\n')}

KEY VALIDATION POINTS:
${definition.validationPoints.map(point => `- ${point}`).join('\n')}

EXPECTED DELIVERABLES:
${definition.deliverables.map(deliverable => `- ${deliverable}`).join('\n')}

MILESTONE-SPECIFIC QUESTIONS TO EXPLORE:
${definition.keyQuestions.map(question => `- ${question}`).join('\n')}
`;

    return {
      definition,
      contextPrompt
    };
  }

  /**
   * Check if a milestone exists
   */
  milestoneExists(milestoneId: string): boolean {
    return milestoneId in this.milestoneDefinitions;
  }

  /**
   * Get all milestone IDs
   */
  getAllMilestoneIds(): string[] {
    return Object.keys(this.milestoneDefinitions);
  }
}

export default new MilestoneDefinitionService();
