-- Comprehensive Onboarding System Tables
-- This redesigns onboarding to collect maximum useful data for personalization

-- 1. Structured Onboarding Responses (Phase 1)
CREATE TABLE IF NOT EXISTS onboarding_responses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  track VARCHAR NOT NULL CHECK (track IN ('newbie', 'builder', 'scaler')),
  
  -- Problem & Solution
  problem_description TEXT,
  solution_approach TEXT,
  target_audience TEXT,
  unique_value_proposition TEXT,
  
  -- Technical & Background
  technical_background VARCHAR, -- 'beginner', 'intermediate', 'expert'
  previous_experience TEXT,
  available_time_per_week INTEGER, -- hours
  budget_range VARCHAR, -- 'none', 'under-1k', '1k-5k', '5k-20k', '20k+'
  
  -- Market & Validation
  market_research_done BOOLEAN DEFAULT false,
  customer_interviews_done BOOLEAN DEFAULT false,
  competition_analysis TEXT,
  early_feedback TEXT,
  
  -- Goals & Timeline  
  primary_goal TEXT, -- 'learn', 'build_mvp', 'find_users', 'raise_funds', 'scale'
  timeline_expectation VARCHAR, -- '1-3months', '3-6months', '6-12months', '12months+'
  success_metrics TEXT,
  biggest_challenge TEXT,
  
  -- Learning & Support
  learning_style VARCHAR, -- 'visual', 'hands-on', 'reading', 'video', 'mentorship'
  preferred_communication VARCHAR, -- 'daily', 'weekly', 'bi-weekly', 'monthly'
  collaboration_preference VARCHAR, -- 'solo', 'small-team', 'community', 'mentor-guided'
  
  -- Resources & Tools
  current_tools_used TEXT[], -- array of tools they already use
  technical_skills TEXT[], -- array of technical skills
  network_connections TEXT, -- description of their network
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. AI Follow-up Conversations (Phase 2)
CREATE TABLE IF NOT EXISTS ai_followup_conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  onboarding_response_id UUID REFERENCES onboarding_responses(id) ON DELETE CASCADE,
  
  conversation_data JSONB NOT NULL, -- full conversation history
  ai_satisfaction_score INTEGER DEFAULT 0, -- 0-100, when 85+ AI is satisfied
  areas_needing_clarification TEXT[], -- what AI still wants to know
  key_insights_extracted JSONB, -- insights AI extracted during conversation
  
  total_questions_asked INTEGER DEFAULT 0,
  conversation_completed BOOLEAN DEFAULT false,
  completion_reason VARCHAR, -- 'satisfied', 'max_questions', 'user_stop'
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Comprehensive User Profiles (Phase 3)
CREATE TABLE IF NOT EXISTS comprehensive_user_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  onboarding_response_id UUID REFERENCES onboarding_responses(id),
  followup_conversation_id UUID REFERENCES ai_followup_conversations(id),
  
  -- AI-Generated Profile Analysis
  profile_summary TEXT, -- AI's summary of the user
  strengths TEXT[], -- identified strengths
  knowledge_gaps TEXT[], -- areas for improvement
  personality_traits JSONB, -- traits relevant to entrepreneurship
  
  -- Project Analysis
  project_viability_score INTEGER, -- 0-100
  market_opportunity_assessment TEXT,
  technical_feasibility_analysis TEXT,
  recommended_next_steps TEXT[],
  potential_blockers TEXT[],
  
  -- Personalization Data
  optimal_learning_path JSONB, -- personalized learning recommendations
  mentor_matching_criteria JSONB, -- what kind of mentor they need
  resource_preferences JSONB, -- types of resources that work best for them
  communication_strategy JSONB, -- how to best engage with this user
  
  -- Goals & Tracking
  personalized_goals JSONB, -- AI-generated goals tailored to their situation
  success_probability_factors JSONB, -- factors that increase their success chance
  risk_factors JSONB, -- potential risks to watch for
  
  -- Metrics for Dashboard Personalization
  preferred_dashboard_layout VARCHAR, -- 'simple', 'detailed', 'visual', 'metrics-heavy'
  key_metrics_to_track TEXT[], -- which metrics matter most to them
  notification_preferences JSONB, -- how and when to notify them
  
  profile_confidence_score INTEGER DEFAULT 0, -- how confident AI is in this profile
  last_updated_by_ai TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Learning & Iteration Tracking
CREATE TABLE IF NOT EXISTS user_learning_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  event_type VARCHAR NOT NULL, -- 'completed_task', 'asked_question', 'used_resource', 'milestone_reached', 'struggled_with'
  event_data JSONB NOT NULL, -- detailed event information
  ai_analysis JSONB, -- AI's analysis of this learning event
  
  impact_on_profile JSONB, -- how this event should update their profile
  confidence_change INTEGER DEFAULT 0, -- -100 to +100 change in confidence
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_onboarding_responses_user_id ON onboarding_responses(user_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_responses_track ON onboarding_responses(track);

CREATE INDEX IF NOT EXISTS idx_ai_followup_conversations_user_id ON ai_followup_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_followup_conversations_completed ON ai_followup_conversations(conversation_completed);

CREATE INDEX IF NOT EXISTS idx_comprehensive_user_profiles_user_id ON comprehensive_user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_comprehensive_user_profiles_confidence ON comprehensive_user_profiles(profile_confidence_score);

CREATE INDEX IF NOT EXISTS idx_user_learning_events_user_id ON user_learning_events(user_id);
CREATE INDEX IF NOT EXISTS idx_user_learning_events_type ON user_learning_events(event_type);
CREATE INDEX IF NOT EXISTS idx_user_learning_events_created_at ON user_learning_events(created_at);

-- RLS Policies
ALTER TABLE onboarding_responses ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can manage own onboarding responses" ON onboarding_responses
  FOR ALL USING (auth.uid() = user_id);

ALTER TABLE ai_followup_conversations ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can manage own AI conversations" ON ai_followup_conversations
  FOR ALL USING (auth.uid() = user_id);

ALTER TABLE comprehensive_user_profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can read own comprehensive profile" ON comprehensive_user_profiles
  FOR ALL USING (auth.uid() = user_id);

ALTER TABLE user_learning_events ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can manage own learning events" ON user_learning_events
  FOR ALL USING (auth.uid() = user_id);

-- Triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_onboarding_responses_updated_at
  BEFORE UPDATE ON onboarding_responses
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_followup_conversations_updated_at
  BEFORE UPDATE ON ai_followup_conversations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_comprehensive_user_profiles_updated_at
  BEFORE UPDATE ON comprehensive_user_profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 