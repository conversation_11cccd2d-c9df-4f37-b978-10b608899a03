-- Fix function return types to resolve bigint/integer mismatch

-- Drop and recreate get_success_stories function with proper type casting
DROP FUNCTION IF EXISTS get_success_stories();

CREATE OR REPLACE FUNCTION get_success_stories()
RETURNS TABLE (
  user_id UUID,
  full_name VARCHAR,
  email VARCHAR,
  track VARCHAR,
  success_category TEXT,
  current_stage INTEGER,
  total_milestones_submitted INTEGER,
  approved_milestones INTEGER,
  engagement_score INTEGER,
  days_to_current_stage INTEGER,
  success_metrics TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    upa.user_id,
    upa.full_name,
    upa.email,
    upa.track,
    upa.success_category,
    upa.current_stage,
    upa.total_milestones_submitted,
    upa.approved_milestones,
    upa.engagement_score::INTEGER,
    EXTRACT(EPOCH FROM (NOW() - upa.user_created_at))::INTEGER / (24 * 3600) as days_to_current_stage,
    CASE 
      WHEN upa.success_category = 'High Performer' 
        THEN 'Advanced quickly through stages with high-quality submissions'
      WHEN upa.success_category = 'On Track' 
        THEN 'Consistent progress with positive sentiment'
      WHEN upa.success_category = 'Engaged' 
        THEN 'Highly engaged with regular updates'
      ELSE 'Showing positive momentum'
    END as success_metrics
  FROM user_progress_analytics upa
  WHERE upa.success_category IN ('High Performer', 'On Track', 'Engaged')
    AND upa.engagement_score >= 60
  ORDER BY 
    CASE upa.success_category 
      WHEN 'High Performer' THEN 1 
      WHEN 'On Track' THEN 2 
      WHEN 'Engaged' THEN 3 
      ELSE 4 
    END,
    upa.engagement_score DESC;
END;
$$ LANGUAGE plpgsql;

-- Also fix get_users_needing_intervention function for consistency
DROP FUNCTION IF EXISTS get_users_needing_intervention();

CREATE OR REPLACE FUNCTION get_users_needing_intervention()
RETURNS TABLE (
  user_id UUID,
  full_name VARCHAR,
  email VARCHAR,
  track VARCHAR,
  risk_level TEXT,
  reason TEXT,
  days_since_last_activity INTEGER,
  recommended_action TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    upa.user_id,
    upa.full_name,
    upa.email,
    upa.track,
    upa.risk_level,
    CASE 
      WHEN NOT upa.onboarding_completed AND upa.onboarding_duration_hours > 72 
        THEN 'Stuck in onboarding for over 3 days'
      WHEN upa.onboarding_completed AND upa.days_in_current_stage > 14 
        THEN 'No stage progression for over 2 weeks'
      WHEN upa.updates_last_7_days = 0 AND upa.last_update_date < NOW() - INTERVAL '7 days' 
        THEN 'No daily updates for over a week'
      WHEN upa.current_confidence < 30 
        THEN 'Very low confidence score'
      ELSE 'Multiple risk factors'
    END as reason,
    COALESCE(
      EXTRACT(EPOCH FROM (NOW() - GREATEST(upa.last_update_date, upa.last_milestone_date, upa.last_ai_interaction)))::INTEGER / (24 * 3600),
      999
    ) as days_since_last_activity,
    CASE 
      WHEN NOT upa.onboarding_completed 
        THEN 'Send onboarding completion reminder and offer 1:1 help'
      WHEN upa.updates_last_7_days = 0 
        THEN 'Reach out to re-engage with daily updates'
      WHEN upa.current_confidence < 30 
        THEN 'Schedule confidence-building session'
      WHEN upa.days_in_current_stage > 14 
        THEN 'Provide stage-specific guidance and resources'
      ELSE 'General check-in and support offer'
    END as recommended_action
  FROM user_progress_analytics upa
  WHERE upa.risk_level IN ('High', 'Medium')
  ORDER BY 
    CASE upa.risk_level WHEN 'High' THEN 1 WHEN 'Medium' THEN 2 ELSE 3 END,
    days_since_last_activity DESC;
END;
$$ LANGUAGE plpgsql;

-- Create a comprehensive user profile function for the management interface
CREATE OR REPLACE FUNCTION get_user_comprehensive_profile(target_user_id UUID)
RETURNS TABLE (
  -- Basic user info
  user_id UUID,
  full_name VARCHAR,
  email VARCHAR,
  track VARCHAR,
  created_at TIMESTAMP WITH TIME ZONE,
  onboarding_completed BOOLEAN,
  
  -- Progress metrics
  current_stage INTEGER,
  current_confidence INTEGER,
  days_in_current_stage NUMERIC,
  engagement_score INTEGER,
  risk_level TEXT,
  success_category TEXT,
  progress_status TEXT,
  
  -- Onboarding data
  problem_description TEXT,
  solution_approach TEXT,
  target_audience TEXT,
  technical_background TEXT,
  primary_goal TEXT,
  biggest_challenge TEXT,
  
  -- Activity metrics
  total_daily_updates INTEGER,
  updates_last_7_days INTEGER,
  last_update_date TIMESTAMP WITH TIME ZONE,
  total_milestones_submitted INTEGER,
  approved_milestones INTEGER,
  last_milestone_date TIMESTAMP WITH TIME ZONE,
  total_ai_interactions INTEGER,
  last_ai_interaction TIMESTAMP WITH TIME ZONE,
  
  -- Recent activity
  recent_daily_updates JSONB,
  recent_milestones JSONB,
  recent_ai_insights JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    -- Basic user info
    upa.user_id,
    upa.full_name,
    upa.email,
    upa.track,
    upa.user_created_at as created_at,
    upa.onboarding_completed,
    
    -- Progress metrics
    upa.current_stage,
    upa.current_confidence,
    upa.days_in_current_stage,
    upa.engagement_score,
    upa.risk_level,
    upa.success_category,
    upa.progress_status,
    
    -- Onboarding data
    or_data.problem_description,
    or_data.solution_approach,
    or_data.target_audience,
    or_data.technical_background,
    or_data.primary_goal,
    or_data.biggest_challenge,
    
    -- Activity metrics
    upa.total_daily_updates,
    upa.updates_last_7_days,
    upa.last_update_date,
    upa.total_milestones_submitted,
    upa.approved_milestones,
    upa.last_milestone_date,
    upa.total_ai_interactions,
    upa.last_ai_interaction,
    
    -- Recent activity (as JSON)
    COALESCE(
      (SELECT jsonb_agg(
        jsonb_build_object(
          'date', du.created_at,
          'content', LEFT(du.content, 200),
          'sentiment_score', du.sentiment_score
        ) ORDER BY du.created_at DESC
      ) FROM daily_updates du WHERE du.user_id = target_user_id LIMIT 5),
      '[]'::jsonb
    ) as recent_daily_updates,
    
    COALESCE(
      (SELECT jsonb_agg(
        jsonb_build_object(
          'date', ms.submission_date,
          'stage_id', ms.stage_id,
          'milestone_id', ms.milestone_id,
          'is_approved', ms.is_approved,
          'content', LEFT(ms.submission_content, 200)
        ) ORDER BY ms.submission_date DESC
      ) FROM milestone_submissions ms WHERE ms.user_id = target_user_id LIMIT 5),
      '[]'::jsonb
    ) as recent_milestones,
    
    COALESCE(
      (SELECT jsonb_agg(
        jsonb_build_object(
          'date', ume.created_at,
          'type', ume.memory_type,
          'content', LEFT(ume.content::text, 200)
        ) ORDER BY ume.created_at DESC
      ) FROM user_memory_entries ume WHERE ume.user_id = target_user_id AND ume.memory_type = 'insight' LIMIT 5),
      '[]'::jsonb
    ) as recent_ai_insights
    
  FROM user_progress_analytics upa
  LEFT JOIN onboarding_responses or_data ON upa.user_id = or_data.user_id
  WHERE upa.user_id = target_user_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to get all users with basic management info
CREATE OR REPLACE FUNCTION get_users_for_management(
  limit_count INTEGER DEFAULT 50,
  offset_count INTEGER DEFAULT 0,
  filter_track VARCHAR DEFAULT NULL,
  filter_risk_level VARCHAR DEFAULT NULL,
  filter_success_category VARCHAR DEFAULT NULL,
  search_term VARCHAR DEFAULT NULL
)
RETURNS TABLE (
  user_id UUID,
  full_name VARCHAR,
  email VARCHAR,
  track VARCHAR,
  created_at TIMESTAMP WITH TIME ZONE,
  onboarding_completed BOOLEAN,
  current_stage INTEGER,
  engagement_score INTEGER,
  risk_level TEXT,
  success_category TEXT,
  days_since_last_activity INTEGER,
  last_update_date TIMESTAMP WITH TIME ZONE,
  total_milestones_submitted INTEGER,
  problem_description TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    upa.user_id,
    upa.full_name,
    upa.email,
    upa.track,
    upa.user_created_at as created_at,
    upa.onboarding_completed,
    upa.current_stage,
    upa.engagement_score,
    upa.risk_level,
    upa.success_category,
    COALESCE(
      EXTRACT(EPOCH FROM (NOW() - GREATEST(upa.last_update_date, upa.last_milestone_date, upa.last_ai_interaction)))::INTEGER / (24 * 3600),
      EXTRACT(EPOCH FROM (NOW() - upa.user_created_at))::INTEGER / (24 * 3600)
    ) as days_since_last_activity,
    upa.last_update_date,
    upa.total_milestones_submitted,
    or_data.problem_description
  FROM user_progress_analytics upa
  LEFT JOIN onboarding_responses or_data ON upa.user_id = or_data.user_id
  WHERE 
    (filter_track IS NULL OR upa.track = filter_track) AND
    (filter_risk_level IS NULL OR upa.risk_level = filter_risk_level) AND
    (filter_success_category IS NULL OR upa.success_category = filter_success_category) AND
    (search_term IS NULL OR 
     upa.full_name ILIKE '%' || search_term || '%' OR 
     upa.email ILIKE '%' || search_term || '%' OR
     or_data.problem_description ILIKE '%' || search_term || '%')
  ORDER BY 
    CASE upa.risk_level WHEN 'High' THEN 1 WHEN 'Medium' THEN 2 ELSE 3 END,
    upa.engagement_score DESC,
    upa.user_created_at DESC
  LIMIT limit_count OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;

-- Add comments
COMMENT ON FUNCTION get_user_comprehensive_profile(UUID) IS 'Returns comprehensive user profile for detailed management view';
COMMENT ON FUNCTION get_users_for_management IS 'Returns filtered and paginated user list for management interface';
