import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
}

interface AnalysisRequest {
  type: 'milestone' | 'daily_update' | 'stage_progress';
  content: string;
  context: {
    userId: string;
    stageId?: number;
    milestoneId?: string;
    userContext?: any;
  };
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      status: 200,
      headers: corsHeaders
    })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Get the request body
    const { type, content, context }: AnalysisRequest = await req.json()

    // Validate request
    if (!type || !content || !context?.userId) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get Gemini API key from environment
    const geminiApiKey = Deno.env.get('GEMINI_API_KEY')
    if (!geminiApiKey) {
      return new Response(
        JSON.stringify({ error: 'AI service not configured' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Check for cached analysis first
    const cacheKey = `${type}_${context.userId}_${hashContent(content)}`
    const { data: cachedResult } = await supabaseClient
      .from('cached_ai_analyses')
      .select('analysis_result')
      .eq('user_id', context.userId)
      .eq('analysis_type', type)
      .eq('content_hash', cacheKey)
      .gt('expires_at', new Date().toISOString())
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle()

    if (cachedResult) {
      console.log('✅ Using cached analysis result')
      return new Response(
        JSON.stringify({ 
          result: cachedResult.analysis_result, 
          cached: true 
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Perform AI analysis based on type
    let analysisResult
    switch (type) {
      case 'milestone':
        analysisResult = await analyzeMilestoneSubmission(content, context, geminiApiKey)
        break
      case 'daily_update':
        analysisResult = await analyzeDailyUpdate(content, context, geminiApiKey)
        break
      case 'stage_progress':
        analysisResult = await analyzeStageProgress(content, context, geminiApiKey)
        break
      default:
        throw new Error(`Unknown analysis type: ${type}`)
    }

    // Cache the result
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + 4) // Cache for 4 hours

    await supabaseClient
      .from('cached_ai_analyses')
      .upsert({
        user_id: context.userId,
        analysis_type: type,
        content_hash: cacheKey,
        analysis_result: analysisResult,
        expires_at: expiresAt.toISOString(),
        metadata: {
          content_length: content.length,
          cached_at: new Date().toISOString()
        }
      }, {
        onConflict: 'user_id,analysis_type,content_hash'
      })

    return new Response(
      JSON.stringify({ 
        result: analysisResult, 
        cached: false 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in AI analysis:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

// Helper function to create content hash
function hashContent(content: string): string {
  // Simple hash function for caching
  let hash = 0
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return hash.toString()
}

// AI Analysis Functions
async function analyzeMilestoneSubmission(content: string, context: any, apiKey: string) {
  // Extract user context for personalized analysis
  const userContext = context.userContext || {};
  const projectInfo = userContext.project_info || {};

  // Build milestone-specific context if available
  const milestoneSpecificContext = context.milestoneDefinition ? `
MILESTONE-SPECIFIC CONTEXT:
${context.milestoneDescription}

This milestone specifically aims to validate:
${context.milestoneDefinition.validationPoints?.map((point: string) => `- ${point}`).join('\n') || '- General milestone completion'}

Key questions this milestone should address:
${context.milestoneDefinition.keyQuestions?.map((q: string) => `- ${q}`).join('\n') || '- Basic milestone requirements'}

Success criteria for this milestone:
${context.milestoneDefinition.successCriteria?.map((criteria: string) => `- ${criteria}`).join('\n') || '- Milestone completion'}
` : `
MILESTONE CONTEXT:
- Stage: ${context.stageId} (${context.stageTitle || 'Unknown'})
- Milestone: ${context.milestoneTitle || context.milestoneId}
- Milestone Description: ${context.milestoneDescription || 'Not provided'}
`;

  const prompt = `You are a Pie Fi Product Journey coach analyzing a milestone submission. Your philosophy: "Challenge the idea without judgment" and help builders think deeply about what they're working on.

${milestoneSpecificContext}

USER CONTEXT:
- Project: ${projectInfo.description || 'Not specified'}
- Target Audience: ${projectInfo.target_audience || 'Not specified'}
- Technical Background: ${projectInfo.technical_background || 'Not specified'}
- Primary Goal: ${projectInfo.primary_goal || 'Not specified'}
- Key Challenges: ${userContext.key_challenges?.join(', ') || 'None identified'}
- User Strengths: ${userContext.ai_insights?.strengths?.join(', ') || 'None identified'}
- Knowledge Gaps: ${userContext.ai_insights?.knowledge_gaps?.join(', ') || 'None identified'}

USER SUBMISSION:
"${content}"

ANALYSIS TASK:
1. Analyze the quality and depth of their milestone submission in context of their project
2. Identify key insights about their progress and thinking specific to their situation
3. Provide coaching feedback that challenges them to think deeper about their specific project
4. Extract learnings that should be added to their profile
5. Suggest specific next steps relevant to their project and challenges

Respond with JSON in this exact format:
{
  "consolidatedFeedback": "Single comprehensive response combining insights and coaching feedback - avoid redundancy",
  "followUpQuestions": ["Specific question 1 to gather more info or challenge thinking", "Specific question 2 if needed"],
  "confidence": 85,
  "keyLearnings": ["learning 1", "learning 2"],
  "nextSteps": ["step 1", "step 2"],
  "profileUpdates": {
    "newSkills": ["skill1"],
    "updatedChallenges": ["challenge1"],
    "strengthsRevealed": ["strength1"]
  }
}`

  return await callGeminiAPI(prompt, apiKey)
}

async function analyzeDailyUpdate(content: string, context: any, apiKey: string) {
  // Extract user context for personalized analysis
  const userContext = context.userContext || {};
  const projectInfo = userContext.project_info || {};

  const prompt = `Analyze this daily update for a Pie Fi builder with comprehensive context:

DAILY UPDATE:
"${content}"

USER CONTEXT:
- Current Stage: ${context.stageId} (${context.stageTitle || 'Unknown'})
- Project: ${projectInfo.description || 'Not specified'}
- Target Audience: ${projectInfo.target_audience || 'Not specified'}
- Technical Background: ${projectInfo.technical_background || 'Not specified'}
- Primary Goal: ${projectInfo.primary_goal || 'Not specified'}
- Key Challenges: ${userContext.key_challenges?.join(', ') || 'None identified'}
- Recent Progress: ${userContext.recent_progress?.slice(0, 2)?.join('; ') || 'No recent updates'}
- User Strengths: ${userContext.ai_insights?.strengths?.join(', ') || 'None identified'}

Provide coaching insights and progress analysis using the Pie Fi philosophy of "Challenge the idea without judgment". Be specific to their project and stage. Respond with JSON:
{
  "insights": "Analysis of progress and thinking specific to their project context",
  "coachingInsight": "Supportive but challenging feedback tailored to their situation",
  "confidence": 80,
  "nextActions": ["action 1 specific to their project", "action 2 relevant to their stage"],
  "sentiment": 0.7,
  "progress": 0.8
}`

  return await callGeminiAPI(prompt, apiKey)
}

async function analyzeStageProgress(content: string, context: any, apiKey: string) {
  // Extract user context for personalized analysis
  const userContext = context.userContext || {};
  const projectInfo = userContext.project_info || {};

  const prompt = `Analyze this update for stage progression indicators with full user context:

UPDATE CONTENT:
"${content}"

USER CONTEXT:
- Current Stage: ${context.stageId}
- Project: ${projectInfo.description || 'Not specified'}
- Target Audience: ${projectInfo.target_audience || 'Not specified'}
- Technical Background: ${projectInfo.technical_background || 'Not specified'}
- Primary Goal: ${projectInfo.primary_goal || 'Not specified'}
- Key Challenges: ${userContext.key_challenges?.join(', ') || 'None identified'}
- Recent Progress: ${userContext.recent_progress?.slice(0, 2)?.join('; ') || 'No recent updates'}

MILESTONE DETECTION EXAMPLES:
- "added [name] to my team" or "recruited a developer" → team_building
- "defined our value proposition" or "clear on what we're building" → concept_clarity
- "talked to 5 potential users" or "got feedback from customers" → user_interviews
- "built the first version" or "MVP is ready" → mvp_designed

Look for milestone completion and stage advancement signals specific to their project context. Respond with JSON:
{
  "detectedMilestones": ["milestone1", "milestone2"],
  "stageProgression": "current",
  "suggestedStage": ${context.stageId},
  "coachingInsight": "Progress feedback tailored to their project and challenges",
  "confidence": 75,
  "followUpQuestions": ["question 1 specific to their situation", "question 2 relevant to their stage"]
}`

  return await callGeminiAPI(prompt, apiKey)
}

async function callGeminiAPI(prompt: string, apiKey: string) {
  const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=' + apiKey, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      contents: [{
        parts: [{ text: prompt }]
      }],
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 4096,
      }
    })
  })

  if (!response.ok) {
    throw new Error(`Gemini API error: ${response.status}`)
  }

  const data = await response.json()
  const text = data.candidates?.[0]?.content?.parts?.[0]?.text

  if (!text) {
    throw new Error('No response from Gemini API')
  }

  // Parse JSON from response
  try {
    const jsonMatch = text.match(/\{[\s\S]*\}/)
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0])
    }
  } catch (parseError) {
    console.error('Error parsing Gemini response:', parseError)
  }

  // Fallback response
  return {
    insights: "Analysis completed successfully",
    coachingInsight: "Keep up the great work!",
    confidence: 70
  }
}
