import { GoogleGenerativeAI } from '@google/generative-ai';
import devJourneyService, { UserStageAnalysis } from './devJourneyService';
import userMemoryService, { UserContext } from './userMemoryService';
import stageProgressService from './stageProgressService';
import aiAnalysisEdgeService from './aiAnalysisEdgeService';
import { supabase } from '@/integrations/supabase/client';

// Comprehensive interfaces for the new system
interface OnboardingStructuredResponse {
  track: string;
  problemDescription: string;
  solutionApproach: string;
  targetAudience: string;
  uniqueValueProposition: string;
  technicalBackground: 'beginner' | 'intermediate' | 'expert';
  previousExperience: string;
  availableTimePerWeek: number;
  budgetRange: 'none' | 'under-1k' | '1k-5k' | '5k-20k' | '20k+';
  marketResearchDone: boolean;
  customerInterviewsDone: boolean;
  competitionAnalysis: string;
  earlyFeedback: string;
  primaryGoal: 'learn' | 'build_mvp' | 'find_users' | 'raise_funds' | 'scale';
  timelineExpectation: '1-3months' | '3-6months' | '6-12months' | '12months+';
  successMetrics: string;
  biggestChallenge: string;
  learningStyle: 'visual' | 'hands-on' | 'reading' | 'video' | 'mentorship';
  preferredCommunication: 'daily' | 'weekly' | 'bi-weekly' | 'monthly';
  collaborationPreference: 'solo' | 'small-team' | 'community' | 'mentor-guided';
  currentToolsUsed: string[];
  technicalSkills: string[];
  networkConnections: string;
}

interface AIFollowupResponse {
  question: string;
  satisfactionScore: number; // 0-100
  areasNeedingClarification: string[];
  keyInsightsExtracted: any;
  isComplete: boolean;
  completionReason?: 'satisfied' | 'max_questions' | 'user_stop';
}

interface ComprehensiveUserProfile {
  profileSummary: string;
  strengths: string[];
  knowledgeGaps: string[];
  personalityTraits: any;
  projectViabilityScore: number;
  marketOpportunityAssessment: string;
  technicalFeasibilityAnalysis: string;
  recommendedNextSteps: string[];
  potentialBlockers: string[];
  optimalLearningPath: any;
  mentorMatchingCriteria: any;
  resourcePreferences: any;
  communicationStrategy: any;
  personalizedGoals: any;
  successProbabilityFactors: any;
  riskFactors: any;
  preferredDashboardLayout: 'simple' | 'detailed' | 'visual' | 'metrics-heavy';
  keyMetricsToTrack: string[];
  notificationPreferences: any;
  profileConfidenceScore: number;
}

class GeminiService {
  private genAI: GoogleGenerativeAI | null = null;
  private model: any = null;
  private proModel: any = null; // For deep analysis
  private rateLimited: boolean = false;
  private lastRateLimitTime: number = 0;
  private useEdgeFunctions: boolean = false; // Disabled for now due to edge function issues

  constructor() {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    console.log('Initializing Gemini service with API key:', apiKey ? 'Present' : 'Missing');
    
    if (apiKey) {
      this.genAI = new GoogleGenerativeAI(apiKey);
      
      // Standard model for regular interactions
      this.model = this.genAI.getGenerativeModel({ 
        model: "models/gemini-2.5-flash",
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        },
      });
      
      // Pro model for deep analysis (use sparingly)
      this.proModel = this.genAI.getGenerativeModel({
        model: "models/gemini-2.5-pro",
        generationConfig: {
          temperature: 0.8,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        },
      });
      
      console.log('Gemini models initialized: Flash for regular use, Pro for deep analysis');
      
      // Test API connection
      this.testConnection();
    } else {
      console.warn('No Gemini API key found, will use mock responses');
    }

    // Initialize edge functions availability - disabled for now
    // this.initializeEdgeFunctions();
  }

  // Test the API connection
  private async testConnection() {
    try {
      console.log('Testing Gemini API connection...');
      const result = await this.model?.generateContent('Hello, respond with just "API Working"');
      const text = await result?.response.text();
      console.log('Gemini API test result:', text);
    } catch (error) {
      console.error('Gemini API test failed:', error);
      console.log('Will fallback to mock responses');
    }
  }

  // Initialize edge functions availability
  private async initializeEdgeFunctions() {
    try {
      this.useEdgeFunctions = await aiAnalysisEdgeService.isEdgeFunctionAvailable();
      if (this.useEdgeFunctions) {
        console.log('✅ Edge functions available - AI analysis will use Supabase Edge Functions');
      } else {
        console.log('⚠️ Edge functions not available - using client-side AI analysis');
      }
    } catch (error) {
      console.error('Error checking edge functions:', error);
      this.useEdgeFunctions = false;
    }
  }

  // Check if we can use Pro model (rate limiting)
  private canUseProModel(): boolean {
    const now = Date.now();
    if (this.rateLimited && now - this.lastRateLimitTime < 60000) { // 1 minute cooldown
      return false;
    }
    return true;
  }

  // Handle rate limiting errors
  private handleRateLimit() {
    this.rateLimited = true;
    this.lastRateLimitTime = Date.now();
    console.warn('⚠️ Gemini Pro rate limit hit, switching to Flash model');
  }

  // Phase 2: AI Deep Dive Conversation
  async conductFollowupConversation(
    structuredResponses: OnboardingStructuredResponse,
    conversationHistory: Array<{role: 'user' | 'ai', content: string}> = [],
    maxQuestions: number = 10
  ): Promise<AIFollowupResponse> {
    try {
      console.log('Conducting AI followup conversation...');
      
      if (!this.model) {
        return this.getMockFollowupResponse(conversationHistory.length);
      }

      const prompt = this.buildFollowupPrompt(structuredResponses, conversationHistory);
      const result = await this.model.generateContent(prompt);
      const response = await result.response.text();
      
      console.log('AI followup response:', response);
      
      // Parse AI response to extract structured data
      return this.parseFollowupResponse(response, conversationHistory.length, maxQuestions);
      
    } catch (error) {
      console.error('Error in followup conversation:', error);
      return this.getMockFollowupResponse(conversationHistory.length);
    }
  }

  private buildFollowupPrompt(
    responses: OnboardingStructuredResponse, 
    conversationHistory: Array<{role: 'user' | 'ai', content: string}>
  ): string {
    return `
You are an expert entrepreneurship mentor conducting a deep dive interview to build a comprehensive user profile.

STRUCTURED ONBOARDING DATA:
Track: ${responses.track}
Problem: ${responses.problemDescription}
Solution: ${responses.solutionApproach}
Target Audience: ${responses.targetAudience}
Technical Background: ${responses.technicalBackground}
Primary Goal: ${responses.primaryGoal}
Timeline: ${responses.timelineExpectation}
Biggest Challenge: ${responses.biggestChallenge}
Previous Experience: ${responses.previousExperience}
Learning Style: ${responses.learningStyle}

CONVERSATION HISTORY:
${conversationHistory.map(msg => `${msg.role.toUpperCase()}: ${msg.content}`).join('\n')}

YOUR TASK:
1. Ask ONE insightful follow-up question that will help you understand this entrepreneur better
2. Focus on areas that aren't clear from their responses
3. Dig deeper into their motivations, constraints, and specific challenges
4. Assess your satisfaction with the information gathered so far (0-100 score)
5. Identify what you still need to know to create a perfect profile

RESPONSE FORMAT (JSON only):
{
  "question": "Your next question here",
  "satisfactionScore": 75,
  "areasNeedingClarification": ["motivation", "market validation"],
  "keyInsightsExtracted": {
    "insight1": "They have strong technical skills but lack business experience",
    "insight2": "Market timing seems critical for their success"
  },
  "isComplete": false,
  "reasoning": "Why you chose this question and what you hope to learn"
}

If satisfactionScore >= 85, set isComplete: true and completionReason: "satisfied"

Ask questions that reveal:
- Deep motivations and driving forces
- Specific technical or business knowledge gaps  
- Hidden constraints or advantages
- Learning preferences and optimal support strategies
- Risk tolerance and decision-making style
- Network and resource accessibility
- Market understanding depth
- Execution approach and timeline realism

Make each question count. Be direct, insightful, and focused on building a complete picture.
`;
  }

  private parseFollowupResponse(response: string, questionCount: number, maxQuestions: number): AIFollowupResponse {
    try {
      // Try to extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        
        // Determine if conversation is complete
        const isComplete = parsed.satisfactionScore >= 85 || questionCount >= maxQuestions;
        const completionReason = parsed.satisfactionScore >= 85 ? 'satisfied' : 
                               questionCount >= maxQuestions ? 'max_questions' : undefined;

        return {
          question: parsed.question || "Tell me more about your long-term vision for this project.",
          satisfactionScore: Math.min(100, Math.max(0, parsed.satisfactionScore || 0)),
          areasNeedingClarification: parsed.areasNeedingClarification || [],
          keyInsightsExtracted: parsed.keyInsightsExtracted || {},
          isComplete,
          completionReason
        };
      }
    } catch (error) {
      console.error('Error parsing followup response:', error);
    }

    // Fallback response
    return {
      question: "What specific outcome would make you feel like this project was a complete success?",
      satisfactionScore: Math.min(questionCount * 10, 85),
      areasNeedingClarification: ['goals', 'success metrics'],
      keyInsightsExtracted: {},
      isComplete: questionCount >= maxQuestions,
      completionReason: questionCount >= maxQuestions ? 'max_questions' : undefined
    };
  }

  // Phase 3: Generate Comprehensive User Profile with Dev Journey integration
  async generateComprehensiveProfile(
    structuredResponses: OnboardingStructuredResponse,
    followupConversation: Array<{role: 'user' | 'ai', content: string}>
  ): Promise<ComprehensiveUserProfile> {
    try {
      console.log('Generating comprehensive user profile...');
      
      if (!this.model) {
        return this.getMockComprehensiveProfile(structuredResponses.track);
      }

      // Analyze user's dev journey stage
      const stageAnalysis = devJourneyService.analyzeUserStage({
        track: structuredResponses.track,
        problemDescription: structuredResponses.problemDescription,
        solutionApproach: structuredResponses.solutionApproach,
        primaryGoal: structuredResponses.primaryGoal,
        technicalBackground: structuredResponses.technicalBackground
      });

      // Get relevant dev journey content
      const devJourneyContext = devJourneyService.getContextualContent(stageAnalysis.current_stage, {
        track: structuredResponses.track,
        problemDescription: structuredResponses.problemDescription,
        primaryGoal: structuredResponses.primaryGoal,
        biggestChallenge: structuredResponses.biggestChallenge
      });

      const prompt = this.buildProfileGenerationPrompt(structuredResponses, followupConversation, stageAnalysis, devJourneyContext);
      const result = await this.model.generateContent(prompt);
      const response = await result.response.text();
      
      console.log('AI profile generation response:', response);
      
      const profile = this.parseComprehensiveProfile(response);
      // Add stage analysis to profile
      (profile as any).dev_journey_stage = stageAnalysis.current_stage;
      (profile as any).stage_confidence = stageAnalysis.confidence_score;
      (profile as any).stage_reasoning = stageAnalysis.reasoning;
      
      return profile;
      
    } catch (error) {
      console.error('Error generating comprehensive profile:', error);
      return this.getMockComprehensiveProfile(structuredResponses.track);
    }
  }

  // Get user's current dev journey stage from authoritative source
  async analyzeUserStage(userId: string): Promise<UserStageAnalysis> {
    try {
      // Get authoritative stage data from database (same source as dashboard)
      const { data: stageData, error } = await supabase
        .from('user_dev_journey_stages')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (error) {
        console.error('Error fetching user stage:', error);
        throw error;
      }

      if (stageData) {
        // Use the authoritative stage data from database
        console.log(`🎯 AI Helper using authoritative stage: ${stageData.stage_id} (confidence: ${stageData.confidence_score}%)`);

        return {
          current_stage: stageData.stage_id,
          confidence_score: stageData.confidence_score,
          reasoning: stageData.reasoning || 'Current stage from user progress tracking',
          next_actions: stageData.next_actions || [],
          applicable_frameworks: stageData.applicable_frameworks || []
        };
      } else {
        // Fallback: analyze stage if no data exists yet
        console.log('🔍 No stage data found, performing initial analysis...');

        // Build user context for analysis
        const userContext = await userMemoryService.buildUserContext(userId);

        // Use dev journey service to analyze stage
        const stageAnalysis = devJourneyService.analyzeUserStage({
          currentProjectStage: userContext.project_info.current_focus,
          recentUpdates: userContext.recent_progress,
          primaryGoal: userContext.project_info.primary_goal,
          problemDescription: userContext.project_info.description,
          technicalBackground: userContext.project_info.technical_background
        });

        // Store the initial analysis in database for consistency
        await supabase
          .from('user_dev_journey_stages')
          .insert({
            user_id: userId,
            stage_id: stageAnalysis.current_stage,
            confidence_score: stageAnalysis.confidence_score,
            reasoning: stageAnalysis.reasoning,
            next_actions: stageAnalysis.next_actions,
            applicable_frameworks: stageAnalysis.applicable_frameworks
          });

        return stageAnalysis;
      }
    } catch (error) {
      console.error('Error analyzing user stage:', error);
      return {
        current_stage: 0,
        confidence_score: 50,
        reasoning: 'Unable to analyze stage due to insufficient data',
        next_actions: ['Complete your profile'],
        applicable_frameworks: []
      };
    }
  }

  // Get contextual help based on user's current situation
  async getContextualHelp(
    userId: string,
    question: string,
    context?: any
  ): Promise<string> {
    if (!this.model) {
      return "I'd be happy to help! However, the AI service is currently unavailable. Please check back later.";
    }

    try {
      // Get user memory and context
      const userMemory = await userMemoryService.getContextualMemory(userId, question);
      const userContext = await userMemoryService.buildUserContext(userId);
      
      // Analyze current stage
      const stageAnalysis = await this.analyzeUserStage(userId);
      
      // Get relevant frameworks
      const relevantFrameworks = devJourneyService.getRelevantFrameworks(stageAnalysis.current_stage, question);
      
      // Get dev journey content for current stage
      const devJourneyContent = devJourneyService.getContextualContent(stageAnalysis.current_stage, {
        problemDescription: userContext.project_info.description,
        biggestChallenge: userContext.key_challenges[0]
      });

      const prompt = `You are an expert mentor helping entrepreneurs navigate their product journey. Use the Pie Fi Product Journey framework to provide specific, actionable guidance.

USER QUESTION: ${question}

USER CONTEXT:
${userMemory}

CURRENT DEV JOURNEY STAGE:
${devJourneyContent}

RELEVANT FRAMEWORKS:
${Object.entries(relevantFrameworks).map(([name, desc]) => `${name}: ${desc}`).join('\n')}

INSTRUCTIONS:
1. Reference the user's specific situation and previous context
2. Apply relevant frameworks from their current Dev Journey stage
3. Give specific, actionable advice aligned with their stage
4. If applicable, mention what to focus on for their current stage vs future stages
5. Keep response concise but thorough (2-3 paragraphs)
6. Always ground advice in their actual progress and challenges

Provide a helpful, personalized response:`;

      const result = await this.model.generateContent(prompt);
      const response = result.response.text();

      // Store this interaction
      await userMemoryService.storeInteraction(userId, 'help_request', question, {
        stage: stageAnalysis.current_stage,
        frameworks_used: Object.keys(relevantFrameworks),
        response_summary: response.substring(0, 200)
      });

      return response;
    } catch (error) {
      console.error('Error getting contextual help:', error);
      return "I'm sorry, I encountered an error while processing your request. Please try again later.";
    }
  }

  private buildProfileGenerationPrompt(
    responses: OnboardingStructuredResponse,
    conversation: Array<{role: 'user' | 'ai', content: string}>,
    stageAnalysis?: UserStageAnalysis,
    devJourneyContext?: string
  ): string {
    return `
You are an expert entrepreneurship analyst. Create a comprehensive user profile for personalized dashboard experience.

STRUCTURED DATA:
${JSON.stringify(responses, null, 2)}

DEEP DIVE CONVERSATION:
${conversation.map(msg => `${msg.role.toUpperCase()}: ${msg.content}`).join('\n')}

DEV JOURNEY STAGE ANALYSIS:
${stageAnalysis ? `Current Stage: ${stageAnalysis.current_stage} (${stageAnalysis.confidence_score}% confidence)
Reasoning: ${stageAnalysis.reasoning}
Next Actions: ${stageAnalysis.next_actions.join(', ')}
Applicable Frameworks: ${stageAnalysis.applicable_frameworks.join(', ')}` : 'Not available'}

DEV JOURNEY CONTEXT:
${devJourneyContext || 'Not available'}

Create a comprehensive profile that integrates the Dev Journey framework and enables highly personalized mentoring and dashboard experience.

RESPONSE FORMAT (JSON only):
{
  "profileSummary": "2-3 sentence summary of this entrepreneur",
  "strengths": ["strength1", "strength2", "strength3"],
  "knowledgeGaps": ["gap1", "gap2", "gap3"],
  "personalityTraits": {
    "riskTolerance": "high/medium/low",
    "learningSpeed": "fast/moderate/methodical",
    "collaborationStyle": "independent/collaborative/guidance-seeking",
    "decisionMaking": "analytical/intuitive/consensus-driven",
    "resilience": "high/medium/low"
  },
  "projectViabilityScore": 75,
  "marketOpportunityAssessment": "Detailed assessment of market opportunity",
  "technicalFeasibilityAnalysis": "Analysis of technical feasibility and requirements",
  "recommendedNextSteps": ["step1", "step2", "step3"],
  "potentialBlockers": ["blocker1", "blocker2"],
  "optimalLearningPath": {
    "phase1": "Focus area for first 4 weeks",
    "phase2": "Focus area for weeks 5-8", 
    "phase3": "Focus area for weeks 9-12",
    "resourceTypes": ["videos", "mentorship", "hands-on"]
  },
  "mentorMatchingCriteria": {
    "industry": "required industry experience",
    "skills": ["skill1", "skill2"],
    "personalityMatch": "description of ideal mentor personality"
  },
  "resourcePreferences": {
    "contentTypes": ["articles", "videos", "podcasts", "tools"],
    "learningFormat": "self-paced/guided/group",
    "complexityLevel": "beginner/intermediate/advanced"
  },
  "communicationStrategy": {
    "frequency": "daily/weekly/bi-weekly",
    "style": "encouraging/direct/analytical",
    "channels": ["email", "app", "discord"]
  },
  "personalizedGoals": [
    {
      "title": "Goal 1",
      "description": "Detailed description",
      "timeframe": "2 weeks",
      "priority": "high",
      "category": "validation/technical/business"
    }
  ],
  "successProbabilityFactors": {
    "strengths": ["factor1", "factor2"],
    "advantages": ["advantage1", "advantage2"]
  },
  "riskFactors": {
    "high": ["major risk 1"],
    "medium": ["medium risk 1", "medium risk 2"],
    "low": ["minor risk 1"]
  },
  "preferredDashboardLayout": "simple/detailed/visual/metrics-heavy",
  "keyMetricsToTrack": ["metric1", "metric2", "metric3"],
  "notificationPreferences": {
    "goals": "daily/weekly",
    "resources": "weekly/monthly",
    "community": "real-time/daily/weekly"
  },
  "profileConfidenceScore": 90
}

Base recommendations on entrepreneurship best practices and their specific situation.
`;
  }

  private parseComprehensiveProfile(response: string): ComprehensiveUserProfile {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          profileSummary: parsed.profileSummary || "Motivated entrepreneur with strong potential",
          strengths: parsed.strengths || [],
          knowledgeGaps: parsed.knowledgeGaps || [],
          personalityTraits: parsed.personalityTraits || {},
          projectViabilityScore: parsed.projectViabilityScore || 70,
          marketOpportunityAssessment: parsed.marketOpportunityAssessment || "",
          technicalFeasibilityAnalysis: parsed.technicalFeasibilityAnalysis || "",
          recommendedNextSteps: parsed.recommendedNextSteps || [],
          potentialBlockers: parsed.potentialBlockers || [],
          optimalLearningPath: parsed.optimalLearningPath || {},
          mentorMatchingCriteria: parsed.mentorMatchingCriteria || {},
          resourcePreferences: parsed.resourcePreferences || {},
          communicationStrategy: parsed.communicationStrategy || {},
          personalizedGoals: parsed.personalizedGoals || [],
          successProbabilityFactors: parsed.successProbabilityFactors || {},
          riskFactors: parsed.riskFactors || {},
          preferredDashboardLayout: parsed.preferredDashboardLayout || 'detailed',
          keyMetricsToTrack: parsed.keyMetricsToTrack || [],
          notificationPreferences: parsed.notificationPreferences || {},
          profileConfidenceScore: parsed.profileConfidenceScore || 70
        };
      }
    } catch (error) {
      console.error('Error parsing comprehensive profile:', error);
    }

    return this.getMockComprehensiveProfile('builder');
  }

  // Mock responses for fallback
  private getMockFollowupResponse(questionCount: number): AIFollowupResponse {
    const questions = [
      "What drives you personally to work on this specific problem?",
      "What would you do if this approach doesn't work out?", 
      "How do you plan to validate your solution with real users?",
      "What resources or connections do you wish you had access to?",
      "How do you handle setbacks and maintain motivation?"
    ];

    return {
      question: questions[questionCount % questions.length],
      satisfactionScore: Math.min(questionCount * 15 + 20, 85),
      areasNeedingClarification: ['motivation', 'risk tolerance'],
      keyInsightsExtracted: { insight: "Strong technical background, needs business validation support" },
      isComplete: questionCount >= 4,
      completionReason: questionCount >= 4 ? 'satisfied' : undefined
    };
  }

  private getMockComprehensiveProfile(track: string): ComprehensiveUserProfile {
    return {
      profileSummary: `${track} track entrepreneur with strong potential for growth and learning.`,
      strengths: ["Technical aptitude", "Problem-solving mindset", "Learning oriented"],
      knowledgeGaps: ["Market validation", "Business model development", "User acquisition"],
      personalityTraits: {
        riskTolerance: "medium",
        learningSpeed: "fast", 
        collaborationStyle: "collaborative",
        decisionMaking: "analytical"
      },
      projectViabilityScore: 75,
      marketOpportunityAssessment: "Promising market with room for innovation",
      technicalFeasibilityAnalysis: "Technically feasible with current resources",
      recommendedNextSteps: ["Market research", "Build MVP", "User interviews"],
      potentialBlockers: ["Time constraints", "Technical complexity"],
      optimalLearningPath: {
        phase1: "Validation and market research",
        phase2: "MVP development", 
        phase3: "User acquisition and growth"
      },
      mentorMatchingCriteria: {
        industry: track + " domain expertise",
        skills: ["Product development", "Market validation"]
      },
      resourcePreferences: {
        contentTypes: ["videos", "articles", "tools"],
        learningFormat: "self-paced"
      },
      communicationStrategy: {
        frequency: "weekly",
        style: "encouraging"
      },
      personalizedGoals: [
        {
          title: "Validate problem-solution fit",
          description: "Conduct user interviews to validate your solution",
          timeframe: "2 weeks",
          priority: "high",
          category: "validation"
        }
      ],
      successProbabilityFactors: {
        strengths: ["Technical skills", "Learning mindset"],
        advantages: ["Clear problem focus", "Available time"]
      },
      riskFactors: {
        medium: ["Market competition", "Technical complexity"],
        low: ["Time management"]
      },
      preferredDashboardLayout: 'detailed' as const,
      keyMetricsToTrack: ["User interviews completed", "MVP progress", "Market feedback"],
      notificationPreferences: {
        goals: "weekly",
        resources: "weekly"
      },
      profileConfidenceScore: 80
    };
  }

  // Enhanced daily update analysis with Pie Fi coaching philosophy
  async analyzeDailyUpdate(content: string, context: any) {
    // Use edge functions if available
    if (this.useEdgeFunctions) {
      try {
        console.log('🚀 Using Supabase Edge Function for daily update analysis');
        return await aiAnalysisEdgeService.analyzeDailyUpdate(content, context);
      } catch (error) {
        console.error('Edge function failed, falling back to client-side:', error);
        // Fall through to client-side analysis
      }
    }

    if (!this.model) {
      return this.getMockDailyAnalysis();
    }

    try {
      // Get stage-specific analysis context
      const stageInfo = devJourneyService.getStage(context.devJourneyStage || 0);
      const stageContent = devJourneyService.getContextualContent(context.devJourneyStage || 0, {
        problemDescription: context.userContext?.project_info?.description,
        biggestChallenge: context.currentChallenges?.[0]
      });

      const prompt = `You are a Pie Fi Product Journey coach. Your philosophy: "Challenge the idea without judgment" and make builders think deeply about what they're working on.

DAILY UPDATE: "${content}"

USER CONTEXT:
- Track: ${context.track}
- Current Dev Journey Stage: ${context.stageTitle || 'Unknown'}
- Project Stage: ${context.projectStage || 'Unknown'}
- Available Frameworks: ${context.stageFrameworks?.join(', ') || 'None'}
- Current Challenges: ${context.currentChallenges?.join(', ') || 'None'}

STAGE CONTEXT:
${stageContent}

COACHING PHILOSOPHY:
Your role is to be a thoughtful challenger who helps builders create better solutions. Don't just cheerleader - dig deeper. Ask the hard questions. Make them think about what they're really solving and why it matters.

ANALYSIS INSTRUCTIONS:
1. Identify concrete achievements vs. just activities
2. Challenge assumptions and surface blind spots
3. Suggest next actions that push toward real validation
4. If the update is vague or surface-level, ask follow-up questions
5. Connect their work to actual user value and market validation

RESPONSE FORMAT (JSON only):
{
  "sentiment": 0.7,
  "progress": 0.8,
  "achievements": ["specific achievement 1", "specific achievement 2"],
  "blockers": ["specific blocker or assumption to challenge"],
  "nextActions": ["actionable next step 1", "actionable next step 2"],
  "coachingInsight": "Here's what I'm seeing... But let me challenge you on this: Are you building what people actually want, or what you think they want? [Specific challenging question based on their update]",
  "needsFollowUp": true,
  "followUpQuestions": [
    "Who specifically have you talked to that confirmed they need this?",
    "What evidence do you have that this technical approach solves their real problem?"
  ],
  "encouragement": "I can see you're making real progress on [specific thing]. Now let's push deeper on [specific challenge].",
  "stageProgression": "You're showing signs of [stage] completion, but I want to see more evidence of [specific missing element]"
}

Be direct but supportive. Challenge their thinking. Don't let them coast on technical work without validating real user value.`;

      const result = await this.model.generateContent(prompt);
      const response = result.response.text();

      try {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const analysis = JSON.parse(jsonMatch[0]);
          
          // Add rate limit info for user
          if (this.rateLimited) {
            analysis.rateLimitWarning = "⚠️ Using standard analysis due to rate limits.";
          }
          
          return analysis;
        }
      } catch (parseError) {
        console.warn('Failed to parse daily analysis, using mock');
      }

      return this.getMockDailyAnalysis();
    } catch (error: any) {
      if (error.message?.includes('429') || error.message?.includes('rate')) {
        this.handleRateLimit();
      }
      console.error('Error analyzing daily update:', error);
      return this.getMockDailyAnalysis();
    }
  }

  // Generate professional AI-enhanced sections for the investor dashboard
  async generateProfessionalSections(
    userId: string,
    rawData: {
      onboarding: any;
      profile: any;
      submissions: any[];
      userContext: any;
      memories: any[];
      achievements: any[];
    }
  ) {
    if (!this.model) {
      return this.getMockProfessionalSections();
    }

    try {
      console.log('🤖 Generating professional sections with AI...');

      const prompt = `
You are an expert business analyst and technical writer. Transform the raw user data below into professional, investor-ready summaries while maintaining authenticity and accuracy.

**Raw User Data:**
${JSON.stringify(rawData, null, 2)}

**Instructions:**
1. Create professional, polished summaries that maintain the user's authentic voice and vision
2. Enhance clarity and business language without changing core meaning
3. Extract and synthesize key insights from submissions and achievements
4. Make informal language more professional while preserving authenticity
5. Ensure all claims are supported by the actual data provided

**Required Output Format (JSON):**
{
  "executiveSummary": {
    "problemStatement": "Professional 2-3 sentence summary of the problem being solved",
    "solutionApproach": "Clear, professional description of the solution approach",
    "targetAudience": "Well-defined target market description",
    "uniqueValueProposition": "Compelling value proposition statement",
    "marketOpportunity": "Professional market opportunity description",
    "technicalFeasibility": "Technical approach and feasibility assessment"
  },
  "marketValidation": {
    "validationSummary": "Professional summary of validation efforts and results",
    "userFeedbackSynthesis": "Synthesized insights from user feedback",
    "marketTraction": "Professional description of market traction and opportunity",
    "competitiveAdvantage": "Clear competitive positioning statement"
  },
  "technicalProgress": {
    "architectureSummary": "Professional technical architecture overview",
    "developmentProgress": "Clear summary of technical milestones and progress",
    "codeQualityAssessment": "Professional assessment of technical quality",
    "scalabilityStrategy": "Technical scalability and growth strategy"
  },
  "teamAndExecution": {
    "executionCapability": "Professional assessment of team execution ability",
    "keyStrengths": "List of demonstrated team strengths",
    "developmentVelocity": "Professional description of development pace",
    "consistencyAndReliability": "Assessment of team consistency and reliability"
  },
  "fundingAndGrowth": {
    "fundingNeeds": "Professional funding requirements and use of funds",
    "growthStrategy": "Clear growth and scaling strategy",
    "milestoneRoadmap": "Professional roadmap of upcoming milestones",
    "investmentOpportunity": "Compelling investment opportunity summary"
  }
}

Generate professional, investor-ready content that enhances the user's vision while staying true to their actual progress and achievements.`;

      const result = await this.model.generateContent(prompt);
      const response = await result.response.text();

      console.log('AI professional sections response:', response);

      try {
        // Clean the response by removing markdown code blocks if present
        let cleanedResponse = response.trim();
        if (cleanedResponse.startsWith('```json')) {
          cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (cleanedResponse.startsWith('```')) {
          cleanedResponse = cleanedResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        const sections = JSON.parse(cleanedResponse);
        sections.generatedAt = new Date().toISOString();
        return sections;
      } catch (parseError) {
        console.error('Error parsing professional sections JSON:', parseError);
        return this.getMockProfessionalSections();
      }
    } catch (error) {
      console.error('Error generating professional sections:', error);
      return this.getMockProfessionalSections();
    }
  }

  // Generate comprehensive investor insights
  async generateInvestorInsights(
    userId: string,
    dashboardData: {
      progressMetrics: any;
      milestoneAchievements: any[];
      stageBreakdown: any[];
      marketValidation: any;
      technicalProgress: any;
      fundingNeeds: any;
      teamAndExecution: any;
    },
    userContext: any
  ) {
    if (!this.model) {
      return this.getMockInvestorInsights();
    }

    try {
      console.log('🤖 Generating investor insights with AI...');

      const prompt = `
You are an expert AI investment analyst evaluating a startup for potential investment. Analyze the comprehensive data below and provide detailed insights.

STARTUP DATA:
Progress Metrics:
- Overall Completion: ${dashboardData.progressMetrics.overallCompletion}%
- Current Stage: ${dashboardData.progressMetrics.currentStage}
- Completed Milestones: ${dashboardData.progressMetrics.completedMilestones}/${dashboardData.progressMetrics.totalMilestones}
- AI Confidence Average: ${dashboardData.progressMetrics.aiConfidenceAverage}%
- Development Velocity: ${dashboardData.progressMetrics.developmentVelocity} milestones/week
- Consistency Score: ${dashboardData.progressMetrics.consistencyScore}%
- Project Viability Score: ${dashboardData.progressMetrics.projectViabilityScore}%

Milestone Achievements: ${dashboardData.milestoneAchievements.length} completed
Recent Achievements: ${dashboardData.milestoneAchievements.slice(-3).map(m => m.title).join(', ')}

Market Validation:
- User Feedback: ${dashboardData.marketValidation.userFeedback.length} pieces
- Validation Evidence: ${dashboardData.marketValidation.validationEvidence.length} items
- Market Traction: ${dashboardData.marketValidation.marketTraction}
- Competitive Advantage: ${dashboardData.marketValidation.competitiveAdvantage}

Technical Progress:
- Architecture Decisions: ${dashboardData.technicalProgress.architectureDecisions.length}
- Technical Milestones: ${dashboardData.technicalProgress.technicalMilestones.length}
- Code Quality: ${dashboardData.technicalProgress.codeQuality}
- Scalability Plan: ${dashboardData.technicalProgress.scalabilityPlan}

Team & Execution:
- Founder Background: ${dashboardData.teamAndExecution.founderBackground}
- Execution Capability: ${dashboardData.teamAndExecution.executionCapability}
- Learning Velocity: ${dashboardData.teamAndExecution.learningVelocity}
- Adaptability: ${dashboardData.teamAndExecution.adaptability}

ANALYSIS REQUIREMENTS:
Provide a comprehensive investment analysis in the following JSON format:

{
  "riskAssessment": {
    "executionRisk": {
      "level": "Low|Medium|High",
      "reasoning": "Detailed analysis of execution capabilities, development velocity, and milestone completion patterns",
      "confidence": 0-100
    },
    "marketRisk": {
      "level": "Low|Medium|High",
      "reasoning": "Analysis of market validation, user feedback, competitive positioning, and market traction",
      "confidence": 0-100
    },
    "technicalRisk": {
      "level": "Low|Medium|High",
      "reasoning": "Assessment of technical architecture, scalability, code quality, and technical milestone completion",
      "confidence": 0-100
    }
  },
  "growthPotential": {
    "marketPenetration": "Analysis of market penetration potential based on validation data and user feedback",
    "scalingCapability": "Assessment of technical and operational scaling potential",
    "trajectoryAnalysis": "Analysis of current progress trajectory and future projections",
    "overallPrediction": "Comprehensive growth potential summary with specific predictions"
  },
  "investmentReadiness": {
    "score": 0-100,
    "reasoning": "Comprehensive assessment of investment readiness",
    "keyStrengths": ["strength1", "strength2", "strength3"],
    "areasForImprovement": ["area1", "area2", "area3"]
  },
  "confidence": 0-100
}

Focus on:
1. Data-driven analysis based on actual metrics and achievements
2. Realistic risk assessment considering stage of development
3. Growth potential based on validation evidence and technical progress
4. Investment readiness considering team execution and market opportunity
5. Specific, actionable insights rather than generic statements

Respond with ONLY the JSON object, no additional text.`;

      const result = await this.model.generateContent(prompt);
      const response = await result.response.text();

      console.log('AI investor insights response:', response);

      try {
        // Clean the response by removing markdown code blocks if present
        let cleanedResponse = response.trim();
        if (cleanedResponse.startsWith('```json')) {
          cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (cleanedResponse.startsWith('```')) {
          cleanedResponse = cleanedResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        const insights = JSON.parse(cleanedResponse);
        insights.generatedAt = new Date().toISOString();
        return insights;
      } catch (parseError) {
        console.error('Error parsing investor insights JSON:', parseError);
        return this.getMockInvestorInsights();
      }

    } catch (error) {
      console.error('Error generating investor insights:', error);
      return this.getMockInvestorInsights();
    }
  }

  // New method for comprehensive progress analysis
  async analyzeComprehensiveProgress(
    userId: string,
    combinedUpdates: string,
    context: {
      track?: string;
      profileData?: any;
      recentUpdates?: any[];
      currentStage?: number;
    }
  ): Promise<{
    suggested_stage: number;
    confidence_score: number;
    reasoning: string;
    next_actions: string[];
    applicable_frameworks: string[];
    detected_milestones: string[];
  } | null> {
    
    if (!this.model) {
      return this.getMockComprehensiveAnalysis(context.currentStage || 0);
    }

    try {
      console.log('🔍 Analyzing comprehensive progress...');
      
      const currentStageInfo = devJourneyService.getStage(context.currentStage || 0);
      const allStages = devJourneyService.getAllStages();
      
      const prompt = `You are an expert product development mentor analyzing a user's complete progress to determine their current Dev Journey stage.

CURRENT STAGE: ${currentStageInfo?.title || 'Unknown'} (Stage ${context.currentStage})

ALL RECENT UPDATES COMBINED:
${combinedUpdates}

USER CONTEXT:
- Track: ${context.track || 'Unknown'}
- Profile Summary: ${context.profileData?.profile_summary || 'None'}
- Recent Updates Count: ${context.recentUpdates?.length || 0}

DEV JOURNEY STAGES FOR REFERENCE:
${allStages.map(stage => `Stage ${stage.id}: ${stage.title} - ${stage.description}`).join('\n')}

ANALYSIS TASK:
Carefully analyze ALL the content to determine:
1. What stage they're ACTUALLY at based on what they've accomplished
2. Look for stage completion indicators like "finished stage X", "completed", "done with"
3. Look for milestone achievements (team building, planning, building, shipping, etc.)
4. Consider natural progression vs stated progression

IMPORTANT STAGE INDICATORS TO LOOK FOR:
- Stage 0 (Spark): "idea", "problem identified", "researching"
- Stage 1 (Formation): "team", "planned out", "roadmap", "vision clear", "teammates", "partners"
- Stage 2 (First Build): "building", "developing", "coding", "prototype", "mvp"
- Stage 3 (Ship): "launched", "live", "users", "shipped", "released"
- Stage 4 (Traction): "growth", "users growing", "metrics", "scaling"
- Stage 5 (Real Company): "revenue", "business model", "company", "team structure"

Pay special attention to phrases like:
- "finished stage 1" or "completed stage X"
- "got a new teammate" (indicates Formation stage progress)
- "everything planned out" (indicates Formation completion)
- "technical challenges" with specific technologies (indicates active building)

RESPONSE FORMAT (JSON only):
{
  "suggested_stage": 2,
  "confidence_score": 85,
  "reasoning": "User explicitly stated 'finished stage 1' and 'everything planned out', plus mentioned getting a new teammate. These are clear Formation stage completions. Technical challenges mentioned suggest they're now in First Build stage.",
  "next_actions": ["Start MVP development", "Set up development workflow", "Define core features"],
  "applicable_frameworks": ["MVP Development", "Technical Architecture"],
  "detected_milestones": ["team_building", "milestone_planning", "concept_clarity"],
  "stage_advancement_reasoning": "Clear indicators of Formation stage completion with evidence of moving into building phase"
}

Be especially sensitive to explicit stage completion language. If someone says "finished stage 1", take that seriously and consider advancement.`;

      const result = await this.model.generateContent(prompt);
      const response = await result.response.text();
      
      console.log('🤖 Comprehensive analysis response:', response);
      
      try {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const analysis = JSON.parse(jsonMatch[0]);
          
          // Store the analysis
          await userMemoryService.storeInsight(
            userId,
            `Comprehensive analysis: ${analysis.reasoning}`,
            'comprehensive_stage_analysis',
            analysis.confidence_score
          );
          
          return analysis;
        }
      } catch (parseError) {
        console.error('Error parsing comprehensive analysis:', parseError);
      }

      return this.getMockComprehensiveAnalysis(context.currentStage || 0);
      
    } catch (error) {
      console.error('Error in comprehensive progress analysis:', error);
      return this.getMockComprehensiveAnalysis(context.currentStage || 0);
    }
  }

  private getMockComprehensiveAnalysis(currentStage: number) {
    return {
      suggested_stage: Math.min(currentStage + 1, 5),
      confidence_score: 75,
      reasoning: "Mock analysis suggests potential for stage advancement based on progress patterns",
      next_actions: ["Continue current progress", "Focus on next stage milestones"],
      applicable_frameworks: ["General Framework"],
      detected_milestones: ["general_progress"]
    };
  }

  // Legacy methods for backward compatibility
  async processOnboardingResponse(track: string, response: string, context: any) {
    return this.getMockOnboardingResponse();
  }

  private getMockOnboardingResponse() {
    return {
      question: "That's interesting! Can you tell me more about your target users?",
      insights: "Great progress on understanding your problem space."
    };
  }

  private getMockDailyAnalysis() {
    return {
      sentiment: 0.7,
      progress: 0.8,
      achievements: ["Made good progress"],
      blockers: [],
      nextActions: ["Continue current approach"],
      goalUpdates: [
        { status: "progress", title: "Continue building", priority: "high" }
      ],
      insights: "Keep up the great work!",
      encouragement: "Great progress today! 🚀"
    };
  }

  private getMockProfessionalSections() {
    return {
      executiveSummary: {
        problemStatement: "Addressing a significant market need with innovative technology solutions that improve efficiency and user experience.",
        solutionApproach: "Comprehensive platform leveraging modern technology stack to deliver scalable, user-centric solutions.",
        targetAudience: "Well-defined target market segment with validated demand and clear value proposition alignment.",
        uniqueValueProposition: "Unique competitive advantage through innovative approach and superior user experience design.",
        marketOpportunity: "Significant market opportunity with measurable impact potential and clear path to monetization.",
        technicalFeasibility: "Proven technical approach with validated architecture and clear development roadmap."
      },
      marketValidation: {
        validationSummary: "Early-stage validation efforts showing promising market response and user engagement.",
        userFeedbackSynthesis: "Positive user feedback indicating strong product-market fit potential and user satisfaction.",
        marketTraction: "Building momentum with early adopters and demonstrating clear value delivery.",
        competitiveAdvantage: "Differentiated positioning with unique value proposition and competitive moat development."
      },
      technicalProgress: {
        architectureSummary: "Robust technical architecture designed for scalability and maintainability.",
        developmentProgress: "Steady technical progress with key milestones achieved and clear development velocity.",
        codeQualityAssessment: "Maintaining high code quality standards with best practices implementation.",
        scalabilityStrategy: "Comprehensive scalability plan with infrastructure and architecture considerations."
      },
      teamAndExecution: {
        executionCapability: "Strong execution capability demonstrated through consistent milestone delivery.",
        keyStrengths: ["Technical expertise", "Market understanding", "Execution velocity"],
        developmentVelocity: "Consistent development pace with regular milestone completion.",
        consistencyAndReliability: "Reliable execution with adaptability to market feedback and requirements."
      },
      fundingAndGrowth: {
        fundingNeeds: "Strategic funding requirements aligned with growth objectives and market expansion.",
        growthStrategy: "Clear growth strategy with defined milestones and market penetration approach.",
        milestoneRoadmap: "Well-defined roadmap with achievable milestones and clear success metrics.",
        investmentOpportunity: "Compelling investment opportunity with strong fundamentals and growth potential."
      },
      generatedAt: new Date().toISOString()
    };
  }

  private getMockInvestorInsights() {
    return {
      riskAssessment: {
        executionRisk: {
          level: "Medium",
          reasoning: "Demonstrating steady progress with consistent milestone completion. Development velocity shows promise but could benefit from acceleration.",
          confidence: 75
        },
        marketRisk: {
          level: "Medium",
          reasoning: "Early-stage market validation in progress. Limited user feedback data available, requiring more comprehensive market testing.",
          confidence: 70
        },
        technicalRisk: {
          level: "Low",
          reasoning: "Solid technical foundation with clear architecture decisions. Code quality standards maintained with scalable design approach.",
          confidence: 85
        }
      },
      growthPotential: {
        marketPenetration: "Moderate market penetration potential based on current validation efforts and user engagement patterns.",
        scalingCapability: "Strong technical scaling capability with well-designed architecture supporting future growth requirements.",
        trajectoryAnalysis: "Current progress trajectory indicates steady development with potential for acceleration given proper resource allocation.",
        overallPrediction: "Moderate to high growth potential with strong technical foundation and developing market validation."
      },
      investmentReadiness: {
        score: 72,
        reasoning: "Solid foundation with demonstrated execution capability. Strong technical progress with developing market validation.",
        keyStrengths: [
          "Consistent milestone completion",
          "Strong technical architecture",
          "Clear development roadmap"
        ],
        areasForImprovement: [
          "Expand market validation efforts",
          "Accelerate user feedback collection",
          "Strengthen go-to-market strategy"
        ]
      },
      confidence: 78,
      generatedAt: new Date().toISOString()
    };
  }

  // Analyze milestone submissions for comprehensive profile building
  async analyzeMilestoneSubmission(
    submissionContent: string,
    context: {
      milestoneTitle: string;
      milestoneDescription: string;
      stageId: number;
      stageTitle: string;
      userContext: any;
      projectContext: string;
      milestoneDefinition?: any;
    }
  ): Promise<{
    consolidatedFeedback: string;
    followUpQuestions: string[];
    confidence: number;
    keyLearnings: string[];
    nextSteps: string[];
    profileUpdates: any;
    // Legacy fields for backward compatibility
    insights?: string;
    coachingInsight?: string;
  } | null> {

    // Use edge functions if available
    if (this.useEdgeFunctions) {
      try {
        console.log('🚀 Using Supabase Edge Function for milestone analysis');
        return await aiAnalysisEdgeService.analyzeMilestoneSubmission(submissionContent, context);
      } catch (error) {
        console.error('Edge function failed, falling back to client-side:', error);
        // Fall through to client-side analysis
      }
    }

    if (!this.model) {
      return this.getMockMilestoneAnalysis();
    }

    try {
      console.log('🤖 Analyzing milestone submission with AI...');

      // Build milestone-specific prompt with detailed context
      const milestoneSpecificContext = context.milestoneDefinition ? `
MILESTONE-SPECIFIC CONTEXT:
${context.milestoneDescription}

This milestone specifically aims to validate:
${context.milestoneDefinition.validationPoints?.map((point: string) => `- ${point}`).join('\n') || '- General milestone completion'}

Key questions this milestone should address:
${context.milestoneDefinition.keyQuestions?.map((q: string) => `- ${q}`).join('\n') || '- Basic milestone requirements'}

Success criteria for this milestone:
${context.milestoneDefinition.successCriteria?.map((criteria: string) => `- ${criteria}`).join('\n') || '- Milestone completion'}
` : `
MILESTONE CONTEXT:
- Title: ${context.milestoneTitle}
- Description: ${context.milestoneDescription}
- Stage: ${context.stageId} - ${context.stageTitle}
`;

      const prompt = `You are a Pie Fi Product Journey coach analyzing a milestone submission. Your philosophy: "Challenge the idea without judgment" and help builders think deeply about what they're working on.

${milestoneSpecificContext}

USER SUBMISSION:
"${submissionContent}"

PROJECT CONTEXT: ${context.projectContext}

USER BACKGROUND:
${JSON.stringify(context.userContext, null, 2)}

ANALYSIS TASK:
1. Evaluate how well the submission addresses the SPECIFIC milestone requirements above
2. Check if the submission demonstrates understanding of the milestone's learning objectives
3. Assess whether the user has provided evidence for the milestone's validation points
4. Generate follow-up questions that are SPECIFIC to this milestone's success criteria
5. Provide coaching that helps them think deeper about THIS milestone's goals

Focus your analysis on:
- Does the submission address the milestone-specific validation points?
- Are the milestone's key questions adequately answered?
- What specific evidence is missing for this milestone?
- How can they better demonstrate mastery of this milestone's objectives?

Respond with JSON in this exact format:
{
  "consolidatedFeedback": "Milestone-specific feedback focusing on the learning objectives and success criteria for this particular milestone",
  "followUpQuestions": ["Specific question about this milestone's validation points", "Question targeting this milestone's success criteria"],
  "confidence": 85,
  "keyLearnings": ["learning specific to this milestone", "insight about milestone mastery"],
  "nextSteps": ["actionable step for this milestone", "next milestone preparation"],
  "profileUpdates": {
    "newSkills": ["skill demonstrated in this milestone"],
    "updatedChallenges": ["challenge revealed by this milestone"],
    "strengthsRevealed": ["strength shown in milestone completion"]
  }
}

Be direct but supportive. Focus on milestone-specific depth rather than generic feedback. Only include follow-up questions that help validate THIS milestone's specific requirements.`;

      const result = await this.model.generateContent(prompt);
      const response = await result.response.text();

      console.log('🧠 Milestone analysis response:', response.substring(0, 200) + '...');

      try {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const analysis = JSON.parse(jsonMatch[0]);

          // Ensure backward compatibility by providing legacy fields
          if (analysis.consolidatedFeedback && !analysis.insights) {
            analysis.insights = analysis.consolidatedFeedback;
            analysis.coachingInsight = analysis.consolidatedFeedback;
          }

          // Ensure followUpQuestions is always an array
          if (!analysis.followUpQuestions) {
            analysis.followUpQuestions = [];
          }

          return analysis;
        }
      } catch (parseError) {
        console.error('Error parsing milestone analysis:', parseError);
      }

      return this.getMockMilestoneAnalysis();

    } catch (error) {
      console.error('Error analyzing milestone submission:', error);
      return this.getMockMilestoneAnalysis();
    }
  }

  private getMockMilestoneAnalysis() {
    return {
      consolidatedFeedback: "Good progress on this milestone. Your submission shows thoughtful consideration of the requirements. Consider diving deeper into the user impact - how does this move you closer to solving a real problem?",
      followUpQuestions: ["What specific user problem does this milestone help solve?", "How will you measure the success of this approach?"],
      confidence: 75,
      keyLearnings: ["Demonstrated understanding of milestone requirements", "Shows progress in current stage"],
      nextSteps: ["Continue building on this foundation", "Focus on user validation"],
      profileUpdates: {
        newSkills: ["milestone_completion"],
        updatedChallenges: [],
        strengthsRevealed: ["systematic_progress"]
      },
      // Legacy fields for backward compatibility
      insights: "Good progress on this milestone. Your submission shows thoughtful consideration of the requirements.",
      coachingInsight: "Great work! Consider diving deeper into the user impact of this milestone. How does this move you closer to solving a real problem?"
    };
  }

  // Continue coaching conversation for milestone submissions
  async continueCoachingConversation(
    conversationHistory: string,
    context: {
      milestoneId: string;
      stageId: number;
      originalSubmission: string;
      currentConfidence: number;
      questionsAnswered: number;
      totalQuestions: number;
    }
  ): Promise<{
    response: string;
    confidence: number;
    isComplete: boolean;
    enhancedFeedback?: string;
    conversationSummary?: string;
    keyInsights?: string[];
  } | null> {

    if (!this.model) {
      return this.getMockCoachingResponse(context);
    }

    try {
      console.log('🤖 Continuing coaching conversation...');

      const prompt = `You are a Pie Fi Product Journey coach continuing a coaching conversation about a milestone submission. Your philosophy: "Challenge the idea without judgment" and help builders think deeply.

CONTEXT:
- Milestone: ${context.milestoneId}
- Stage: ${context.stageId}
- Current Confidence: ${context.currentConfidence}%
- Questions Answered: ${context.questionsAnswered}/${context.totalQuestions}

ORIGINAL SUBMISSION:
"${context.originalSubmission}"

CONVERSATION HISTORY:
${conversationHistory}

INSTRUCTIONS:
1. Analyze the user's latest response in the conversation
2. Determine if you need more information or if you're satisfied
3. If confidence < 85%, ask 1-2 more specific follow-up questions
4. If confidence >= 85%, provide final enhanced feedback and summary
5. Be conversational, supportive, but challenging

Respond with JSON in this exact format:
{
  "response": "Your conversational response to the user",
  "confidence": 85,
  "isComplete": false,
  "enhancedFeedback": "Enhanced feedback if conversation is complete",
  "conversationSummary": "Summary of key insights gained if complete",
  "keyInsights": ["insight1", "insight2"]
}

Be direct but supportive. Focus on depth of thinking and real user value.`;

      const result = await this.model.generateContent(prompt);
      const response = await result.response.text();

      try {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const analysis = JSON.parse(jsonMatch[0]);
          return analysis;
        }
      } catch (parseError) {
        console.error('Error parsing coaching response:', parseError);
      }

      return this.getMockCoachingResponse(context);

    } catch (error) {
      console.error('Error in coaching conversation:', error);
      return this.getMockCoachingResponse(context);
    }
  }

  private getMockCoachingResponse(context: any) {
    const newConfidence = Math.min(context.currentConfidence + 15, 90);
    const isComplete = newConfidence >= 85;

    return {
      response: isComplete
        ? "Thank you for those insights! I now have a much clearer picture of your approach and thinking process. You've demonstrated solid understanding of this milestone."
        : "That's helpful context. Can you tell me more about how you validated this approach with potential users?",
      confidence: newConfidence,
      isComplete,
      enhancedFeedback: isComplete ? "Based on our conversation, your milestone submission shows solid thinking with good consideration of user needs and demonstrates mastery of the milestone objectives." : undefined,
      conversationSummary: isComplete ? "User demonstrated thoughtful approach to milestone with good user focus and validation mindset." : undefined,
      keyInsights: isComplete ? ["Shows user-centric thinking", "Demonstrates validation mindset", "Milestone objectives understood"] : undefined
    };
  }

  // Enhanced AI analysis for stage progress (uses Flash model for speed and rate limiting)
  async analyzeUpdateForStageProgress(
    userId: string,
    updateContent: string,
    currentStage: number,
    userContext: any
  ): Promise<{
    detectedMilestones: string[];
    stageProgression: 'backward' | 'current' | 'forward';
    suggestedStage?: number;
    coachingInsight: string;
    followUpQuestions?: string[];
    confidence: number;
  } | null> {

    // Use edge functions if available
    if (this.useEdgeFunctions) {
      try {
        console.log('🚀 Using Supabase Edge Function for stage progress analysis');
        return await aiAnalysisEdgeService.analyzeUpdateForStageProgress(userId, updateContent, currentStage, userContext);
      } catch (error) {
        console.error('Edge function failed, falling back to client-side:', error);
        // Fall through to client-side analysis
      }
    }

    // Always use Flash model for stage progress analysis for speed and reliability
    const modelToUse = this.model;
    const modelName = 'Flash';

    if (!modelToUse) {
      return this.getMockStageAnalysis();
    }

    try {
      console.log(`🤖 Using Gemini ${modelName} for stage progress analysis...`);
      
      const currentStageInfo = devJourneyService.getStage(currentStage);
      const allStages = devJourneyService.getAllStages();
      
      const prompt = this.buildCoachingStageAnalysisPrompt(
        updateContent,
        currentStage,
        currentStageInfo,
        allStages,
        userContext
      );

      const result = await modelToUse.generateContent(prompt);
      const response = await result.response.text();
      
      console.log(`🧠 ${modelName} stage analysis response:`, response.substring(0, 200) + '...');
      
      return this.parseStageAnalysisResponse(response);
      
    } catch (error: any) {
      if (error.message?.includes('429') || error.message?.includes('rate')) {
        console.warn('⚠️ Flash model rate limited, falling back to mock analysis');
      }
      console.error('Error in AI stage analysis:', error);
      return this.getMockStageAnalysis();
    }
  }

  private buildCoachingStageAnalysisPrompt(
    updateContent: string,
    currentStage: number,
    currentStageInfo: any,
    allStages: any[],
    userContext: any
  ): string {
    // Get current stage milestones for better detection
    const currentStageMilestones = stageProgressService.getStageMilestones(currentStage);
    const milestonesInfo = currentStageMilestones.map(m =>
      `- ${m.id}: ${m.title} - ${m.description} (Keywords: ${m.keywords.join(', ')})`
    ).join('\n');

    return `You are an expert product development coach using the Pie Fi Product Journey framework. Your role is to challenge without judgment and make the builder think deeply about their progress.

CURRENT UPDATE FROM BUILDER:
"${updateContent}"

CURRENT ASSESSMENT:
- Stage: ${currentStage} (${currentStageInfo?.title || 'Unknown'})
- Project: ${userContext?.project_info?.description || 'Not specified'}
- Recent Progress: ${userContext?.recent_progress?.slice(0, 2)?.join(', ') || 'Limited data'}

CURRENT STAGE MILESTONES TO DETECT:
${milestonesInfo}

PIE FI STAGES FOR REFERENCE:
${allStages.map(stage => `Stage ${stage.id}: ${stage.title} - ${stage.description}`).join('\n')}

COACHING PHILOSOPHY:
- Challenge the idea without judgment 
- Make them think deeply about what they're working on
- Help them create good solutions
- Focus on clarity and strategic thinking
- Always keep the human builder at the center

ANALYSIS TASK:
1. Carefully analyze the update content for evidence of milestone completion. Look for:
   - Direct mentions of activities (e.g., "added Ben to my team" = team_building)
   - Implicit accomplishments (e.g., "figured out our approach" = concept_clarity)
   - Progress indicators that match milestone descriptions
2. Based on ACTUAL accomplishments mentioned, what stage are they really at?
3. What hard questions should they be asking themselves right now?
4. What might they be avoiding or not considering?

MILESTONE DETECTION EXAMPLES:
- "added [name] to my team" or "recruited a developer" → team_building
- "defined our value proposition" or "clear on what we're building" → concept_clarity
- "talked to 5 potential users" or "got feedback from customers" → user_interviews
- "built the first version" or "MVP is ready" → mvp_designed

RESPONSE FORMAT (JSON only):
{
  "detectedMilestones": ["milestone_id_1", "milestone_id_2"],
  "stageProgression": "forward", // backward, current, or forward
  "suggestedStage": 2,
  "confidence": 85,
  "coachingInsight": "Here's what I'm seeing in your update... But here's what I want to challenge you on: Are you really solving a problem people will pay for? The technical challenges are real, but what about the market validation? Don't just build because you can - build because you must.",
  "followUpQuestions": [
    "What evidence do you have that people actually want this solution?",
    "How are you validating demand beyond technical feasibility?"
  ],
  "reasoning": "Builder mentioned team formation and technical challenges, indicating Formation completion and First Build entry. However, missing market validation evidence."
}

Be direct but supportive. Challenge their assumptions. Make them think harder about the real problems they're solving.`;
  }

  private parseStageAnalysisResponse(response: string) {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const analysis = JSON.parse(jsonMatch[0]);
        return {
          detectedMilestones: analysis.detectedMilestones || [],
          stageProgression: analysis.stageProgression || 'current',
          suggestedStage: analysis.suggestedStage,
          coachingInsight: analysis.coachingInsight || "Keep pushing forward with intentional progress.",
          followUpQuestions: analysis.followUpQuestions || [],
          confidence: analysis.confidence || 70
        };
      }
    } catch (parseError) {
      console.error('Error parsing stage analysis response:', parseError);
    }

    return this.getMockStageAnalysis();
  }

  private getMockStageAnalysis() {
    return {
      detectedMilestones: ["general_progress"],
      stageProgression: 'current' as const,
      suggestedStage: undefined,
      coachingInsight: "I see you're making progress, but let's dig deeper. What's the real problem you're solving here? Don't just tell me about the technical work - tell me about the human need.",
      followUpQuestions: ["What evidence do you have that people want this solution?"],
      confidence: 50
    };
  }
}

const geminiService = new GeminiService();
export default geminiService; 