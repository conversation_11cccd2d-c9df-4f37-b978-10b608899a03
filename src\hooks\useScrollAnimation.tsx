import { useInView } from 'framer-motion';
import { useRef, useState, useEffect, useCallback } from 'react';
import { Variants } from 'framer-motion';
import { useIsMobile } from "./use-mobile";

// Debounce utility for scroll events
const useDebounce = (value: boolean, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Check if device prefers reduced motion
const prefersReducedMotion = typeof window !== 'undefined' 
  ? window.matchMedia('(prefers-reduced-motion: reduce)').matches 
  : false;

// Basic scroll variants for animations
export const scrollVariants: Variants = {
  hidden: { 
    opacity: 0, 
    y: 50 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

// Stagger container for child animations
export const staggerContainer: Variants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
};

// Mobile-optimized variants (lighter animations)
export const mobileScrollVariants: Variants = {
  hidden: { 
    opacity: 0, 
    y: 20 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
};

export const useScrollAnimation = (options = {}) => {
  const ref = useRef(null);
  const isMobile = useIsMobile();
  const isTablet = typeof window !== "undefined" && window.innerWidth < 1024;

  // Use correct options structure for useInView
  const defaultOptions = {
    amount: isMobile ? 0.05 : isTablet ? 0.1 : 0.2, // More lenient thresholds
    margin: isMobile ? "-5% 0px -5% 0px" : "-10% 0px -10% 0px", // Start earlier
    // Don't use 'once' to prevent content disappearing
  };
  
  const mergedOptions = { ...defaultOptions, ...options };

  const observerInView = useInView(ref, mergedOptions);

  return {
    ref,
    isInView: observerInView,
    variants: isMobile ? mobileScrollVariants : scrollVariants
  };
};

// Utility function to get mobile-optimized animation variants
export const getResponsiveVariants = (isMobile: boolean): Variants => {
  return isMobile ? mobileScrollVariants : scrollVariants;
};

export const useSafeScrollAnimation = (options = {}) => {
  const ref = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const isMobile = typeof window !== "undefined" && window.innerWidth < 768;
  
  // Immediate visibility for reduced motion
  useEffect(() => {
    if (prefersReducedMotion) {
      setIsVisible(true);
    }
  }, []);

  const isInView = useInView(ref, {
    // Remove 'once' to prevent unloading
    amount: isMobile ? 0.01 : 0.05,
    margin: isMobile ? "50px" : "100px",
    ...options
  });

  // Once visible, stay visible
  useEffect(() => {
    if (isInView && !isVisible) {
      setIsVisible(true);
    }
  }, [isInView, isVisible]);

  // Aggressive fallback
  useEffect(() => {
    if (isVisible) return;
    
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, isMobile ? 200 : 500);

    return () => clearTimeout(timer);
  }, [isVisible, isMobile]);

  return { ref, isInView: isVisible || isInView };
};

// Mobile-optimized animation variants
const createResponsiveVariants = (baseVariants: Variants): Variants => {
  const isMobile = typeof window !== "undefined" && window.innerWidth < 768;
  const isTablet = typeof window !== "undefined" && window.innerWidth < 1024;
  
  if (prefersReducedMotion) {
    return {
      hidden: { opacity: 1 },
      visible: { opacity: 1, transition: { duration: 0 } }
    };
  }

  if (isMobile) {
    return {
      ...baseVariants,
      hidden: {
        ...baseVariants.hidden,
        y: Math.min((baseVariants.hidden as any)?.y || 0, 15), // Reduce movement
        scale: Math.max((baseVariants.hidden as any)?.scale || 1, 0.99), // Less scaling
      },
      visible: {
        ...baseVariants.visible,
        transition: {
          ...((baseVariants.visible as any)?.transition || {}),
          duration: 0.4, // Faster animations
          ease: "easeOut"
        }
      }
    };
  }

  if (isTablet) {
    return {
      ...baseVariants,
      visible: {
        ...baseVariants.visible,
        transition: {
          ...((baseVariants.visible as any)?.transition || {}),
          duration: 0.5,
          ease: "easeOut"
        }
      }
    };
  }

  return baseVariants;
};

export const slideInLeft: Variants = createResponsiveVariants({
  hidden: { opacity: 0, x: -30 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: { duration: 0.6, ease: [0.25, 0.46, 0.45, 0.94] }
  }
});

export const slideInRight: Variants = createResponsiveVariants({
  hidden: { opacity: 0, x: 30 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: { duration: 0.6, ease: [0.25, 0.46, 0.45, 0.94] }
  }
});

export const floatingVariants: Variants = createResponsiveVariants({
  animate: {
    y: [-5, 5, -5],
    rotate: [0, 2, -2, 0],
    transition: {
      duration: 6,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
});

export const scaleInVariants: Variants = createResponsiveVariants({
  hidden: { 
    opacity: 0, 
    scale: 0.95,
    y: 20
  },
  visible: { 
    opacity: 1, 
    scale: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
});

export const safeScrollVariants: Variants = createResponsiveVariants({
  hidden: { 
    opacity: 0.7, // Start with more opacity
    y: 15, // Less movement
    scale: 0.99 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: "easeOut"
    }
  }
});

export const immediateVariants: Variants = {
  hidden: { 
    opacity: 1,
    y: 0,
    scale: 1 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.1,
      ease: "easeOut"
    }
  }
};

// Mobile-specific variants for critical content
export const mobileOptimizedVariants: Variants = createResponsiveVariants({
  hidden: { 
    opacity: 0.9, // More visible initially
    y: 8, // Minimal movement
    scale: 0.99
  },
  visible: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: "easeOut"
    }
  }
});

// Persistent variants that don't reset animations
export const persistentVariants: Variants = createResponsiveVariants({
  hidden: { 
    opacity: 1, // Always visible to prevent resets
    y: 0,
    scale: 1
  },
  visible: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: "easeOut"
    }
  }
});
