-- Fix RLS policy for user creation during signup
DROP POLICY IF EXISTS "Users can insert own profile" ON users;

-- Allow users to insert their own profile OR service role to insert
CREATE POLICY "Users can insert own profile" ON users
  FOR INSERT WITH CHECK (
    auth.uid() = id OR 
    auth.role() = 'service_role' OR
    auth.role() = 'authenticated'
  );

-- Also ensure the read policy works correctly
DROP POLICY IF EXISTS "Users can read own profile" ON users;
CREATE POLICY "Users can read own profile" ON users
  FOR SELECT USING (
    auth.uid() = id OR 
    auth.role() = 'service_role'
  );

-- Update policy works correctly  
DROP POLICY IF EXISTS "Users can update own profile" ON users;
CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (
    auth.uid() = id OR 
    auth.role() = 'service_role'
  ); 