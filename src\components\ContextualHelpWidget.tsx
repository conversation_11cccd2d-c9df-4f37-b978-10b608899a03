import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  HelpCircle, 
  Brain, 
  X, 
  Send, 
  Loader2, 
  Lightbulb,
  Target,
  Users,
  DollarSign,
  TrendingUp,
  Book,
  MessageCircle,
  Sparkles
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import geminiService from '@/services/geminiService';
import devJourneyService from '@/services/devJourneyService';
import userMemoryService from '@/services/userMemoryService';
import { useAuth } from '@/hooks/useAuth';
import MarkdownText from './MarkdownText';

interface ContextualHelpWidgetProps {
  userStage?: number;
  currentContext?: string;
  className?: string;
}

const ContextualHelpWidget: React.FC<ContextualHelpWidgetProps> = ({
  userStage = 0,
  currentContext = '',
  className = ''
}) => {
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [question, setQuestion] = useState('');
  const [response, setResponse] = useState('');
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('ask');
  const [isClosing, setIsClosing] = useState(false);
  const chatScrollRef = useRef<HTMLDivElement>(null);
  const [conversationHistory, setConversationHistory] = useState<Array<{role: 'user' | 'ai', content: string}>>([]);

  // Get current stage info
  const stage = devJourneyService.getStage(userStage);
  const stageFrameworks = stage?.frameworks || {};
  
  // Framework icons mapping
  const frameworkIcons = {
    'Problem Validation': Target,
    'Jobs To Be Done (JTBD)': Users,
    'CAC Strategic Lens': DollarSign,
    'Growth Metrics': TrendingUp,
    'Learning Path': Book
  };

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (chatScrollRef.current && (loading || conversationHistory.length > 0)) {
      chatScrollRef.current.scrollTop = chatScrollRef.current.scrollHeight;
    }
  }, [loading, conversationHistory]);

  const handleAskQuestion = async () => {
    if (!question.trim() || !user) return;
    
    // Add user question to conversation
    const newHistory = [...conversationHistory, { role: 'user' as const, content: question }];
    setConversationHistory(newHistory);
    
    setLoading(true);
    setQuestion(''); // Clear input immediately
    
    try {
      // Get authoritative stage from database instead of prop
      const userContext = await userMemoryService.buildUserContext(user.id);
      const authoritativeStage = userContext.current_stage;

      console.log(`🎯 AI Helper using authoritative stage ${authoritativeStage} (prop: ${userStage})`);

      const helpResponse = await geminiService.getContextualHelp(
        user.id,
        question,
        { stage: authoritativeStage, context: currentContext }
      );
      
      // Add AI response to conversation
      setConversationHistory([...newHistory, { role: 'ai' as const, content: helpResponse }]);
      setResponse(helpResponse);
      setActiveTab('response');
    } catch (error) {
      console.error('Error getting help:', error);
      const errorMsg = 'Sorry, I encountered an error. Please try again.';
      setConversationHistory([...newHistory, { role: 'ai' as const, content: errorMsg }]);
      setResponse(errorMsg);
      setActiveTab('response');
    } finally {
      setLoading(false);
    }
  };

  const handleQuickQuestion = async (quickQuestion: string) => {
    if (!user) return;
    
    // Add quick question to conversation
    const newHistory = [...conversationHistory, { role: 'user' as const, content: quickQuestion }];
    setConversationHistory(newHistory);
    
    setLoading(true);
    try {
      // Get authoritative stage from database instead of prop
      const userContext = await userMemoryService.buildUserContext(user.id);
      const authoritativeStage = userContext.current_stage;

      const helpResponse = await geminiService.getContextualHelp(
        user.id,
        quickQuestion,
        { stage: authoritativeStage, context: currentContext }
      );
      
      // Add AI response to conversation
      setConversationHistory([...newHistory, { role: 'ai' as const, content: helpResponse }]);
      setResponse(helpResponse);
      setActiveTab('response');
    } catch (error) {
      console.error('Error getting help:', error);
      const errorMsg = 'Sorry, I encountered an error. Please try again.';
      setConversationHistory([...newHistory, { role: 'ai' as const, content: errorMsg }]);
      setResponse(errorMsg);
      setActiveTab('response');
    } finally {
      setLoading(false);
    }
  };

  // Quick questions based on current stage
  const getQuickQuestions = () => {
    const baseQuestions = [
      "What should I focus on next?",
      "Am I on the right track?",
      "What are common mistakes at this stage?"
    ];

    const stageSpecificQuestions = {
      0: ["How do I validate my idea?", "Where do I find potential users?"],
      1: ["How do I build a team?", "What planning should I do?"],
      2: ["What's the minimum viable product?", "How do I prioritize features?"],
      3: ["How do I get my first users?", "What metrics should I track?"],
      4: ["How do I scale effectively?", "What growth channels work?"],
      5: ["How do I build a sustainable business?", "When should I fundraise?"]
    };

    return [...baseQuestions, ...(stageSpecificQuestions[userStage as keyof typeof stageSpecificQuestions] || [])];
  };

  const resetConversation = () => {
    setConversationHistory([]);
    setResponse('');
    setQuestion('');
    setActiveTab('ask');
  };

  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);
    // Prevent window from closing when switching tabs
  };

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      setIsOpen(false);
      setIsClosing(false);
    }, 100);
  };

  return (
    <>
      {/* Floating Help Button */}
      <motion.div
        className={`fixed bottom-6 right-6 z-50 ${className}`}
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ delay: 1, duration: 0.5 }}
      >
        <motion.div
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => setIsOpen(true)}
          className="relative group cursor-pointer"
        >
          {/* Glowing background effect */}
          <motion.div 
            className="absolute inset-0 bg-gradient-to-r from-sauce-red/30 to-cheese-gold/30 rounded-full blur-xl"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          
          <div className="relative w-16 h-16 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-full flex items-center justify-center shadow-2xl backdrop-blur-sm border border-accent-white/20">
            <Brain className="h-8 w-8 text-white" />
          </div>
          
          {/* Pulse indicator */}
          <motion.div
            className="absolute -top-1 -right-1 w-4 h-4 bg-cheese-gold rounded-full"
            animate={{
              scale: [1, 1.5, 1],
              opacity: [1, 0, 1]
            }}
            transition={{
              duration: 2,
              repeat: Infinity
            }}
          />
        </motion.div>
      </motion.div>

      {/* Help Modal */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-oven-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setIsOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0, y: 50 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.8, opacity: 0, y: 50 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              onClick={(e) => e.stopPropagation()}
              className="w-full max-w-2xl max-h-[90vh] relative"
            >
              {/* Animated background glow */}
              <div className="absolute inset-0 bg-gradient-to-br from-sauce-red/20 via-cheese-gold/20 to-purple-500/20 rounded-3xl blur-2xl" />
              
              <Card className="relative bg-oven-black/95 backdrop-blur-xl border border-accent-white/20 shadow-2xl rounded-3xl overflow-hidden">
                {/* Header with animated background */}
                <div className="relative">
                  <motion.div 
                    className="absolute inset-0 bg-gradient-to-r from-sauce-red/10 via-cheese-gold/10 to-sauce-red/10"
                    animate={{
                      x: ['-100%', '100%'],
                    }}
                    transition={{
                      duration: 8,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                  />
                  
                  <CardHeader className="relative border-b border-accent-white/10">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <motion.div 
                          className="p-3 rounded-xl bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 backdrop-blur-sm"
                          animate={{ 
                            boxShadow: [
                              "0 0 20px rgba(255, 197, 61, 0.3)",
                              "0 0 40px rgba(255, 197, 61, 0.5)",
                              "0 0 20px rgba(255, 197, 61, 0.3)"
                            ]
                          }}
                          transition={{ duration: 3, repeat: Infinity }}
                        >
                          <Brain className="h-6 w-6 text-cheese-gold" />
                        </motion.div>
                        
                        <div>
                          <CardTitle className="text-xl text-accent-white font-bold">
                            AI Helper
                          </CardTitle>
                          <p className="text-accent-white/60 text-sm">
                            {stage ? `${stage.name} Stage Support` : 'Personalized Guidance'}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={resetConversation}
                          className="text-accent-white/60 hover:text-accent-white hover:bg-accent-white/10"
                        >
                          Clear
                        </Button>
                                                 <Button
                           variant="ghost"
                           size="sm"
                           onClick={handleClose}
                           className="text-accent-white/60 hover:text-accent-white hover:bg-accent-white/10"
                         >
                           <X className="h-4 w-4" />
                         </Button>
                      </div>
                    </div>
                  </CardHeader>
                </div>

                <CardContent className="p-0">
                                     <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
                    <TabsList className="grid w-full grid-cols-3 bg-transparent border-b border-accent-white/10 rounded-none">
                      <TabsTrigger 
                        value="ask" 
                        className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-sauce-red/20 data-[state=active]:to-cheese-gold/20 data-[state=active]:text-accent-white text-accent-white/60"
                      >
                        <MessageCircle className="h-4 w-4 mr-2" />
                        Ask
                      </TabsTrigger>
                      <TabsTrigger 
                        value="response" 
                        className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-sauce-red/20 data-[state=active]:to-cheese-gold/20 data-[state=active]:text-accent-white text-accent-white/60"
                      >
                        <Brain className="h-4 w-4 mr-2" />
                        Chat
                      </TabsTrigger>
                      <TabsTrigger 
                        value="frameworks" 
                        className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-sauce-red/20 data-[state=active]:to-cheese-gold/20 data-[state=active]:text-accent-white text-accent-white/60"
                      >
                        <Book className="h-4 w-4 mr-2" />
                        Tools
                      </TabsTrigger>
                    </TabsList>

                    {/* Ask Tab */}
                    <TabsContent value="ask" className="p-6 space-y-4">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <label className="text-accent-white/80 text-sm font-medium">Ask me anything about your journey:</label>
                          <Textarea
                            value={question}
                            onChange={(e) => setQuestion(e.target.value)}
                            placeholder="What specific guidance do you need right now?"
                            className="bg-oven-black/50 border-accent-white/20 text-accent-white placeholder:text-accent-white/40 min-h-[100px] resize-none focus:border-cheese-gold/50 focus:ring-cheese-gold/20"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' && !e.shiftKey) {
                                e.preventDefault();
                                handleAskQuestion();
                              }
                            }}
                          />
                        </div>
                        
                        <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                          <Button
                            onClick={handleAskQuestion}
                            disabled={!question.trim() || loading}
                            className="w-full bg-gradient-to-r from-sauce-red to-cheese-gold hover:from-sauce-red/90 hover:to-cheese-gold/90 text-white font-semibold h-12"
                          >
                            {loading ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                AI is thinking...
                              </>
                            ) : (
                              <>
                                <Send className="h-4 w-4 mr-2" />
                                Ask AI Helper
                              </>
                            )}
                          </Button>
                        </motion.div>
                      </div>

                      {/* Quick Questions */}
                      <div className="space-y-3">
                        <h4 className="text-accent-white/80 text-sm font-medium">Quick Questions:</h4>
                        <div className="grid gap-2">
                          {getQuickQuestions().map((q, idx) => (
                            <motion.button
                              key={idx}
                              whileHover={{ scale: 1.02, x: 5 }}
                              whileTap={{ scale: 0.98 }}
                              onClick={() => handleQuickQuestion(q)}
                              disabled={loading}
                              className="text-left p-3 rounded-xl bg-gradient-to-r from-accent-white/5 to-accent-white/10 backdrop-blur-sm border border-accent-white/10 text-accent-white/80 hover:text-accent-white hover:border-cheese-gold/30 transition-all duration-300 text-sm"
                            >
                              <Sparkles className="h-3 w-3 inline mr-2 text-cheese-gold" />
                              {q}
                            </motion.button>
                          ))}
                        </div>
                      </div>
                    </TabsContent>

                    {/* Response/Chat Tab */}
                    <TabsContent value="response" className="p-0">
                      <div className="h-[500px] flex flex-col">
                        {/* Chat History */}
                        <div 
                          ref={chatScrollRef}
                          className="flex-1 p-6 overflow-y-auto space-y-4 bg-gradient-to-b from-oven-black/50 to-oven-black/80"
                        >
                          {conversationHistory.length === 0 ? (
                            <div className="flex items-center justify-center h-full text-accent-white/60">
                              <div className="text-center space-y-2">
                                <Brain className="h-12 w-12 mx-auto text-cheese-gold/50" />
                                <p>Start a conversation to see your chat history here</p>
                              </div>
                            </div>
                          ) : (
                            conversationHistory.map((message, idx) => (
                              <motion.div
                                key={idx}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: idx * 0.1 }}
                                className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                              >
                                <div className={`max-w-[80%] rounded-2xl p-4 ${
                                  message.role === 'user' 
                                    ? 'bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 border border-sauce-red/30' 
                                    : 'bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-500/20'
                                }`}>
                                  <div className="flex items-center gap-2 mb-2">
                                    {message.role === 'user' ? (
                                      <div className="w-6 h-6 rounded-full bg-sauce-red/30 flex items-center justify-center">
                                        <span className="text-xs text-accent-white">You</span>
                                      </div>
                                    ) : (
                                      <div className="w-6 h-6 rounded-full bg-cheese-gold/30 flex items-center justify-center">
                                        <Brain className="h-3 w-3 text-cheese-gold" />
                                      </div>
                                    )}
                                    <span className="text-xs font-medium text-accent-white/80">
                                      {message.role === 'user' ? 'You' : 'AI Helper'}
                                    </span>
                                  </div>
                                                                     <MarkdownText className="text-sm text-accent-white/90 leading-relaxed">
                                     {message.content}
                                   </MarkdownText>
                                </div>
                              </motion.div>
                            ))
                          )}
                          
                          {/* Loading indicator */}
                          {loading && (
                            <motion.div
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="flex gap-3 justify-start"
                            >
                              <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-500/20 rounded-2xl p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <div className="w-6 h-6 rounded-full bg-cheese-gold/30 flex items-center justify-center">
                                    <Brain className="h-3 w-3 text-cheese-gold" />
                                  </div>
                                  <span className="text-xs font-medium text-accent-white/80">AI Helper</span>
                                </div>
                                <div className="flex items-center gap-2 text-accent-white/60">
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                  <span className="text-sm">Thinking...</span>
                                </div>
                              </div>
                            </motion.div>
                          )}
                        </div>

                        {/* Input area */}
                        <div className="p-4 border-t border-accent-white/10 bg-oven-black/80 backdrop-blur-sm">
                          <div className="flex gap-2">
                            <Textarea
                              value={question}
                              onChange={(e) => setQuestion(e.target.value)}
                              placeholder="Continue the conversation..."
                              className="flex-1 bg-oven-black/50 border-accent-white/20 text-accent-white placeholder:text-accent-white/40 resize-none min-h-[50px] max-h-[120px] focus:border-cheese-gold/50 focus:ring-cheese-gold/20"
                              onKeyDown={(e) => {
                                if (e.key === 'Enter' && !e.shiftKey) {
                                  e.preventDefault();
                                  handleAskQuestion();
                                }
                              }}
                            />
                            <Button
                              onClick={handleAskQuestion}
                              disabled={!question.trim() || loading}
                              size="sm"
                              className="bg-gradient-to-r from-sauce-red to-cheese-gold hover:from-sauce-red/90 hover:to-cheese-gold/90 px-4"
                            >
                              <Send className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </TabsContent>

                    {/* Frameworks Tab */}
                    <TabsContent value="frameworks" className="p-6 space-y-4">
                      <div className="space-y-4">
                        <h4 className="text-accent-white font-semibold">
                          Available Frameworks for {stage?.name || 'Your'} Stage:
                        </h4>
                        
                        {Object.keys(stageFrameworks).length > 0 ? (
                          <div className="grid gap-3">
                            {Object.entries(stageFrameworks).map(([name, description], idx) => {
                              const IconComponent = frameworkIcons[name as keyof typeof frameworkIcons] || Lightbulb;
                              return (
                                <motion.div
                                  key={idx}
                                  initial={{ opacity: 0, y: 20 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ delay: idx * 0.1 }}
                                  whileHover={{ scale: 1.02, x: 5 }}
                                  className="p-4 rounded-xl bg-gradient-to-r from-accent-white/5 to-accent-white/10 backdrop-blur-sm border border-accent-white/10 hover:border-cheese-gold/30 transition-all duration-300 cursor-pointer"
                                  onClick={() => handleQuickQuestion(`Tell me about ${name} and how to use it in my current situation`)}
                                >
                                  <div className="flex items-start gap-3">
                                    <div className="p-2 rounded-lg bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20">
                                      <IconComponent className="h-4 w-4 text-cheese-gold" />
                                    </div>
                                    <div className="flex-1">
                                      <h5 className="text-accent-white font-medium text-sm mb-1">{name}</h5>
                                      <p className="text-accent-white/70 text-xs leading-relaxed">{description}</p>
                                    </div>
                                  </div>
                                </motion.div>
                              );
                            })}
                          </div>
                        ) : (
                          <div className="text-center py-8 text-accent-white/60">
                            <Book className="h-12 w-12 mx-auto mb-4 text-cheese-gold/50" />
                            <p>No specific frameworks available for this stage yet.</p>
                            <p className="text-sm mt-2">Ask me anything about your current challenges!</p>
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default ContextualHelpWidget; 