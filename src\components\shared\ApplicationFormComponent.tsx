import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Send, Sparkles, User, Mail, School, Code, Lightbulb, Users, FileText, Linkedin, Upload, Globe, Phone, ChevronDown, Check, Key, Calendar, Target, AlertCircle, CheckCircle, MessageSquare } from 'lucide-react';
import { motion, useInView } from 'framer-motion';
import { supabase } from '@/integrations/supabase/client';
import { useIsMobile } from '@/hooks/use-mobile';
import { useToast } from '@/hooks/use-toast';

// Custom Mobile Select Component - Fixed positioning and performance
const MobileSelect = ({ value, onValueChange, placeholder, children, className, triggerClassName }: {
  value: string;
  onValueChange: (value: string) => void;
  placeholder: string;
  children: React.ReactNode;
  className?: string;
  triggerClassName?: string;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownRef, setDropdownRef] = useState<HTMLDivElement | null>(null);
  const [buttonRef, setButtonRef] = useState<HTMLButtonElement | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [openUpward, setOpenUpward] = useState(false);

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.matchMedia("(max-width: 768px)").matches);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef && !dropdownRef.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, dropdownRef]);

  // Check if dropdown should open upward
  const checkDropdownPosition = () => {
    if (!buttonRef) return;
    
    const buttonRect = buttonRef.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const dropdownHeight = 240; // approximate maximum height of list

    const spaceBelow = viewportHeight - buttonRect.bottom;
    const spaceAbove = buttonRect.top;

    // If not enough space below for full dropdown, but plenty above, open upward
    if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
      setOpenUpward(true);
    } else {
      setOpenUpward(false);
    }
  };

  const selectItems = React.Children.toArray(children);

  const handleSelect = (newValue: string) => {
    onValueChange(newValue);
    setIsOpen(false);
  };

  const handleToggle = () => {
    if (!isMobile) {
      // Always open upward on desktop to avoid submit button overlap
      setOpenUpward(true);
    } else {
      if (!isOpen) {
        checkDropdownPosition();
      }
    }
    setIsOpen(!isOpen);
  };

  const getSelectedLabel = () => {
    const selectedItem = selectItems.find((child: any) => 
      child.props && child.props.value === value
    );
    return selectedItem ? (selectedItem as any).props.children : placeholder;
  };

  if (!isMobile) {
    return (
      <div className={`relative ${className}`} ref={setDropdownRef}>
        <button
          ref={setButtonRef}
          type="button"
          onClick={handleToggle}
          className={`w-full px-3 py-2 text-left bg-gray-50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 flex items-center justify-between ${triggerClassName}`}
        >
          <span className={value ? "text-gray-900" : "text-gray-500"}>
            {getSelectedLabel()}
          </span>
          <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </button>
        
        {isOpen && (
          <div className={`absolute z-[99999] w-full bg-white border border-gray-200 rounded-lg shadow-xl max-h-48 overflow-auto ${
            openUpward ? 'bottom-full mb-1' : 'top-full mt-1'
          }`}>
            {selectItems.map((child: any, index) => (
              <button
                key={index}
                type="button"
                onClick={() => handleSelect(child.props.value)}
                className="w-full px-3 py-2 text-left text-gray-900 hover:bg-blue-50 hover:text-blue-900 focus:bg-blue-50 focus:text-blue-900 first:rounded-t-lg last:rounded-b-lg transition-colors duration-150"
              >
                {child.props.children}
              </button>
            ))}
          </div>
        )}
      </div>
    );
  }

  return (
    <select 
      value={value} 
      onChange={(e) => onValueChange(e.target.value)}
      className={`w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 ${triggerClassName}`}
      style={{ color: value ? '#111827' : '#6B7280' }} // Dark text for selected, gray for placeholder
    >
      <option value="" disabled style={{ color: '#6B7280' }}>{placeholder}</option>
      {selectItems.map((child: any, index) => (
        <option key={index} value={child.props.value} style={{ color: '#111827' }}>
          {child.props.children}
        </option>
      ))}
    </select>
  );
};

const SelectItem = ({ value, children }: { value: string; children: React.ReactNode }) => {
  return <option value={value}>{children}</option>;
};

// Enhanced error state management
interface FieldErrors {
  [key: string]: string | null;
}

// Standardized Required Indicator Component
const RequiredIndicator = ({ required }: { required: boolean }) => {
  if (!required) return null;
  return <span className="text-red-500 ml-1">*</span>;
};

// Field Error Display Component
const FieldError = ({ error }: { error: string | null }) => {
  if (!error) return null;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="flex items-center gap-1 text-red-600 text-sm mt-1"
    >
      <AlertCircle className="w-4 h-4 flex-shrink-0" />
      <span>{error}</span>
    </motion.div>
  );
};

// Field Success Display Component  
const FieldSuccess = ({ show }: { show: boolean }) => {
  if (!show) return null;
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      className="flex items-center gap-1 text-green-600 text-sm mt-1"
    >
      <Check className="w-4 h-4" />
      <span>Looks good!</span>
    </motion.div>
  );
};

interface ApplicationFormComponentProps {
  onSuccess?: () => void;
}

const ApplicationFormComponent = ({ onSuccess }: ApplicationFormComponentProps) => {
  const { toast } = useToast();
  const formRef = useRef(null);
  const formInView = useInView(formRef, { 
    amount: 0.05,
    margin: "50px 0px -50px 0px"
  });
  const isMobile = useIsMobile();
  
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    university: '',
    year: '',
    major: '',
    graduationYear: '',
    skills: '',
    projectIdea: '',
    teamStatus: '',
    whyPieFi: '',
    linkedin: '',
    website: '',
    github: '',
    previousProjects: '',
    availability: '',
    references: '',
    accessCode: ''
  });

  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<FieldErrors>({});
  const [fieldTouched, setFieldTouched] = useState<{[key: string]: boolean}>({});
  const [validFields, setValidFields] = useState<{[key: string]: boolean}>({});
  const [validAccessCodes, setValidAccessCodes] = useState<string[]>([]);
  const [accessCode, setAccessCode] = useState('');
  const [isValidatingCode, setIsValidatingCode] = useState(false);

  // Fetch valid access codes from database
  useEffect(() => {
    fetchValidAccessCodes();
  }, []);

  useEffect(() => {
    if (accessCode) {
      // Fetch valid access codes from database
      fetchValidAccessCodes();
      
      // Validate the entered access code
      setIsValidatingCode(true);
      setTimeout(() => {
        setIsValidatingCode(false);
      }, 1500);
    }
  }, [accessCode]);

  const fetchValidAccessCodes = async () => {
    try {
      const { data: codes, error } = await supabase
        .from('access_codes')
        .select('code, usage_count, usage_limit')
        .eq('is_active', true);
        
      if (error) {
        console.error('Error fetching access codes:', error);
        return;
      }
      
      // Filter out codes that have reached their usage limit
      const validCodes = codes?.filter(item => {
        // If usage_limit is null, it's unlimited
        if (item.usage_limit === null) return true;
        // Otherwise, check if usage hasn't exceeded the limit
        return item.usage_count < item.usage_limit;
      }).map(item => item.code) || [];
      
      setValidAccessCodes(validCodes);
    } catch (error) {
      console.error('Error fetching access codes:', error);
    }
  };

  const validateAccessCodeSync = (code: string): boolean => {
    return validAccessCodes.includes(code.toUpperCase());
  };

  // Real-time field validation
  const validateField = (field: string, value: string): string | null => {
    const requiredFields = ['fullName', 'email', 'phone', 'university', 'year', 'skills', 'teamStatus', 'whyPieFi', 'availability'];
    
    // Required field check
    if (requiredFields.includes(field) && !value.trim()) {
      const fieldName = field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      return `${fieldName} is required`;
    }

    // Field-specific validation
    switch (field) {
      case 'email':
        if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          return 'Please enter a valid email address';
        }
        break;
        
      case 'phone':
        if (value) {
          const cleanPhone = value.replace(/[\s\-\(\)\.]/g, '');
          if (!/^[\+]?[1-9][\d]{0,15}$/.test(cleanPhone) || cleanPhone.length < 10) {
            return 'Please enter a valid phone number (at least 10 digits)';
          }
        }
        break;
        
      case 'graduationYear':
        if (value) {
          const currentYear = new Date().getFullYear();
          const gradYear = parseInt(value);
          if (isNaN(gradYear) || gradYear < currentYear || gradYear > currentYear + 10) {
            return 'Please enter a valid graduation year';
          }
        }
        break;
        
      case 'linkedin':
      case 'github':
      case 'website':
        // Simplified URL validation - accepts common domain formats
        if (value) {
          // Accept various URL formats:
          // - domain.com
          // - https://domain.com
          // - subdomain.domain.com
          // - domain.com/path
          const urlPattern = /^(https?:\/\/)?([\w\-]+\.)+[\w\-]+(\/.*)?$/i;
          
          if (!urlPattern.test(value)) {
            const fieldName = field === 'website' ? 'website' : 
                            field === 'linkedin' ? 'LinkedIn' : 'GitHub';
            return `Please enter a valid ${fieldName} URL (e.g., example.com or https://example.com)`;
          }
        }
        break;
        
      case 'accessCode':
        if (value) {
          if (!validateAccessCodeSync(value)) {
            return 'Invalid referral code. Please check with the Pie Fi team.';
          }
        }
        break;
        
      case 'fullName':
        if (value && value.length < 2) {
          return 'Please enter your full name';
        }
        break;
        
      case 'university':
        if (value && value.length < 2) {
          return 'Please enter a valid university/school name';
        }
        break;
        
      case 'skills':
      case 'whyPieFi':
      case 'availability':
        if (value && value.length < 10) {
          return 'Please provide more detail (at least 10 characters)';
        }
        break;
    }
    
    return null;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Mark field as touched
    if (!fieldTouched[field]) {
      setFieldTouched(prev => ({ ...prev, [field]: true }));
    }
    
    // Real-time validation
    const error = validateField(field, value);
    setFieldErrors(prev => ({ ...prev, [field]: error }));
    
    // Special handling for access code
    if (field === 'accessCode') {
      const isValidCode = !error && value.trim() !== '' && validateAccessCodeSync(value);
      setValidFields(prev => ({ ...prev, [field]: isValidCode }));
    } else {
      // For other fields, show success only when:
      // 1. No validation error
      // 2. Field has content (for required fields) OR field is optional and has valid content
      // 3. Not a dropdown field (year, teamStatus)
      const skipSuccessFields = ['year', 'teamStatus'];
      
      if (!skipSuccessFields.includes(field)) {
        const hasValidContent = value.trim() !== '';
        const isFieldValid = !error && hasValidContent;
        
        // For optional URL fields, only show success if they have valid content
        if (['website', 'linkedin', 'github'].includes(field)) {
          setValidFields(prev => ({ ...prev, [field]: isFieldValid && hasValidContent }));
        } else {
          setValidFields(prev => ({ ...prev, [field]: isFieldValid }));
        }
      }
    }
  };

  const handleBlur = (field: string) => {
    setFieldTouched(prev => ({ ...prev, [field]: true }));
    const value = formData[field as keyof typeof formData];
    const error = validateField(field, value);
    setFieldErrors(prev => ({ ...prev, [field]: error }));
    
    // Special handling for access code
    if (field === 'accessCode') {
      const isValidCode = !error && value.trim() !== '' && validateAccessCodeSync(value);
      setValidFields(prev => ({ ...prev, [field]: isValidCode }));
    } else {
      // For other fields, show success only when valid
      const skipSuccessFields = ['year', 'teamStatus'];
      
      if (!skipSuccessFields.includes(field)) {
        const hasValidContent = value.trim() !== '';
        const isFieldValid = !error && hasValidContent;
        
        // For optional URL fields, only show success if they have valid content
        if (['website', 'linkedin', 'github'].includes(field)) {
          setValidFields(prev => ({ ...prev, [field]: isFieldValid && hasValidContent }));
        } else {
          setValidFields(prev => ({ ...prev, [field]: isFieldValid }));
        }
      }
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Prevent any default behavior that might cause page refresh
    e.preventDefault();
    e.stopPropagation();
    
    const file = e.target.files?.[0];
    if (file) {
      // Use setTimeout to ensure the file processing doesn't block the UI
      setTimeout(() => {
        if (file.type !== 'application/pdf') {
          setFieldErrors(prev => ({ ...prev, resume: 'Please upload a PDF file only' }));
          setResumeFile(null);
          return;
        }
        if (file.size > 10 * 1024 * 1024) { // 10MB limit
          setFieldErrors(prev => ({ ...prev, resume: 'File size must be less than 10MB' }));
          setResumeFile(null);
          return;
        }
        setResumeFile(file);
        setFieldErrors(prev => ({ ...prev, resume: null }));
        setValidFields(prev => ({ ...prev, resume: true }));
      }, 0);
    }
  };

  const uploadResume = async () => {
    if (!resumeFile) return null;

    try {
      const fileExt = resumeFile.name.split('.').pop();
      const fileName = `${Date.now()}.${fileExt}`;
      const filePath = `resumes/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('applications')
        .upload(filePath, resumeFile, {
          contentType: 'application/pdf',
          cacheControl: '3600',
          upsert: false,
        });

      if (uploadError) {
        console.error('Error uploading resume:', uploadError);
        throw new Error('Failed to upload resume');
      }

      const { data } = supabase.storage
        .from('applications')
        .getPublicUrl(filePath);

      return data.publicUrl;
    } catch (error) {
      setFieldErrors(prev => ({ ...prev, resume: 'Failed to upload resume. Please try again.' }));
      throw error;
    }
  };

  // Enhanced form validation
  const validateForm = (): boolean => {
    const requiredFields: (keyof typeof formData)[] = [
      'fullName', 'email', 'phone', 'university', 'year', 'skills', 'teamStatus', 'whyPieFi', 'availability', 'previousProjects', 'projectIdea'
    ];

    const optionalFields: (keyof typeof formData)[] = [
      'major', 'graduationYear', 'linkedin', 'website', 'github', 'references', 'accessCode'
    ];

    let hasErrors = false;
    const newErrors: FieldErrors = {};

    // Validate required fields
    for (const field of requiredFields) {
      const value = formData[field];
      if (!value || value.trim() === '') {
        const fieldName = field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        newErrors[field] = `${fieldName} is required`;
        hasErrors = true;
      } else {
        // Additional validation for specific fields
        const error = validateField(field, value);
        if (error) {
          newErrors[field] = error;
          hasErrors = true;
        }
      }
    }

    // Validate optional fields only if they have content
    for (const field of optionalFields) {
      const value = formData[field];
      if (value && value.trim() !== '') {
        const error = validateField(field, value);
        if (error) {
          newErrors[field] = error;
          hasErrors = true;
        }
      }
    }

    // Mark all fields with errors as touched
    const errorFields = Object.keys(newErrors);
    const touchedFields = [...requiredFields, ...errorFields].reduce((acc, field) => ({ ...acc, [field]: true }), {});
    setFieldTouched(prev => ({ ...prev, ...touchedFields }));
    setFieldErrors(prev => ({ ...prev, ...newErrors }));

    if (hasErrors) {
      // Find first error field and scroll to it
      const firstErrorField = Object.keys(newErrors)[0];
      const element = document.getElementById(firstErrorField);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        element.focus();
      }
      
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form before submitting",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Additional access code validation before submission
    if (formData.accessCode && !validateAccessCodeSync(formData.accessCode)) {
      toast({
        title: "Invalid Referral Code",
        description: "Please check with the Pie Fi team.",
        variant: "destructive",
      });
      return;
    }

    // Rate limit check
    const SUBMISSION_TIMESTAMP_KEY = 'piefi_last_submission';
    const lastSubmission = localStorage.getItem(SUBMISSION_TIMESTAMP_KEY);
    if (lastSubmission) {
      const diff = Date.now() - parseInt(lastSubmission, 10);
      if (diff < 10 * 60 * 1000) {
        toast({
          title: "Rate Limited",
          description: "Please wait a few minutes before submitting again.",
          variant: "destructive",
        });
        return;
      }
    }

    setIsSubmitting(true);

    try {
      // Upload resume if provided
      const resumeUrl = await uploadResume();

      // Map form data to database schema
      const applicationData = {
        full_name: formData.fullName,
        email: formData.email,
        phone: formData.phone,
        university: formData.university,
        year: formData.year,
        major: formData.major,
        graduation_year: formData.graduationYear,
        skills: formData.skills,
        project_idea: formData.projectIdea,
        team_status: formData.teamStatus,
        why_pie_fi: formData.whyPieFi,
        linkedin: formData.linkedin,
        website: formData.website,
        github: formData.github,
        previous_projects: formData.previousProjects,
        availability: formData.availability,
        references: formData.references,
        resume_url: resumeUrl,
        access_code_used: formData.accessCode.toUpperCase(), // Track which access code was used
        application_type: 'builder' // This is a builder application, not a supporter
      };

      const { error } = await supabase
        .from('applications')
        .insert(applicationData);

      if (error) {
        console.error('Error submitting application:', error);
        if (error.code === '23505') {
          toast({
            title: "Duplicate Application",
            description: "An application with this email already exists.",
            variant: "destructive",
          });
        } else if (error.code === 'PGRST116') {
          toast({
            title: "Database Error",
            description: "Database is temporarily unavailable. Please try again later.",
            variant: "destructive",
          });
        } else {
          toast({
            title: "Submission Failed",
            description: "Failed to submit application. Please check your internet connection and try again.",
            variant: "destructive",
          });
        }
        return;
      }

              toast({
          title: "Success!",
          description: "🎉 Application submitted successfully! We'll be in touch soon.",
        });
      
      // Reset form
      setFormData({
        fullName: '',
        email: '',
        phone: '',
        university: '',
        year: '',
        major: '',
        graduationYear: '',
        skills: '',
        projectIdea: '',
        teamStatus: '',
        whyPieFi: '',
        linkedin: '',
        website: '',
        github: '',
        previousProjects: '',
        availability: '',
        references: '',
        accessCode: ''
      });
      setResumeFile(null);
      setFieldErrors({});
      setFieldTouched({});
      setValidFields({});

      // Save timestamp for rate-limit tracking
      localStorage.setItem(SUBMISSION_TIMESTAMP_KEY, Date.now().toString());

      if (onSuccess) {
        onSuccess();
      }
      
    } catch (error) {
      console.error('Unexpected error:', error);
              toast({
          title: "Error",
          description: "An unexpected error occurred. Please check your internet connection and try again.",
          variant: "destructive",
        });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Animation variants
  const formVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.8, staggerChildren: 0.1 }
    }
  };

  const fieldVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  const formFields = [
    {
      id: 'fullName',
      label: 'Full Name',
      type: 'input',
      placeholder: 'Your full name',
      required: true,
      icon: User,
      color: 'from-blue-400 to-blue-600'
    },
    {
      id: 'email',
      label: 'Email',
      type: 'input',
      inputType: 'email',
      placeholder: '<EMAIL>',
      required: true,
      icon: Mail,
      color: 'from-green-400 to-green-600'
    },
    {
      id: 'phone',
      label: 'Phone Number',
      type: 'input',
      inputType: 'tel',
      placeholder: '+****************',
      required: true,
      icon: Phone,
      color: 'from-cyan-400 to-cyan-600'
    },
    {
      id: 'university',
      label: 'University/School',
      type: 'input',
      placeholder: 'UC Santa Cruz, Stanford, etc.',
      required: true,
      icon: School,
      color: 'from-purple-400 to-purple-600'
    },
    {
      id: 'major',
      label: 'Major/Field of Study',
      type: 'input',
      placeholder: 'Computer Science, Business, Design, etc.',
      required: false,
      icon: School,
      color: 'from-indigo-400 to-indigo-600'
    },
    {
      id: 'graduationYear',
      label: 'Expected Graduation Year',
      type: 'input',
      inputType: 'number',
      placeholder: '2025',
      required: false,
      icon: School,
      color: 'from-teal-400 to-teal-600'
    },
    {
      id: 'linkedin',
      label: 'LinkedIn Profile',
      type: 'input',
      inputType: 'text',
      placeholder: 'linkedin.com/in/yourprofile',
      required: false,
      icon: Linkedin,
      color: 'from-blue-600 to-blue-800'
    },
    {
      id: 'website',
      label: 'Personal Website/Portfolio',
      type: 'input',
      inputType: 'text',
      placeholder: 'yourwebsite.com',
      required: false,
      icon: Globe,
      color: 'from-emerald-400 to-emerald-600'
    },
    {
      id: 'github',
      label: 'GitHub Profile',
      type: 'input',
      inputType: 'text',
      placeholder: 'github.com/yourusername',
      required: false,
      icon: Code,
      color: 'from-gray-600 to-gray-800'
    },
    {
      id: 'skills',
      label: 'Skills & Experience',
      type: 'textarea',
      placeholder: 'What can you build? (e.g., frontend development, design, AI/ML, business development, marketing...)',
      required: true,
      icon: Code,
      color: 'from-yellow-400 to-yellow-600'
    },
    {
      id: 'previousProjects',
      label: 'Previous Projects',
      type: 'textarea',
      placeholder: 'Tell us about projects you\'ve worked on, hackathons you\'ve participated in, or things you\'ve built.',
      required: true,
      icon: Lightbulb,
      color: 'from-pink-400 to-pink-600'
    },
    {
      id: 'projectIdea',
      label: 'Project Idea',
      type: 'textarea',
      placeholder: "Tell us about your project idea, or write 'open to collaboration' if you want to join a team",
      required: true,
      icon: Lightbulb,
      color: 'from-orange-400 to-orange-600'
    },
    {
      id: 'whyPieFi',
      label: 'Why Pie Fi?',
      type: 'textarea',
      placeholder: 'What draws you to Pie Fi? What do you hope to accomplish in 10 weeks?',
      required: true,
      icon: Sparkles,
      color: 'from-red-400 to-red-600'
    },
    {
      id: 'availability',
      label: 'Availability & Commitment',
      type: 'textarea',
      placeholder: 'Can you commit to the full 10-week program? Any scheduling conflicts we should know about?',
      required: true,
      icon: Users,
      color: 'from-violet-400 to-violet-600'
    },
    {
      id: 'references',
      label: 'References (Optional)',
      type: 'textarea',
      placeholder: 'Names and contact info for 1-2 people who can speak to your work or character.',
      required: false,
      icon: Users,
      color: 'from-rose-400 to-rose-600'
    },
    {
      id: 'accessCode',
      label: 'Referral Code (Optional)',
      type: 'input',
      placeholder: 'Referral code from a Pie Fi team member',
      required: false,
      icon: Key,
      color: 'from-orange-500 to-red-500'
    }
  ];

  // Get field styling based on validation state
  const getFieldStyling = (fieldId: string) => {
    const hasError = fieldTouched[fieldId] && fieldErrors[fieldId];
    const isValid = validFields[fieldId] && !fieldErrors[fieldId];
    
    let borderColor = 'border-accent-white/30';
    let focusColor = 'focus:border-sauce-red focus:ring-sauce-red';
    
    if (hasError) {
      borderColor = 'border-red-400';
      focusColor = 'focus:border-red-400 focus:ring-red-400';
    } else if (isValid) {
      borderColor = 'border-green-400';
      focusColor = 'focus:border-green-400 focus:ring-green-400';
    }
    
    return `bg-accent-white/10 backdrop-blur-md ${borderColor} text-accent-white placeholder:text-crust-beige/60 rounded-xl h-12 text-lg md:text-lg text-base ${focusColor} transition-all duration-300 placeholder:text-sm md:placeholder:text-base`;
  };

  return (
    <motion.div
      ref={formRef}
      initial="hidden"
      animate={formInView ? "visible" : "hidden"}
      variants={formVariants}
      className="relative group"
    >
      {/* Glow effect - reduced on mobile */}
      {!isMobile && (
        <div className="absolute -inset-1 bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 rounded-3xl blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-500" />
      )}
      
      {/* Main form card */}
      <Card className="relative bg-accent-white/10 backdrop-blur-md border border-accent-white/20 rounded-3xl shadow-2xl overflow-visible">
        <CardHeader className="bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 pb-8">
          <CardTitle className="flex items-center space-x-3 text-accent-white text-2xl">
            <motion.div
              className="p-3 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-2xl"
              whileHover={!isMobile ? { rotate: 360 } : undefined}
              transition={{ duration: 0.5 }}
            >
              <Users className="h-8 w-8 text-white" />
            </motion.div>
            <span className="font-black">Builder Application</span>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="p-8">
          {/* Discord Community Callout */}
          <motion.div
            className="mb-8 p-6 bg-gradient-to-r from-purple-500/10 to-indigo-500/10 backdrop-blur-md rounded-2xl border border-purple-400/30"
            variants={fieldVariants}
            whileHover={!isMobile ? { scale: 1.02, y: -2 } : undefined}
          >
            <div className="flex items-center gap-4 mb-4">
              <motion.div 
                className="p-3 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-xl"
                whileHover={!isMobile ? { scale: 1.1, rotate: 10 } : undefined}
              >
                <MessageSquare className="h-6 w-6 text-white" />
              </motion.div>
              <div>
                <h3 className="text-xl font-bold text-accent-white">Join Our Discord Community!</h3>
                <p className="text-crust-beige/80 text-sm">Stay connected with fellow builders</p>
              </div>
            </div>
            <p className="text-crust-beige/90 mb-4 leading-relaxed">
              Join our Discord server for real-time updates, community discussions, Q&A sessions, and direct access to the Pie Fi team. Connect with other builders, get early announcements, and be part of the conversation!
            </p>
            <motion.a
              href="https://discord.gg/gzQC3PBded"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-semibold rounded-xl transition-all duration-300"
              whileHover={!isMobile ? { scale: 1.05 } : undefined}
              whileTap={{ scale: 0.95 }}
            >
              <MessageSquare className="w-4 h-4" />
              Join Discord Server
            </motion.a>
          </motion.div>

          <form 
            onSubmit={handleSubmit} 
            className="space-y-8"
            onKeyDown={(e) => {
              // Prevent form submission on Enter key when file input is focused
              if (e.key === 'Enter' && e.target === document.getElementById('resume')) {
                e.preventDefault();
              }
            }}
          >
            {formFields.map((field, index) => (
              <motion.div 
                key={field.id}
                className="group/field relative"
                variants={fieldVariants}
                custom={index}
              >
                {/* Field glow effect - reduced on mobile */}
                {!isMobile && (
                  <div className={`absolute -inset-1 bg-gradient-to-r ${field.color} rounded-2xl blur-lg opacity-0 group-hover/field:opacity-20 transition-opacity duration-300`} />
                )}
                
                <div className="relative">
                  <Label htmlFor={field.id} className="flex items-center gap-3 text-accent-white font-semibold mb-3 text-lg">
                    <motion.div 
                      className={`p-2 bg-gradient-to-r ${field.color} rounded-xl`}
                      whileHover={!isMobile ? { scale: 1.1, rotate: 10 } : undefined}
                    >
                      <field.icon className="h-5 w-5 text-white" />
                    </motion.div>
                    {field.label}
                    <RequiredIndicator required={field.required} />
                  </Label>
                  
                  {field.type === 'input' ? (
                    <Input
                      id={field.id}
                      type={field.inputType || "text"}
                      placeholder={field.placeholder}
                      value={formData[field.id as keyof typeof formData]}
                      onChange={(e) => handleInputChange(field.id, e.target.value)}
                      onBlur={() => handleBlur(field.id)}
                      required={field.required}
                      className={getFieldStyling(field.id)}
                    />
                  ) : (
                    <Textarea
                      id={field.id}
                      placeholder={field.placeholder}
                      value={formData[field.id as keyof typeof formData]}
                      onChange={(e) => handleInputChange(field.id, e.target.value)}
                      onBlur={() => handleBlur(field.id)}
                      required={field.required}
                      autoResize={true}
                      className={`bg-accent-white/10 backdrop-blur-md ${fieldTouched[field.id] && fieldErrors[field.id] ? 'border-red-400 focus:border-red-400 focus:ring-red-400' : validFields[field.id] ? 'border-green-400 focus:border-green-400 focus:ring-green-400' : 'border-accent-white/30 focus:border-sauce-red focus:ring-sauce-red'} text-accent-white placeholder:text-crust-beige/60 rounded-xl min-h-[120px] text-lg md:text-lg text-base transition-all duration-300 placeholder:text-sm md:placeholder:text-base`}
                    />
                  )}
                  
                  {/* Error/Success Messages */}
                  <FieldError error={fieldTouched[field.id] ? fieldErrors[field.id] : null} />
                  <FieldSuccess show={validFields[field.id] && !fieldErrors[field.id]} />
                </div>
              </motion.div>
            ))}

            {/* Resume Upload */}
            <motion.div 
              className="group/field relative"
              variants={fieldVariants}
            >
              {!isMobile && (
                <div className="absolute -inset-1 bg-gradient-to-r from-emerald-400 to-emerald-600 rounded-2xl blur-lg opacity-0 group-hover/field:opacity-20 transition-opacity duration-300" />
              )}
              <div className="relative">
                <Label htmlFor="resume" className="flex items-center gap-3 text-accent-white font-semibold mb-3 text-lg">
                  <motion.div 
                    className="p-2 bg-gradient-to-r from-emerald-400 to-emerald-600 rounded-xl"
                    whileHover={!isMobile ? { scale: 1.1, rotate: 10 } : undefined}
                  >
                    <FileText className="h-5 w-5 text-white" />
                  </motion.div>
                  Resume Upload (Optional)
                </Label>
                
                <div className="relative">
                  <div 
                    className="relative"
                    onTouchStart={(e) => e.stopPropagation()}
                    onTouchEnd={(e) => e.stopPropagation()}
                  >
                    <Input
                      id="resume"
                      type="file"
                      accept=".pdf"
                      onChange={handleFileChange}
                      onClick={(e) => e.stopPropagation()}
                      onFocus={(e) => e.stopPropagation()}
                      key={resumeFile ? resumeFile.name : 'resume-input'}
                      style={{ WebkitAppearance: 'none' }}
                      className={`bg-accent-white/10 backdrop-blur-md ${fieldErrors.resume ? 'border-red-400' : validFields.resume ? 'border-green-400' : 'border-accent-white/30'} text-accent-white rounded-xl h-12 text-lg focus:border-sauce-red focus:ring-sauce-red transition-all duration-300 file:bg-sauce-red file:text-white file:border-0 file:rounded-lg file:px-4 file:py-2 file:mr-4`}
                    />
                  </div>
                  {resumeFile && !fieldErrors.resume && (
                    <motion.p 
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-sm text-green-400 mt-2 flex items-center gap-2"
                    >
                      <CheckCircle className="h-4 w-4" />
                      {resumeFile.name} uploaded successfully
                    </motion.p>
                  )}
                  <FieldError error={fieldErrors.resume} />
                </div>
              </div>
            </motion.div>

            {/* Select fields */}
            <motion.div 
              className="grid md:grid-cols-2 gap-6"
              variants={fieldVariants}
            >
              <div className="relative group/field">
                {!isMobile && (
                  <div className="absolute -inset-1 bg-gradient-to-r from-teal-400 to-teal-600 rounded-2xl blur-lg opacity-0 group-hover/field:opacity-20 transition-opacity duration-300" />
                )}
                <div className="relative">
                  <Label htmlFor="year" className="flex items-center gap-3 text-accent-white font-semibold mb-3 text-lg">
                    <motion.div 
                      className="p-2 bg-gradient-to-r from-teal-400 to-teal-600 rounded-xl"
                      whileHover={!isMobile ? { scale: 1.1, rotate: 10 } : undefined}
                    >
                      <School className="h-5 w-5 text-white" />
                    </motion.div>
                    Year/Status
                    <RequiredIndicator required={true} />
                  </Label>
                  <MobileSelect
                    value={formData.year}
                    onValueChange={(value) => handleInputChange('year', value)}
                    placeholder="Select your year/status..."
                    className={getFieldStyling('year')}
                  >
                    <SelectItem value="freshman">Freshman</SelectItem>
                    <SelectItem value="sophomore">Sophomore</SelectItem>
                    <SelectItem value="junior">Junior</SelectItem>
                    <SelectItem value="senior">Senior</SelectItem>
                    <SelectItem value="graduate">Graduate Student</SelectItem>
                    <SelectItem value="recent-grad">Recent Graduate</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </MobileSelect>
                  <FieldError error={fieldTouched.year ? fieldErrors.year : null} />
                </div>
              </div>

              <div className="relative group/field">
                {!isMobile && (
                  <div className="absolute -inset-1 bg-gradient-to-r from-orange-400 to-orange-600 rounded-2xl blur-lg opacity-0 group-hover/field:opacity-20 transition-opacity duration-300" />
                )}
                <div className="relative">
                  <Label htmlFor="teamStatus" className="flex items-center gap-3 text-accent-white font-semibold mb-3 text-lg">
                    <motion.div 
                      className="p-2 bg-gradient-to-r from-orange-400 to-orange-600 rounded-xl"
                      whileHover={!isMobile ? { scale: 1.1, rotate: 10 } : undefined}
                    >
                      <Users className="h-5 w-5 text-white" />
                    </motion.div>
                    Team Status
                    <RequiredIndicator required={true} />
                  </Label>
                  <MobileSelect
                    value={formData.teamStatus}
                    onValueChange={(value) => handleInputChange('teamStatus', value)}
                    placeholder="Select your team preference..."
                    className={getFieldStyling('teamStatus')}
                  >
                    <SelectItem value="solo">Flying Solo</SelectItem>
                    <SelectItem value="have-team">I Have a Team</SelectItem>
                    <SelectItem value="looking-for-team">Looking for a Team</SelectItem>
                    <SelectItem value="flexible">Flexible</SelectItem>
                  </MobileSelect>
                  <FieldError error={fieldTouched.teamStatus ? fieldErrors.teamStatus : null} />
                </div>
              </div>
            </motion.div>

            {/* Submit Button */}
            <motion.div 
              className="pt-8"
              variants={fieldVariants}
            >
              <motion.div
                whileHover={!isMobile ? { scale: 1.05 } : undefined}
                whileTap={{ scale: 0.95 }}
                className="relative group/submit"
              >
                {!isMobile && (
                  <div className="absolute inset-0 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-2xl blur-xl opacity-70 group-hover/submit:opacity-100 transition-opacity pointer-events-none z-0" />
                )}
                <Button 
                  type="submit"
                  size="lg"
                  disabled={isSubmitting}
                  className="relative w-full bg-gradient-to-r from-sauce-red to-cheese-gold hover:from-sauce-red/90 hover:to-cheese-gold/90 text-oven-black font-bold text-lg px-8 py-6 rounded-2xl shadow-2xl border-0 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed z-10"
                >
                  {isSubmitting ? (
                    <>
                      <motion.div
                        className="w-6 h-6 border-2 border-oven-black/30 border-t-oven-black rounded-full mr-3"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      />
                      Submitting Application...
                    </>
                  ) : (
                    <>
                      <Send className="mr-3 h-6 w-6" />
                      Submit Application
                    </>
                  )}
                </Button>
              </motion.div>
              
              <p className="text-sm text-crust-beige/60 mt-4 text-center">
                By submitting, you agree to be contacted about Pie Fi updates and opportunities.
              </p>
            </motion.div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default ApplicationFormComponent;
