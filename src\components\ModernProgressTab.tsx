import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { toast } from "sonner";
import ComprehensiveInvestorDashboard from "./ComprehensiveInvestorDashboard";
import onboardingToJourneyService from "@/services/onboardingToJourneyService";

interface ModernProgressTabProps {
  userId: string;
  currentStage: number;
  stageConfidence: number;
  onStageUpdate?: () => void;
}

const ModernProgressTab: React.FC<ModernProgressTabProps> = ({
  userId,
  currentStage,
  stageConfidence,
  onStageUpdate
}) => {
  const [refreshing, setRefreshing] = useState(false);

  const handleRefreshProgress = async () => {
    setRefreshing(true);
    try {
      await onboardingToJourneyService.populateJourneyFromOnboarding(userId);
      toast.success("Progress refreshed successfully!");
      onStageUpdate?.();
    } catch (error) {
      console.error("Error refreshing progress:", error);
      toast.error("Failed to refresh progress");
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-accent-white">Comprehensive Project Dashboard</h2>
          <p className="text-accent-white/70 mt-1">
            Professional investor presentation with detailed progress tracking
          </p>
        </div>
        <Button
          onClick={handleRefreshProgress}
          disabled={refreshing}
          variant="outline"
          className="bg-accent-white/10 border-accent-white/20 text-accent-white hover:bg-accent-white/20"
        >
          {refreshing ? (
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Refresh Data
        </Button>
      </div>

      <ComprehensiveInvestorDashboard
        userId={userId}
        currentStage={currentStage}
        stageConfidence={stageConfidence}
        onStageUpdate={onStageUpdate}
      />
    </div>
  );
};

export default ModernProgressTab;
