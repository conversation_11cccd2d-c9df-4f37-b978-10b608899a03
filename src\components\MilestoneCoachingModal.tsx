import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Brain, 
  Send, 
  User, 
  CheckCircle, 
  MessageSquare,
  Sparkles,
  X,
  Save,
  RotateCcw
} from 'lucide-react';
import { toast } from 'sonner';
import MarkdownText from './MarkdownText';
import geminiService from '@/services/geminiService';
import { supabase } from '@/integrations/supabase/client';
import { initializeCoachingTable } from '@/utils/createCoachingTable';

interface MilestoneCoachingModalProps {
  isOpen: boolean;
  onClose: () => void;
  milestone: {
    id: string;
    milestone_id: string;
    stage_id: number;
    submission_content: string;
    ai_feedback: string;
  } | null;
  followUpQuestions: string[];
  userId: string;
  onConversationComplete: () => void;
}

interface ConversationMessage {
  id: string;
  type: 'ai' | 'user';
  content: string;
  timestamp: Date;
  confidence?: number;
}

interface ConversationState {
  messages: ConversationMessage[];
  currentConfidence: number;
  isComplete: boolean;
  conversationId: string;
}

const MilestoneCoachingModal: React.FC<MilestoneCoachingModalProps> = ({
  isOpen,
  onClose,
  milestone,
  followUpQuestions,
  userId,
  onConversationComplete
}) => {
  const [conversationState, setConversationState] = useState<ConversationState>({
    messages: [],
    currentConfidence: 50,
    isComplete: false,
    conversationId: `coaching_${milestone?.id || 'unknown'}_${Date.now()}`
  });
  
  const [currentResponse, setCurrentResponse] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Initialize conversation with context and first questions
  useEffect(() => {
    if (isOpen && milestone && followUpQuestions.length > 0) {
      console.log('🚀 Initializing coaching conversation with questions:', followUpQuestions);
      initializeConversation();
    }
  }, [isOpen, milestone, followUpQuestions]);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversationState.messages]);

  const initializeConversation = () => {
    if (!milestone) return;

    const contextMessage: ConversationMessage = {
      id: 'context',
      type: 'ai',
      content: `I've reviewed your milestone submission for "${milestone.milestone_id.replace(/_/g, ' ')}" and have some follow-up questions to help deepen your thinking. Let's have a conversation about it!

**Your Original Submission:**
${milestone.submission_content}

**My Initial Analysis:**
${milestone.ai_feedback}

Now, let's dive deeper with some questions:`,
      timestamp: new Date()
    };

    const questionMessages: ConversationMessage[] = followUpQuestions.map((question, index) => ({
      id: `question_${index}`,
      type: 'ai',
      content: `**Question ${index + 1}:** ${question}`,
      timestamp: new Date(),
      confidence: 50
    }));

    setConversationState(prev => ({
      ...prev,
      messages: [contextMessage, ...questionMessages]
    }));
  };

  const handleSendResponse = async () => {
    if (!currentResponse.trim() || isProcessing || !milestone) return;

    console.log('💬 Sending user response:', currentResponse);

    const userMessage: ConversationMessage = {
      id: `user_${Date.now()}`,
      type: 'user',
      content: currentResponse,
      timestamp: new Date()
    };

    setConversationState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage]
    }));

    setCurrentResponse('');
    setIsProcessing(true);

    try {
      // Analyze the user's response and continue the conversation
      const conversationHistory = [...conversationState.messages, userMessage]
        .map(msg => `${msg.type.toUpperCase()}: ${msg.content}`)
        .join('\n\n');

      const aiResponse = await geminiService.continueCoachingConversation(
        conversationHistory,
        {
          milestoneId: milestone?.milestone_id || 'unknown',
          stageId: milestone?.stage_id || 0,
          originalSubmission: milestone?.submission_content || '',
          currentConfidence: conversationState.currentConfidence
        }
      );

      if (aiResponse) {
        const aiMessage: ConversationMessage = {
          id: `ai_${Date.now()}`,
          type: 'ai',
          content: aiResponse.response,
          timestamp: new Date(),
          confidence: aiResponse.confidence
        };

        setConversationState(prev => ({
          ...prev,
          messages: [...prev.messages, aiMessage],
          currentConfidence: aiResponse.confidence,
          isComplete: aiResponse.confidence >= 85 || aiResponse.isComplete
        }));

        // If conversation is complete, save the results
        if (aiResponse.confidence >= 85 || aiResponse.isComplete) {
          await saveConversationResults(aiResponse);
        }
      }
    } catch (error) {
      console.error('Error processing response:', error);
      toast.error('Failed to process your response. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const saveConversationResults = async (finalAnalysis: any) => {
    if (!milestone) return;

    setIsSaving(true);
    try {
      console.log('💾 Saving conversation results:', finalAnalysis);

      // Determine if milestone should be automatically approved
      const shouldApprove = finalAnalysis.confidence >= 85;

      // Update milestone with enhanced feedback and potential approval
      const updateData: any = {
        ai_feedback: finalAnalysis.enhancedFeedback || milestone.ai_feedback,
        ai_insights: JSON.stringify({
          conversationSummary: finalAnalysis.conversationSummary,
          keyInsights: finalAnalysis.keyInsights,
          finalConfidence: finalAnalysis.confidence,
          conversationId: conversationState.conversationId,
          originalFollowUpQuestions: followUpQuestions,
          autoApproved: shouldApprove
        })
      };

      // Auto-approve milestone if AI confidence is high enough
      if (shouldApprove) {
        updateData.is_approved = true;
        updateData.approval_date = new Date().toISOString();
        console.log('🎉 Auto-approving milestone due to high AI confidence:', finalAnalysis.confidence);
      }

      const { error: updateError } = await supabase
        .from('milestone_submissions')
        .update(updateData)
        .eq('id', milestone.id);

      if (updateError) {
        console.error('Error updating milestone:', updateError);
        throw updateError;
      }

      // Save conversation history for future reference (with error handling)
      try {
        // Try to initialize the table first
        const tableExists = await initializeCoachingTable();

        // Only try to save if table exists
        if (tableExists) {
          await supabase
            .from('coaching_conversations')
            .insert({
              user_id: userId,
              milestone_id: milestone?.id || 'unknown',
              conversation_id: conversationState.conversationId,
              conversation_data: JSON.stringify(conversationState),
              final_confidence: finalAnalysis.confidence,
              created_at: new Date().toISOString()
            });

          console.log('✅ Conversation history saved successfully');
        } else {
          // Store conversation in local storage as fallback
          try {
            localStorage.setItem(
              `coaching_conversation_${conversationState.conversationId}`,
              JSON.stringify({
                milestone_id: milestone?.id || 'unknown',
                conversation_data: conversationState,
                final_confidence: finalAnalysis.confidence,
                created_at: new Date().toISOString()
              })
            );
            console.log('📝 Conversation saved to local storage as fallback');
          } catch (localStorageError) {
            console.warn('Could not save to local storage:', localStorageError);
          }
        }
      } catch (conversationError) {
        console.warn('Could not save conversation history:', conversationError);
        // Continue without failing - the main milestone update is more important
      }

      // Provide specific success message based on approval status
      if (shouldApprove) {
        toast.success('🎉 Milestone completed and approved! Great work on demonstrating mastery.');
      } else {
        toast.success('Coaching session completed successfully! Continue working on this milestone.');
      }

      onConversationComplete();
    } catch (error) {
      console.error('Error saving conversation:', error);
      toast.error('Failed to save conversation results.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleEndEarly = () => {
    setConversationState(prev => ({ ...prev, isComplete: true }));
    toast.info('Conversation ended. You can always continue later!');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendResponse();
    }
  };

  // Don't render if milestone is null
  if (!milestone) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl h-[80vh] bg-gradient-to-br from-pizza-red/95 to-pizza-red/90 backdrop-blur-xl border border-accent-white/20 flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-2xl font-bold text-accent-white flex items-center gap-3">
            <motion.div
              animate={{ rotate: isProcessing ? 360 : 0 }}
              transition={{ duration: 2, repeat: isProcessing ? Infinity : 0, ease: "linear" }}
            >
              <Brain className="h-6 w-6 text-purple-400" />
            </motion.div>
            AI Coaching Session
            <Badge className="bg-purple-500/20 text-purple-200">
              {milestone?.milestone_id?.replace(/_/g, ' ') || 'Milestone'}
            </Badge>
          </DialogTitle>
          
          {/* AI Confidence Display */}
          <div className="space-y-3 mt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <motion.div
                  animate={{
                    scale: [1, 1.1, 1],
                    opacity: [0.7, 1, 0.7]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="w-3 h-3 bg-purple-400 rounded-full"
                />
                <span className="text-sm font-medium text-accent-white">AI Confidence Level</span>
              </div>
              <motion.span
                className="text-lg font-bold text-purple-400"
                animate={{
                  scale: conversationState.currentConfidence >= 85 ? [1, 1.2, 1] : 1
                }}
                transition={{ duration: 0.5 }}
              >
                {conversationState.currentConfidence}%
              </motion.span>
            </div>

            <div className="relative">
              <Progress
                value={conversationState.currentConfidence}
                className="h-3 bg-accent-white/10"
              />
              <motion.div
                className="absolute top-0 left-0 h-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full"
                initial={{ width: "50%" }}
                animate={{ width: `${conversationState.currentConfidence}%` }}
                transition={{ duration: 0.8, ease: "easeOut" }}
              />
            </div>

            <p className="text-xs text-accent-white/60 text-center">
              The AI will continue asking questions until it reaches sufficient confidence in your understanding (85%+)
            </p>
          </div>
        </DialogHeader>

        {/* Conversation Messages */}
        <div className="flex-1 overflow-y-auto space-y-4 py-4">
          <AnimatePresence>
            {conversationState.messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`flex gap-3 max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                    message.type === 'ai' 
                      ? 'bg-purple-500/20 text-purple-400' 
                      : 'bg-blue-500/20 text-blue-400'
                  }`}>
                    {message.type === 'ai' ? <Brain className="h-4 w-4" /> : <User className="h-4 w-4" />}
                  </div>
                  <div className={`rounded-lg p-3 ${
                    message.type === 'ai'
                      ? 'bg-accent-white/10 border border-accent-white/20'
                      : 'bg-blue-500/20 border border-blue-400/30'
                  }`}>
                    <MarkdownText className="text-sm text-accent-white/90">
                      {message.content}
                    </MarkdownText>
                    {message.confidence && (
                      <div className="mt-2 text-xs text-accent-white/60">
                        Confidence: {message.confidence}%
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        {!conversationState.isComplete && (
          <div className="flex-shrink-0 space-y-3">
            <Textarea
              value={currentResponse}
              onChange={(e) => setCurrentResponse(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your response here... (Press Enter to send, Shift+Enter for new line)"
              className="bg-accent-white/10 border-accent-white/20 text-accent-white placeholder:text-accent-white/60 min-h-[80px] resize-none"
              disabled={isProcessing}
            />
            <div className="flex items-center justify-between">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEndEarly}
                  className="bg-accent-white/10 border-accent-white/20 text-accent-white hover:bg-accent-white/20"
                >
                  <X className="h-4 w-4 mr-2" />
                  End Early
                </Button>
              </div>
              <Button
                onClick={handleSendResponse}
                disabled={!currentResponse.trim() || isProcessing}
                className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white"
              >
                {isProcessing ? (
                  <RotateCcw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Send className="h-4 w-4 mr-2" />
                )}
                {isProcessing ? 'Processing...' : 'Send'}
              </Button>
            </div>
          </div>
        )}

        {/* Completion State */}
        {conversationState.isComplete && (
          <div className={`flex-shrink-0 rounded-lg p-4 ${
            conversationState.currentConfidence >= 85
              ? 'bg-green-500/10 border border-green-400/30'
              : 'bg-blue-500/10 border border-blue-400/30'
          }`}>
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className={`h-5 w-5 ${
                conversationState.currentConfidence >= 85 ? 'text-green-400' : 'text-blue-400'
              }`} />
              <span className={`font-medium ${
                conversationState.currentConfidence >= 85 ? 'text-green-400' : 'text-blue-400'
              }`}>
                {conversationState.currentConfidence >= 85
                  ? '🎉 Milestone Completed & Approved!'
                  : 'Coaching Session Complete!'}
              </span>
            </div>
            <p className="text-sm text-accent-white/80 mb-3">
              {conversationState.currentConfidence >= 85
                ? `Excellent work! You've demonstrated mastery of this milestone. Final confidence: ${conversationState.currentConfidence}%`
                : `Great conversation! I now have a better understanding of your approach. Final confidence: ${conversationState.currentConfidence}% - continue working on this milestone.`
              }
            </p>
            <div className="flex gap-2">
              <Button
                onClick={onClose}
                className={`border ${
                  conversationState.currentConfidence >= 85
                    ? 'bg-green-500/20 border-green-400/30 text-green-400 hover:bg-green-500/30'
                    : 'bg-blue-500/20 border-blue-400/30 text-blue-400 hover:bg-blue-500/30'
                }`}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Save className="h-4 w-4 mr-2 animate-pulse" />
                    Saving...
                  </>
                ) : (
                  'Close'
                )}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default MilestoneCoachingModal;
