import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { 
  Lightbulb, 
  Users, 
  Hammer, 
  Rocket, 
  TrendingUp, 
  Building,
  CheckCircle,
  ArrowRight,
  Sparkles,
  Target,
  Plus,
  FileText,
  Calendar,
  Brain,
  AlertCircle,
  Clock,
  Send,
  Edit3,
  Loader2,
  MessageSquare
} from 'lucide-react';
import devJourneyService, { DevJourneyStage } from '@/services/devJourneyService';
import userMemoryService from '@/services/userMemoryService';
import stageProgressService from '@/services/stageProgressService';
import geminiService from '@/services/geminiService';
import { supabase } from '@/integrations/supabase/client';
import MarkdownText from './MarkdownText';
import { activityTrackingService } from '@/services/activityTrackingService';

interface DevJourneyStageTabProps {
  stage: DevJourneyStage;
  isCurrentStage: boolean;
  isCompleted: boolean;
  userId: string;
  onStageUpdate?: () => void;
}

interface ProjectSpecificMilestone {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  required: boolean;
  projectContext?: string;
  userSpecificDetails?: string;
  aiInsight?: string;
  completedDate?: string;
  autoPopulated?: boolean;
  personalizedTitle?: string;
  personalizedDescription?: string;
  personalizedDeliverables?: string[];
}

const stageIcons = {
  0: Lightbulb,
  1: Users,
  2: Hammer,
  3: Rocket,
  4: TrendingUp,
  5: Building
};

const stageColors = {
  0: 'from-purple-500/20 to-pink-500/20',
  1: 'from-blue-500/20 to-purple-500/20', 
  2: 'from-orange-500/20 to-red-500/20',
  3: 'from-green-500/20 to-emerald-500/20',
  4: 'from-yellow-500/20 to-orange-500/20',
  5: 'from-indigo-500/20 to-blue-500/20'
};

const stageBorders = {
  0: 'border-purple-500/30',
  1: 'border-blue-500/30',
  2: 'border-orange-500/30', 
  3: 'border-green-500/30',
  4: 'border-yellow-500/30',
  5: 'border-indigo-500/30'
};

const DevJourneyStageTab: React.FC<DevJourneyStageTabProps> = ({ 
  stage, 
  isCurrentStage, 
  isCompleted, 
  userId,
  onStageUpdate 
}) => {
  const [isExpanded, setIsExpanded] = useState(isCurrentStage);
  const [stageNotes, setStageNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [projectMilestones, setProjectMilestones] = useState<ProjectSpecificMilestone[]>([]);
  const [stageProgress, setStageProgress] = useState(0);
  const [coachingInsight, setCoachingInsight] = useState('');
  const [milestoneInputs, setMilestoneInputs] = useState<Record<string, string>>({});
  const [editingMilestone, setEditingMilestone] = useState<string | null>(null);
  const [isSubmittingInput, setIsSubmittingInput] = useState(false);
  const [aiFeedback, setAiFeedback] = useState<Record<string, string>>({});

  const IconComponent = stageIcons[stage.id as keyof typeof stageIcons];
  const stageGradient = stageColors[stage.id as keyof typeof stageColors];
  const stageBorder = stageBorders[stage.id as keyof typeof stageBorders];

  useEffect(() => {
    if (isExpanded) {
      loadProjectSpecificMilestones();
    }
  }, [isExpanded, userId, stage.id]);

  const loadProjectSpecificMilestones = async () => {
    try {
      setIsLoading(true);
      
      // Get stage progress with completed milestones - Handle case where no data exists
      const progress = await stageProgressService.getCurrentStageProgress(userId, stage.id);
      
      // Get user context for AI-driven milestone customization
      const userContext = await userMemoryService.buildUserContext(userId);
      
      // Get comprehensive profile for project context - Handle gracefully if none exists
      const { data: profile, error: profileError } = await supabase
        .from('comprehensive_user_profiles')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1);

      // Use first profile or null if none exists
      const userProfile = profile && profile.length > 0 ? profile[0] : null;

      // Generate project-specific milestones
      const customMilestones = await generateProjectSpecificMilestones(
        stage.id,
        userContext,
        userProfile,
        progress?.milestones_completed || []
      );

      setProjectMilestones(customMilestones);
      
      // Calculate progress
      const completedRequired = customMilestones.filter(m => m.required && m.completed).length;
      const totalRequired = customMilestones.filter(m => m.required).length;
      const progressPercentage = totalRequired > 0 ? Math.round((completedRequired / totalRequired) * 100) : 0;
      setStageProgress(progressPercentage);

      // Get latest coaching insight for this stage
      const memories = await userMemoryService.getUserMemories(userId, 'insight', 5);
      const stageInsight = memories.find(m => m.metadata?.stage_id === stage.id);
      if (stageInsight) {
        setCoachingInsight(stageInsight.content);
      }
      
    } catch (error) {
      console.error('Error loading project milestones:', error);
      // Set default empty state instead of failing
      setProjectMilestones([]);
      setStageProgress(0);
    } finally {
      setIsLoading(false);
    }
  };

  const generateProjectSpecificMilestones = async (
    stageId: number,
    userContext: any,
    profile: any,
    completedMilestones: string[]
  ): Promise<ProjectSpecificMilestone[]> => {
    // Get base milestones for this stage
    const baseMilestones = stageProgressService.getStageMilestones(stageId);

    // Get milestone submissions from database
    const { data: milestoneSubmissions } = await supabase
      .from('milestone_submissions')
      .select('*')
      .eq('user_id', userId)
      .eq('stage_id', stageId);

    // Get recent user updates to extract specific details
    const recentUpdates = userContext?.recent_updates || [];
    const allUpdates = userContext?.all_interactions || [];

    return baseMilestones.map(milestone => {
      const isCompleted = completedMilestones.includes(milestone.id);

      // Find submission data for this milestone
      const submission = milestoneSubmissions?.find(s => s.milestone_id === milestone.id);

      // Customize milestone description based on user's actual progress and context
      let projectContext = milestone.description;
      let userSpecificDetails = '';
      let aiInsight = '';
      let completedDate = undefined;
      let autoPopulated = false;

      if (submission) {
        // Use submission data if available
        userSpecificDetails = submission.submission_content || '';
        aiInsight = submission.ai_feedback || '';
        completedDate = submission.submission_date;
        autoPopulated = submission.metadata?.auto_populated || false;

        // Use personalized content if available
        if (submission.metadata?.personalized_description) {
          projectContext = submission.metadata.personalized_description;
        } else if (userSpecificDetails) {
          projectContext = milestone.description; // Keep original description as context
        }
      } else if (profile?.profile_summary || recentUpdates.length > 0) {
        // Fall back to old customization method
        const customization = customizeMilestoneForProject(
          milestone,
          profile,
          userContext,
          recentUpdates,
          allUpdates
        );
        projectContext = customization.context;
        userSpecificDetails = customization.userDetails;
        completedDate = isCompleted ? getCompletionDateFromUpdates(milestone.id, allUpdates) : undefined;
      }

      return {
        id: milestone.id,
        title: submission?.metadata?.personalized_title || milestone.title,
        description: milestone.description,
        completed: isCompleted,
        required: milestone.required,
        projectContext,
        userSpecificDetails,
        aiInsight,
        completedDate,
        autoPopulated,
        personalizedTitle: submission?.metadata?.personalized_title,
        personalizedDescription: submission?.metadata?.personalized_description,
        personalizedDeliverables: submission?.metadata?.personalized_deliverables
      };
    });
  };

  const customizeMilestoneForProject = (
    milestone: any, 
    profile: any, 
    userContext: any, 
    recentUpdates: any[],
    allUpdates: any[]
  ): { context: string; userDetails: string } => {
    
    const projectType = profile?.project_viability_score > 80 ? 'high-potential' : 'developing';
    const hasTeam = userContext?.project_info?.team_size > 1;
    
    // Extract user-specific details from their updates
    const userDetails = extractUserSpecificDetails(milestone.id, recentUpdates, allUpdates);
    
    // Customize based on project context and user's actual mentions
    switch (milestone.id) {
      case 'team_building':
        if (userDetails.includes('co-founder') || userDetails.includes('teammate') || userDetails.includes('partner')) {
          return {
            context: `Great! You mentioned: "${userDetails}". Focus on defining clear roles and establishing effective collaboration workflows.`,
            userDetails
          };
        }
        return {
          context: hasTeam ? 
            'You\'ve assembled a team - now focus on clear role definition and collaboration workflows' :
            'Based on your project scope, consider if you need co-founders or key team members for critical skills',
          userDetails: ''
        };
      
      case 'concept_clarity':
        if (userDetails.includes('planned out') || userDetails.includes('clear vision') || userDetails.includes('defined')) {
          return {
            context: `Excellent progress: "${userDetails}". Ensure your value proposition clearly addresses your target users' specific problems.`,
            userDetails
          };
        }
        return {
          context: `For your ${profile?.project_viability_score > 70 ? 'promising' : 'developing'} concept: ensure your value proposition clearly addresses the specific problem you identified`,
          userDetails: ''
        };
      
      case 'technical_challenges':
        if (userDetails.includes('sensor') || userDetails.includes('technical') || userDetails.includes('implementation')) {
          return {
            context: `Working through technical challenges: "${userDetails}". Break complex problems into smaller, testable components.`,
            userDetails
          };
        }
        if (profile?.technical_feasibility_analysis?.includes('complex')) {
          return {
            context: 'Your technical approach has complexity - break it into smaller, testable components and validate each part',
            userDetails: ''
          };
        }
        return {
          context: 'Work through the core technical implementation challenges, focusing on proving feasibility of your key assumptions',
          userDetails: ''
        };
      
      case 'stage_completion':
        if (userDetails.includes('finished stage') || userDetails.includes('completed stage') || userDetails.includes('done with')) {
          return {
            context: `You indicated: "${userDetails}". Ready to advance to the next stage with solid foundations.`,
            userDetails
          };
        }
        return {
          context: milestone.description,
          userDetails: ''
        };
      
      default:
        return {
          context: userDetails ? `Progress noted: "${userDetails}". ${milestone.description}` : milestone.description,
          userDetails: userDetails || ''
        };
    }
  };

  const extractUserSpecificDetails = (milestoneId: string, recentUpdates: any[], allUpdates: any[]): string => {
    // Combine recent and all updates for comprehensive analysis
    const combinedUpdates = [...recentUpdates, ...allUpdates].slice(0, 10);
    
    const relevantPhrases: { [key: string]: string[] } = {
      'team_building': [
        'co-founder', 'teammate', 'partner', 'joined team', 'new team member', 
        'hired', 'collaborator', 'working with', 'team up'
      ],
      'concept_clarity': [
        'planned out', 'clear vision', 'defined concept', 'figured out', 
        'clarity on', 'mapped out', 'outlined', 'structured'
      ],
      'technical_challenges': [
        'sensor', 'technical', 'implementation', 'coding', 'development',
        'architecture', 'algorithm', 'technical difficulty', 'engineering'
      ],
      'stage_completion': [
        'finished stage', 'completed stage', 'done with stage', 'stage complete',
        'ready for next', 'moving to stage'
      ]
    };

    const phrases = relevantPhrases[milestoneId] || [];
    
    for (const update of combinedUpdates) {
      const content = update.content || update.daily_update || '';
      if (typeof content === 'string') {
        for (const phrase of phrases) {
          if (content.toLowerCase().includes(phrase.toLowerCase())) {
            // Extract a relevant sentence or snippet containing the phrase
            const sentences = content.split(/[.!?]+/);
            const relevantSentence = sentences.find(s => 
              s.toLowerCase().includes(phrase.toLowerCase())
            );
            if (relevantSentence) {
              return relevantSentence.trim();
            }
          }
        }
      }
    }
    
    return '';
  };

  const getCompletionDateFromUpdates = (milestoneId: string, allUpdates: any[]): string => {
    // Find the update where this milestone was likely completed
    for (const update of allUpdates) {
      const content = update.content || update.daily_update || '';
      if (typeof content === 'string' && content.toLowerCase().includes(milestoneId.replace('_', ' '))) {
        return update.created_at || new Date().toISOString();
      }
    }
    return new Date().toISOString();
  };

  const handleSaveNotes = async () => {
    if (!stageNotes.trim()) return;
    
    setIsLoading(true);
    try {
      // Store notes and get AI analysis
      await userMemoryService.storeMemory({
        user_id: userId,
        memory_type: 'progress',
        content: {
          stage_id: stage.id,
          notes: stageNotes,
          timestamp: new Date().toISOString()
        },
        metadata: {
          stage_id: stage.id,
          source: 'stage_notes'
        }
      });

      // Analyze notes for milestone progress
      await stageProgressService.analyzeUpdateForProgress(
        userId,
        stageNotes,
        stage.id
      );
      
      setStageNotes('');
      await loadProjectSpecificMilestones(); // Refresh to show updates
      onStageUpdate?.();
    } catch (error) {
      console.error('Error saving stage notes:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleMilestone = async (milestoneId: string) => {
    const milestone = projectMilestones.find(m => m.id === milestoneId);
    if (!milestone) return;

    // Prevent unchecking auto-populated milestones
    if (milestone.autoPopulated && milestone.completed) {
      console.log('Cannot uncheck auto-populated milestone:', milestoneId);
      return;
    }

    // For now, just visual toggle - in reality this would be driven by AI analysis of updates
    const updatedMilestones = projectMilestones.map(m =>
      m.id === milestoneId
        ? { ...m, completed: !m.completed, completedDate: !m.completed ? new Date().toISOString() : undefined }
        : m
    );

    setProjectMilestones(updatedMilestones);

    // Update progress
    const completedRequired = updatedMilestones.filter(m => m.required && m.completed).length;
    const totalRequired = updatedMilestones.filter(m => m.required).length;
    const progressPercentage = totalRequired > 0 ? Math.round((completedRequired / totalRequired) * 100) : 0;
    setStageProgress(progressPercentage);
  };

  const handleMilestoneInput = async (milestoneId: string, input: string) => {
    if (!input.trim()) return;

    try {
      setIsSubmittingInput(true);

      // Track milestone submission activity
      await activityTrackingService.trackMilestoneSubmission(
        stage.id,
        milestoneId,
        input
      );

      // Store the input in user memory with context
      await userMemoryService.storeMemory({
        user_id: userId,
        memory_type: 'progress',
        content: {
          milestone_id: milestoneId,
          stage_id: stage.id,
          user_input: input,
          input_type: 'milestone_submission'
        },
        metadata: {
          stage_id: stage.id,
          milestone_id: milestoneId,
          input_timestamp: new Date().toISOString()
        }
      });

      // ENHANCED: Comprehensive AI analysis of the milestone submission
      console.log('🤖 Analyzing milestone submission with AI...');

      try {
        // Get user context for analysis
        const userContext = await userMemoryService.buildUserContext(userId);
        const milestone = projectMilestones.find(m => m.id === milestoneId);

        // Analyze the submission with AI
        const aiAnalysis = await geminiService.analyzeMilestoneSubmission(
          input,
          {
            milestoneTitle: milestone?.title || milestoneId,
            milestoneDescription: milestone?.description || '',
            stageId: stage.id,
            stageTitle: stage.title,
            userContext: userContext,
            projectContext: milestone?.projectContext || ''
          }
        );

        if (aiAnalysis) {
          // Store AI insights in user memory
          await userMemoryService.storeInsight(
            userId,
            aiAnalysis.insights || aiAnalysis.coachingInsight || 'Milestone completed successfully',
            'milestone_analysis',
            aiAnalysis.confidence || 75
          );

          // Update milestone with AI insights
          setProjectMilestones(prev =>
            prev.map(m =>
              m.id === milestoneId
                ? {
                    ...m,
                    userSpecificDetails: input,
                    completed: true,
                    completedDate: new Date().toISOString(),
                    aiInsight: aiAnalysis.insights || aiAnalysis.coachingInsight
                  }
                : m
            )
          );

          // Store progress in comprehensive profile
          await userMemoryService.storeProgress(
            userId,
            'milestone_completion',
            {
              milestone_id: milestoneId,
              milestone_title: milestone?.title,
              user_submission: input,
              ai_analysis: aiAnalysis,
              stage_context: {
                stage_id: stage.id,
                stage_title: stage.title
              }
            },
            stage.id
          );

          // Update comprehensive profile with AI insights
          if (aiAnalysis.keyLearnings && aiAnalysis.profileUpdates) {
            await userMemoryService.updateProfileFromAIAnalysis(
              userId,
              {
                keyLearnings: aiAnalysis.keyLearnings,
                profileUpdates: aiAnalysis.profileUpdates
              },
              {
                milestoneId: milestoneId,
                stageId: stage.id,
                submissionContent: input
              }
            );
          }

          console.log('✅ Milestone submission analyzed and embedded in profile');
        }
      } catch (error) {
        console.error('Error analyzing milestone submission:', error);
        // Continue with basic milestone completion even if AI analysis fails
      }

      // Update milestone with user input
      setProjectMilestones(prev => 
        prev.map(m => 
          m.id === milestoneId 
            ? { ...m, userSpecificDetails: input, completed: true, completedDate: new Date().toISOString() }
            : m
        )
      );

      // Update state
      setMilestoneInputs(prev => ({ ...prev, [milestoneId]: input }));
      setEditingMilestone(null);

      // Recalculate progress
      const updatedMilestones = projectMilestones.map(m => 
        m.id === milestoneId ? { ...m, completed: true } : m
      );
      const completedRequired = updatedMilestones.filter(m => m.required && m.completed).length;
      const totalRequired = updatedMilestones.filter(m => m.required).length;
      const progressPercentage = totalRequired > 0 ? Math.round((completedRequired / totalRequired) * 100) : 0;
      setStageProgress(progressPercentage);

      onStageUpdate?.();

    } catch (error) {
      console.error('Error processing milestone input:', error);
    } finally {
      setIsSubmittingInput(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`rounded-2xl border backdrop-blur-sm ${stageBorder} transition-all duration-300 ${
        isCurrentStage ? 'ring-2 ring-purple-500/30' : ''
      }`}
    >
      <div 
        className={`p-6 cursor-pointer bg-gradient-to-br ${stageGradient} backdrop-blur-xl border-b border-accent-white/10`}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className={`p-3 rounded-xl ${isCompleted ? 'bg-green-500/30' : isCurrentStage ? 'bg-purple-500/30' : 'bg-accent-white/20'}`}>
              {isCompleted ? (
                <CheckCircle className="h-6 w-6 text-green-400" />
              ) : (
                <IconComponent className={`h-6 w-6 ${isCurrentStage ? 'text-purple-400' : 'text-accent-white/80'}`} />
              )}
            </div>
            
            <div>
              <div className="flex items-center gap-3">
                <h3 className={`text-lg font-semibold ${isCurrentStage ? 'text-accent-white' : 'text-accent-white/90'}`}>
                  Stage {stage.id}: {stage.title}
                </h3>
                {isCurrentStage && (
                  <Badge variant="outline" className="text-xs text-purple-400 border-purple-400/50 bg-purple-500/20">
                    Current
                  </Badge>
                )}
                {isCompleted && (
                  <Badge variant="outline" className="text-xs text-green-400 border-green-400/50 bg-green-500/20">
                    Completed
                  </Badge>
                )}
              </div>
              <p className={`text-sm mt-1 ${isCurrentStage ? 'text-accent-white/80' : 'text-accent-white/70'}`}>
                {stage.description}
              </p>
              
              {isCurrentStage && stageProgress > 0 && (
                <div className="mt-3">
                  <div className="flex justify-between text-xs text-accent-white/80 mb-1">
                    <span>Progress</span>
                    <span>{stageProgress}%</span>
                  </div>
                  <Progress value={stageProgress} className="h-1.5" />
                </div>
              )}
            </div>
          </div>
          
          <motion.div
            animate={{ rotate: isExpanded ? 90 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ArrowRight className="h-5 w-5 text-accent-white/70" />
          </motion.div>
        </div>
      </div>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden bg-oven-black/20 backdrop-blur-sm"
          >
            <div className="p-6 pt-4 space-y-6">
              {/* Project-Specific Milestones */}
              {projectMilestones.length > 0 && (
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-purple-400" />
                    <h4 className="text-accent-white font-medium">Your Project Milestones</h4>
                  </div>
                  
                  <div className="space-y-3">
                    {projectMilestones.map((milestone) => (
                      <motion.div
                        key={milestone.id}
                        whileHover={{ x: 5 }}
                        className={`p-4 rounded-lg border ${
                          milestone.completed 
                            ? 'bg-green-500/10 border-green-500/20' 
                            : milestone.required 
                              ? 'bg-purple-500/5 border-purple-500/20' 
                              : 'bg-accent-white/5 border-accent-white/10'
                        } transition-all ${
                          (!milestone.required || milestone.completed) &&
                          !(milestone.autoPopulated && milestone.completed)
                            ? 'cursor-pointer'
                            : milestone.autoPopulated && milestone.completed
                              ? 'cursor-not-allowed'
                              : ''
                        }`}
                        onClick={() => {
                          // Prevent unchecking auto-populated milestones
                          if (milestone.autoPopulated && milestone.completed) {
                            return;
                          }
                          if (!milestone.required || milestone.completed) {
                            toggleMilestone(milestone.id);
                          }
                        }}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`mt-1 p-1 rounded ${
                            milestone.completed ? 'bg-green-500/20' : 'bg-accent-white/10'
                          }`}>
                            {milestone.completed ? (
                              <CheckCircle className="h-3 w-3 text-green-400" />
                            ) : (
                              <div className={`w-3 h-3 rounded border-2 ${
                                milestone.required ? 'border-purple-400' : 'border-accent-white/30'
                              }`} />
                            )}
                          </div>
                          
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span className={`text-sm font-medium ${
                                milestone.completed ? 'text-green-400' : 'text-accent-white'
                              }`}>
                                {milestone.title}
                              </span>
                              {milestone.required && (
                                <Badge variant="outline" className="text-xs text-orange-400 border-orange-400/30">
                                  Required
                                </Badge>
                              )}
                              {milestone.autoPopulated && (
                                <Badge variant="outline" className="text-xs text-blue-400 border-blue-400/30">
                                  Auto-populated
                                </Badge>
                              )}
                            </div>
                            
                            {/* User-specific details highlighted */}
                            {milestone.userSpecificDetails && (
                              <div className="mt-2 p-2 bg-cheese-gold/10 border border-cheese-gold/20 rounded-md">
                                <div className="flex items-start gap-2">
                                  <Sparkles className="h-3 w-3 text-cheese-gold mt-0.5 flex-shrink-0" />
                                  <p className="text-xs text-cheese-gold font-medium">
                                    Your Response: "{milestone.userSpecificDetails}"
                                  </p>
                                </div>
                              </div>
                            )}

                            {/* AI Insight */}
                            {milestone.aiInsight && (
                              <div className="mt-2 p-2 bg-purple-500/10 border border-purple-500/20 rounded-md">
                                <div className="flex items-start gap-2">
                                  <Brain className="h-3 w-3 text-purple-400 mt-0.5 flex-shrink-0" />
                                  <p className="text-xs text-purple-300">
                                    {milestone.aiInsight}
                                  </p>
                                </div>
                              </div>
                            )}

                            <MarkdownText className="text-xs text-accent-white/70 mt-1">
                              {milestone.projectContext || milestone.description}
                            </MarkdownText>

                            {/* Personalized Deliverables */}
                            {milestone.personalizedDeliverables && milestone.personalizedDeliverables.length > 0 && (
                              <div className="mt-2 p-2 bg-cheese-gold/10 border border-cheese-gold/20 rounded-md">
                                <div className="flex items-start gap-2">
                                  <Target className="h-3 w-3 text-cheese-gold mt-0.5 flex-shrink-0" />
                                  <div className="space-y-1">
                                    <p className="text-xs text-cheese-gold font-medium">Key Deliverables:</p>
                                    <ul className="text-xs text-accent-white/80 space-y-0.5">
                                      {milestone.personalizedDeliverables.map((deliverable, idx) => (
                                        <li key={idx} className="flex items-start gap-1">
                                          <span className="text-cheese-gold/60 mt-0.5">•</span>
                                          <MarkdownText>{deliverable}</MarkdownText>
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Interactive Input for Required Milestones */}
                            {milestone.required && !milestone.completed && (
                              <div className="mt-3 space-y-2">
                                {editingMilestone === milestone.id ? (
                                  <div className="space-y-2">
                                    <Textarea
                                      placeholder={`Describe your ${milestone.title.toLowerCase()}...`}
                                      value={milestoneInputs[milestone.id] || ''}
                                      onChange={(e) => setMilestoneInputs(prev => ({
                                        ...prev,
                                        [milestone.id]: e.target.value
                                      }))}
                                      className="min-h-[100px] bg-accent-white/5 border-accent-white/20 text-accent-white resize-none"
                                    />
                                    <div className="flex gap-2">
                                      <Button
                                        size="sm"
                                        onClick={() => handleMilestoneInput(milestone.id, milestoneInputs[milestone.id] || '')}
                                        disabled={isSubmittingInput || !milestoneInputs[milestone.id]?.trim()}
                                        className="bg-purple-500/20 hover:bg-purple-500/30 text-purple-400 border-purple-500/30"
                                      >
                                        {isSubmittingInput ? (
                                          <>
                                            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                            Submitting...
                                          </>
                                        ) : (
                                          <>
                                            <Send className="h-3 w-3 mr-1" />
                                            Submit
                                          </>
                                        )}
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="ghost"
                                        onClick={() => setEditingMilestone(null)}
                                        className="text-accent-white/60 hover:text-accent-white"
                                      >
                                        Cancel
                                      </Button>
                                    </div>
                                  </div>
                                ) : (
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setEditingMilestone(milestone.id);
                                    }}
                                    className="text-purple-400 border-purple-500/30 hover:bg-purple-500/10 bg-transparent"
                                  >
                                    <Edit3 className="h-3 w-3 mr-1" />
                                    Add Details
                                  </Button>
                                )}
                              </div>
                            )}
                            
                            {milestone.completed && milestone.completedDate && (
                              <div className="flex items-center gap-1 mt-2">
                                <Clock className="h-3 w-3 text-accent-white/50" />
                                <span className="text-xs text-accent-white/50">
                                  Completed {new Date(milestone.completedDate).toLocaleDateString()}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              {/* Coaching Insight */}
              {coachingInsight && (
                <div className="p-4 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                  <div className="flex items-start gap-3">
                    <Brain className="h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-purple-400 mb-1">Coaching Insight</p>
                      <p className="text-sm text-accent-white/80">{coachingInsight}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Stage Notes */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-accent-white/60" />
                  <span className="text-sm text-accent-white/80">Add notes about your progress</span>
                </div>
                
                <Textarea
                  value={stageNotes}
                  onChange={(e) => setStageNotes(e.target.value)}
                  placeholder={`Share what you're working on for ${stage.title}...`}
                  className="bg-oven-black/50 border-accent-white/20 text-accent-white placeholder:text-accent-white/50 min-h-[80px] resize-none"
                />
                
                <Button
                  onClick={handleSaveNotes}
                  disabled={!stageNotes.trim() || isLoading}
                  size="sm"
                  className="bg-purple-500/20 hover:bg-purple-500/30 text-purple-400 border border-purple-500/30"
                >
                  {isLoading ? (
                    <>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="w-3 h-3 border border-purple-400 border-t-transparent rounded-full mr-2"
                      />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-3 w-3 mr-2" />
                      Save & Analyze
                    </>
                  )}
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default DevJourneyStageTab; 