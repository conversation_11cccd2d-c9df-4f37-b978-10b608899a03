import { supabase } from '@/integrations/supabase/client';
import userMemoryService from './userMemoryService';
import devJourneyService from './devJourneyService';
import geminiService from './geminiService';

export interface StageProgress {
  stage_id: number;
  user_id: string;
  milestones_completed: string[];
  total_milestones: number;
  progress_percentage: number;
  last_update: string;
  confidence_score: number;
}

export interface StageMilestone {
  id: string;
  title: string;
  description: string;
  keywords: string[];
  required: boolean;
  completed: boolean;
  completed_date?: string;
}

class StageProgressService {
  // Enhanced milestones with better keyword detection and user language patterns
  private stageMilestones = {
    0: [ // Spark
      { 
        id: 'problem_identified', 
        title: 'Problem Identified', 
        description: 'Clearly defined a specific problem or opportunity', 
        keywords: ['problem', 'issue', 'pain point', 'opportunity', 'challenge', 'need', 'gap', 'frustration', 'difficulty'],
        userPhrases: ['identified the problem', 'found an issue', 'see a need for', 'people struggle with'],
        required: true 
      },
      { 
        id: 'market_research', 
        title: 'Market Research', 
        description: 'Researched target market and existing solutions', 
        keywords: ['research', 'market', 'competitors', 'analysis', 'study', 'investigate', 'explore', 'competitive'],
        userPhrases: ['did research', 'looked into', 'studied the market', 'checked out competitors'],
        required: true 
      },
      { 
        id: 'user_interviews', 
        title: 'User Interviews', 
        description: 'Talked to potential users about the problem', 
        keywords: ['interview', 'user', 'feedback', 'conversation', 'talk', 'discuss', 'chat', 'survey'],
        userPhrases: ['talked to users', 'interviewed people', 'got feedback from', 'spoke with potential customers'],
        required: false 
      },
      { 
        id: 'idea_validation', 
        title: 'Idea Validation', 
        description: 'Validated that people care about this problem', 
        keywords: ['validation', 'confirm', 'interest', 'demand', 'validate', 'verify', 'proof', 'evidence'],
        userPhrases: ['validated the idea', 'confirmed demand', 'people are interested', 'found validation'],
        required: true 
      }
    ],
    1: [ // Formation
      { 
        id: 'concept_clarity', 
        title: 'Concept Clarity', 
        description: 'Clear vision and value proposition defined', 
        keywords: ['vision', 'concept', 'value proposition', 'clarity', 'clear', 'defined', 'planned out', 'figured out'],
        userPhrases: ['have everything planned out', 'vision is clear', 'figured out the concept', 'defined our approach'],
        required: true 
      },
      { 
        id: 'team_building', 
        title: 'Team Building', 
        description: 'Assembled core team or identified needed skills', 
        keywords: ['team', 'cofounder', 'partner', 'collaborate', 'teammate', 'member', 'hire', 'recruit', 'join'],
        userPhrases: ['got a new teammate', 'found a cofounder', 'team member joined', 'recruited someone', 'new team member'],
        required: false 
      },
      { 
        id: 'milestone_planning', 
        title: 'Milestone Planning', 
        description: 'Created roadmap and timeline', 
        keywords: ['plan', 'roadmap', 'timeline', 'milestones', 'schedule', 'steps', 'phases', 'strategy'],
        userPhrases: ['created a roadmap', 'planned the milestones', 'set up timeline', 'mapped out steps'],
        required: true 
      },
      { 
        id: 'feedback_loops', 
        title: 'Feedback Loops', 
        description: 'Established ways to get user feedback', 
        keywords: ['feedback', 'testing', 'user input', 'iteration', 'loop', 'process', 'system'],
        userPhrases: ['set up feedback system', 'created testing process', 'established feedback loops'],
        required: true 
      },
      {
        id: 'stage_completion',
        title: 'Stage 1 Completion',
        description: 'Explicitly completed Formation stage',
        keywords: ['finished stage 1', 'completed formation', 'done with stage 1', 'stage 1 complete'],
        userPhrases: ['just finished stage 1', 'completed stage 1', 'done with formation stage', 'finished formation'],
        required: true
      }
    ],
    2: [ // First Build
      { 
        id: 'mvp_designed', 
        title: 'MVP Designed', 
        description: 'Designed minimum viable product', 
        keywords: ['mvp', 'prototype', 'design', 'wireframe', 'mockup', 'sketch', 'blueprint'],
        userPhrases: ['designed the MVP', 'created wireframes', 'sketched the prototype'],
        required: true 
      },
      { 
        id: 'core_features', 
        title: 'Core Features', 
        description: 'Built essential functionality', 
        keywords: ['features', 'functionality', 'build', 'develop', 'code', 'implement', 'create'],
        userPhrases: ['built the features', 'implemented functionality', 'coded the core features'],
        required: true 
      },
      { 
        id: 'technical_challenges', 
        title: 'Technical Implementation', 
        description: 'Working through technical challenges and implementation', 
        keywords: ['technical', 'coding', 'development', 'programming', 'sensor', 'detection', 'algorithm', 'difficulty'],
        userPhrases: ['technical difficulties', 'coding challenges', 'implementation issues', 'development problems', 'sensor sensitivity'],
        required: true 
      },
      { 
        id: 'testing_framework', 
        title: 'Testing Framework', 
        description: 'Set up testing and validation methods', 
        keywords: ['testing', 'validation', 'qa', 'debugging', 'test', 'verify', 'check'],
        userPhrases: ['set up testing', 'testing the features', 'validating functionality'],
        required: true 
      },
      { 
        id: 'first_version', 
        title: 'First Version', 
        description: 'Completed working prototype', 
        keywords: ['prototype', 'working', 'demo', 'version', 'complete', 'finished', 'ready'],
        userPhrases: ['finished the prototype', 'completed first version', 'working demo ready'],
        required: true 
      }
    ],
    3: [ // Ship
      { 
        id: 'product_launched', 
        title: 'Product Launched', 
        description: 'Made product available to users', 
        keywords: ['launch', 'live', 'available', 'released', 'deployed', 'published', 'ship'],
        userPhrases: ['launched the product', 'went live', 'released to users'],
        required: true 
      },
      { 
        id: 'user_acquisition', 
        title: 'User Acquisition', 
        description: 'Started getting first users', 
        keywords: ['users', 'customers', 'acquisition', 'onboarding', 'signup', 'registration'],
        userPhrases: ['got first users', 'users signed up', 'customers onboarded'],
        required: true 
      },
      { 
        id: 'feedback_collection', 
        title: 'Feedback Collection', 
        description: 'Actively collecting user feedback', 
        keywords: ['feedback', 'reviews', 'suggestions', 'input', 'comments', 'responses'],
        userPhrases: ['collecting feedback', 'got user reviews', 'users providing input'],
        required: true 
      },
      { 
        id: 'iteration_cycle', 
        title: 'Iteration Cycle', 
        description: 'Regular updates based on feedback', 
        keywords: ['iterate', 'improve', 'update', 'enhance', 'refine', 'fix'],
        userPhrases: ['iterated based on feedback', 'improved the product', 'updated features'],
        required: true 
      }
    ],
    4: [ // Traction
      { 
        id: 'growth_metrics', 
        title: 'Growth Metrics', 
        description: 'Tracking key growth indicators', 
        keywords: ['metrics', 'analytics', 'growth', 'kpi', 'data', 'tracking', 'measure'],
        userPhrases: ['tracking metrics', 'measuring growth', 'analyzing data'],
        required: true 
      },
      { 
        id: 'user_retention', 
        title: 'User Retention', 
        description: 'Users coming back regularly', 
        keywords: ['retention', 'engagement', 'recurring', 'loyal', 'repeat', 'active'],
        userPhrases: ['users coming back', 'good retention', 'engaged users'],
        required: true 
      },
      { 
        id: 'optimization', 
        title: 'Optimization', 
        description: 'Optimizing conversion and experience', 
        keywords: ['optimize', 'conversion', 'improve', 'efficiency', 'performance', 'enhance'],
        userPhrases: ['optimizing conversion', 'improving performance', 'enhancing experience'],
        required: true 
      },
      { 
        id: 'scaling_systems', 
        title: 'Scaling Systems', 
        description: 'Building systems for growth', 
        keywords: ['scale', 'automation', 'systems', 'infrastructure', 'capacity', 'growth'],
        userPhrases: ['scaling systems', 'building infrastructure', 'preparing for growth'],
        required: false 
      }
    ],
    5: [ // Real Company
      { 
        id: 'business_model', 
        title: 'Business Model', 
        description: 'Proven sustainable business model', 
        keywords: ['revenue', 'business model', 'monetization', 'profit', 'income', 'sales'],
        userPhrases: ['generating revenue', 'profitable business model', 'making money'],
        required: true 
      },
      { 
        id: 'team_structure', 
        title: 'Team Structure', 
        description: 'Organized team with defined roles', 
        keywords: ['team', 'roles', 'organization', 'structure', 'management', 'hierarchy'],
        userPhrases: ['organized team', 'defined roles', 'team structure'],
        required: true 
      },
      { 
        id: 'market_position', 
        title: 'Market Position', 
        description: 'Established position in the market', 
        keywords: ['market', 'position', 'competitive', 'advantage', 'leader', 'established'],
        userPhrases: ['market position', 'competitive advantage', 'established in market'],
        required: true 
      },
      { 
        id: 'growth_planning', 
        title: 'Growth Planning', 
        description: 'Strategic plan for future growth', 
        keywords: ['strategy', 'planning', 'expansion', 'future', 'growth plan', 'roadmap'],
        userPhrases: ['strategic planning', 'growth strategy', 'expansion plans'],
        required: true 
      }
    ]
  };

  async analyzeUpdateForProgress(userId: string, updateContent: string, currentStage: number): Promise<StageProgress | null> {
    try {
      // Check if this specific update has already been analyzed for milestone progress
      const { data: existingUpdate } = await supabase
        .from('daily_updates')
        .select('ai_insights')
        .eq('user_id', userId)
        .eq('content', updateContent)
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (existingUpdate?.ai_insights &&
          typeof existingUpdate.ai_insights === 'object' &&
          Object.keys(existingUpdate.ai_insights).length > 0) {
        console.log('✅ Update already analyzed for milestone progress, skipping...');
        return null;
      }

      console.log(`🔍 Analyzing update for stage ${currentStage} progress...`);

      // Get user context for AI analysis
      const userContext = await userMemoryService.buildUserContext(userId);
      
      // Use AI for intelligent milestone detection
      const aiAnalysis = await geminiService.analyzeUpdateForStageProgress(
        userId,
        updateContent,
        currentStage,
        userContext
      );

      console.log('🤖 AI milestone analysis:', aiAnalysis);

      let completedMilestones: string[] = [];
      let stageAdjustment: number | null = null;

      if (aiAnalysis) {
        completedMilestones = aiAnalysis.detectedMilestones;
        
        // Handle stage progression adjustments
        if (aiAnalysis.stageProgression === 'forward' && aiAnalysis.suggestedStage) {
          stageAdjustment = aiAnalysis.suggestedStage;
        } else if (aiAnalysis.stageProgression === 'backward' && aiAnalysis.suggestedStage) {
          stageAdjustment = aiAnalysis.suggestedStage;
        }
        
        // Store the coaching insight for later use
        await userMemoryService.storeInsight(
          userId,
          aiAnalysis.coachingInsight,
          'coaching_insight',
          aiAnalysis.confidence
        );
      }

      // Fallback to keyword detection if AI doesn't detect milestones
      if (completedMilestones.length === 0) {
        console.log('🔄 Falling back to keyword detection...');
        completedMilestones = this.detectMilestonesWithKeywords(updateContent, currentStage);
      }

      if (completedMilestones.length === 0 && !stageAdjustment) {
        console.log('❌ No milestones or stage adjustments detected');
        return null;
      }

      console.log(`🎯 Final detected milestones:`, completedMilestones);
      if (stageAdjustment) console.log(`📊 Stage adjustment suggested: ${currentStage} → ${stageAdjustment}`);

      // Get existing progress
      const { data: existingProgress, error: progressError } = await supabase
        .from('user_stage_progress')
        .select('*')
        .eq('user_id', userId)
        .eq('stage_id', stageAdjustment || currentStage)
        .maybeSingle();

      if (progressError) {
        console.log('Stage progress table not fully available, creating new progress');
      }

      // Calculate progress for the appropriate stage
      const targetStage = stageAdjustment || currentStage;
      const milestones = this.stageMilestones[targetStage as keyof typeof this.stageMilestones] || [];
      
      // Merge with existing completed milestones
      const allCompleted = existingProgress 
        ? [...new Set([...existingProgress.milestones_completed, ...completedMilestones])]
        : completedMilestones;

      // Calculate progress percentage
      const requiredMilestones = milestones.filter(m => m.required).length;
      const completedRequired = allCompleted.filter(id => {
        const milestone = milestones.find(m => m.id === id);
        return milestone && milestone.required;
      }).length;
      
      const progressPercentage = requiredMilestones > 0 ? 
        Math.min(100, Math.round((completedRequired / requiredMilestones) * 100)) : 0;

      console.log(`📊 Progress for stage ${targetStage}: ${completedRequired}/${requiredMilestones} required milestones = ${progressPercentage}%`);

      // Enhanced confidence calculation with AI input
      let confidence = Math.min(100, progressPercentage + (allCompleted.length * 5));
      if (aiAnalysis) {
        confidence = Math.round((confidence + aiAnalysis.confidence) / 2); // Average with AI confidence
      }

      // Check for stage completion indicators
      const hasStageCompletionIndicator = completedMilestones.some(id => id.includes('stage_completion')) ||
        updateContent.toLowerCase().includes('finished stage') || 
        updateContent.toLowerCase().includes('completed stage') ||
        updateContent.toLowerCase().includes('done with stage');

      if (hasStageCompletionIndicator) {
        confidence = Math.min(100, confidence + 20);
      }

      // Update or create progress record
      const progressData = {
        user_id: userId,
        stage_id: targetStage,
        milestones_completed: allCompleted,
        total_milestones: milestones.length,
        progress_percentage: progressPercentage,
        last_update: new Date().toISOString(),
        confidence_score: confidence
      };

      try {
        const { data, error } = await supabase
          .from('user_stage_progress')
          .upsert(progressData, {
            onConflict: 'user_id,stage_id'
          })
          .select()
          .maybeSingle();

        if (error) {
          console.error('Error updating stage progress:', error);
          return progressData as StageProgress;
        }

        // Store comprehensive progress update in user memory
        await userMemoryService.storeProgress(
          userId,
          'ai_milestone_analysis',
          {
            milestones_completed: completedMilestones,
            update_content: updateContent,
            progress_percentage: progressPercentage,
            ai_analysis: aiAnalysis,
            stage_adjustment: stageAdjustment,
            has_stage_completion_indicator: hasStageCompletionIndicator
          },
          targetStage
        );

        console.log(`✅ Stage progress updated successfully:`, data);
        return data;
      } catch (error) {
        console.error('Error in stage progress update:', error);
        return progressData as StageProgress;
      }
    } catch (error) {
      console.error('Error analyzing update for progress:', error);
      return null;
    }
  }

  // Fallback keyword detection method
  private detectMilestonesWithKeywords(updateContent: string, currentStage: number): string[] {
    const milestones = this.stageMilestones[currentStage as keyof typeof this.stageMilestones] || [];
    const completedMilestones: string[] = [];
    const updateLower = updateContent.toLowerCase();
    
    milestones.forEach(milestone => {
      let isCompleted = false;
      
      // Check for exact phrase matches first
      if (milestone.userPhrases) {
        const hasExactPhrase = milestone.userPhrases.some(phrase => 
          updateLower.includes(phrase.toLowerCase())
        );
        if (hasExactPhrase) {
          isCompleted = true;
          console.log(`✅ Keyword phrase match: ${milestone.id}`);
        }
      }
      
      // Check for keyword matches
      if (!isCompleted) {
        const hasKeywords = milestone.keywords.some(keyword => 
          updateLower.includes(keyword.toLowerCase())
        );
        if (hasKeywords) {
          isCompleted = true;
          console.log(`📝 Keyword match: ${milestone.id}`);
        }
      }
      
      if (isCompleted) {
        completedMilestones.push(milestone.id);
      }
    });

    return completedMilestones;
  }

  async getCurrentStageProgress(userId: string, stageId: number): Promise<StageProgress | null> {
    try {
      // First try to get from our new stage_progress table
      const { data: stageProgressData, error: stageError } = await supabase
        .from('stage_progress')
        .select('*')
        .eq('user_id', userId)
        .eq('stage_id', stageId)
        .maybeSingle();

      if (stageProgressData) {
        return {
          stage_id: stageProgressData.stage_id,
          user_id: stageProgressData.user_id,
          milestones_completed: stageProgressData.milestones_completed || [],
          total_milestones: stageProgressData.total_milestones || 0,
          progress_percentage: stageProgressData.progress_percentage || 0,
          last_update: stageProgressData.updated_at || stageProgressData.created_at,
          confidence_score: stageProgressData.confidence_score || 0
        };
      }

      // Fallback to calculating from milestone submissions
      const { data: submissions, error: submissionError } = await supabase
        .from('milestone_submissions')
        .select('milestone_id, is_approved, completed')
        .eq('user_id', userId)
        .eq('stage_id', stageId);

      if (submissionError) {
        console.error('Error fetching milestone submissions:', submissionError);
        return null;
      }

      const completedMilestones = submissions
        ?.filter(s => s.is_approved || s.completed)
        ?.map(s => s.milestone_id) || [];

      const stageMilestones = this.getStageMilestones(stageId);
      const requiredMilestones = stageMilestones.filter(m => m.required).length;
      const completedRequired = completedMilestones.filter(id => {
        const milestone = stageMilestones.find(m => m.id === id);
        return milestone && milestone.required;
      }).length;

      const progressPercentage = requiredMilestones > 0 ?
        Math.round((completedRequired / requiredMilestones) * 100) : 0;

      // Update the stage_progress table with calculated values
      await this.updateStageProgress(userId, stageId, {
        milestones_completed: completedMilestones,
        total_milestones: stageMilestones.length,
        progress_percentage: progressPercentage,
        confidence_score: Math.min(100, progressPercentage + (completedMilestones.length * 5)),
        is_stage_complete: progressPercentage === 100
      });

      return {
        stage_id: stageId,
        user_id: userId,
        milestones_completed: completedMilestones,
        total_milestones: stageMilestones.length,
        progress_percentage: progressPercentage,
        last_update: new Date().toISOString(),
        confidence_score: Math.min(100, progressPercentage + (completedMilestones.length * 5))
      };
    } catch (error) {
      console.error('Error in getCurrentStageProgress:', error);
      return null;
    }
  }

  async updateStageProgress(userId: string, stageId: number, progressData: {
    milestones_completed: string[];
    total_milestones: number;
    progress_percentage: number;
    confidence_score: number;
    is_stage_complete: boolean;
  }) {
    try {
      const { data, error } = await supabase
        .from('stage_progress')
        .upsert({
          user_id: userId,
          stage_id: stageId,
          milestones_completed: progressData.milestones_completed,
          total_milestones: progressData.total_milestones,
          progress_percentage: progressData.progress_percentage,
          confidence_score: progressData.confidence_score,
          is_stage_complete: progressData.is_stage_complete,
          stage_completed_at: progressData.is_stage_complete ? new Date().toISOString() : null,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id,stage_id'
        })
        .select()
        .maybeSingle();

      if (error) {
        console.error('Error updating stage progress:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in updateStageProgress:', error);
      return null;
    }
  }

  getStageMilestones(stageId: number) {
    const milestones = this.stageMilestones[stageId as keyof typeof this.stageMilestones] || [];
    return milestones.map(m => ({ ...m, completed: false }));
  }

  async getStageMilestonesWithProgress(userId: string, stageId: number): Promise<StageMilestone[]> {
    try {
      const progress = await this.getCurrentStageProgress(userId, stageId);
      const baseMilestones = this.getStageMilestones(stageId);
      
      return baseMilestones.map(milestone => ({
        ...milestone,
        completed: progress?.milestones_completed?.includes(milestone.id) || false
      }));
    } catch (error) {
      console.error('Error getting stage milestones with progress:', error);
      return this.getStageMilestones(stageId);
    }
  }

  // Enhanced stage advancement checking
  async checkForStageAdvancement(userId: string, currentStage: number): Promise<{ shouldAdvance: boolean; nextStage?: number; reason?: string }> {
    try {
      const progress = await this.getCurrentStageProgress(userId, currentStage);
      
      if (!progress) {
        return { shouldAdvance: false };
      }

      console.log(`🔍 Checking stage advancement for stage ${currentStage}:`, progress);

      // More lenient advancement criteria
      const hasHighProgress = progress.progress_percentage >= 70; // Reduced from 80%
      const hasStageCompletionMilestone = progress.milestones_completed.some(id => 
        id.includes('stage_completion')
      );
      
      // Check if user explicitly mentioned stage completion
      const hasExplicitCompletion = progress.milestones_completed.length > 0 && 
        progress.confidence_score >= 75;

      const shouldAdvance = hasHighProgress || hasStageCompletionMilestone || hasExplicitCompletion;

      if (!shouldAdvance) {
        console.log(`❌ Not ready for advancement: progress=${progress.progress_percentage}%, confidence=${progress.confidence_score}%`);
        return { shouldAdvance: false };
      }

      // Check if user has demonstrated readiness for next stage
      const nextStage = currentStage + 1;
      if (nextStage > 5) {
        return { shouldAdvance: false };
      }

      const reason = hasStageCompletionMilestone ? 
        'Explicit stage completion detected' :
        hasHighProgress ? 
        `High progress completion (${progress.progress_percentage}%)` :
        'Strong milestone completion pattern';

      console.log(`🚀 Stage advancement approved: ${currentStage} → ${nextStage}. Reason: ${reason}`);

      return {
        shouldAdvance: true,
        nextStage,
        reason
      };
    } catch (error) {
      console.error('Error checking stage advancement:', error);
      return { shouldAdvance: false };
    }
  }

  async updateStageIfReady(userId: string): Promise<{ updated: boolean; newStage?: number; message?: string }> {
    try {
      // Get current user stage
      const { data: stageData } = await supabase
        .from('user_dev_journey_stages')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (!stageData) {
        return { updated: false, message: 'No current stage found' };
      }

      const advancement = await this.checkForStageAdvancement(userId, stageData.stage_id);
      
      if (!advancement.shouldAdvance) {
        return { updated: false, message: 'Not ready for stage advancement' };
      }

      // Update to next stage
      const { error } = await supabase
        .from('user_dev_journey_stages')
        .insert({
          user_id: userId,
          stage_id: advancement.nextStage!,
          confidence_score: 75, // Start with medium confidence for new stage
          reasoning: advancement.reason || 'Automatic advancement based on milestone completion',
          next_actions: devJourneyService.getStage(advancement.nextStage!)?.supportNeeded?.slice(0, 3) || [],
          applicable_frameworks: Object.keys(devJourneyService.getStage(advancement.nextStage!)?.frameworks || {})
        });

      if (error) {
        console.error('Error updating user stage:', error);
        return { updated: false, message: 'Error updating stage' };
      }

      // Store stage advancement in memory
      await userMemoryService.storeMemory({
        user_id: userId,
        memory_type: 'progress',
        content: {
          stage_advancement: true,
          from_stage: stageData.stage_id,
          to_stage: advancement.nextStage,
          reason: advancement.reason
        },
        metadata: {
          stage_id: advancement.nextStage,
          confidence_score: 75,
          source: 'automatic_advancement'
        }
      });

      return {
        updated: true,
        newStage: advancement.nextStage,
        message: `Congratulations! You've advanced to ${devJourneyService.getStage(advancement.nextStage!)?.name} stage!`
      };
    } catch (error) {
      console.error('Error updating stage:', error);
      return { updated: false, message: 'Error checking for stage update' };
    }
  }
}

const stageProgressService = new StageProgressService();
export default stageProgressService; 