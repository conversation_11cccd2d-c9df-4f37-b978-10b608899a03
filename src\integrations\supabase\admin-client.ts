// Admin client with service role for secure admin operations
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://xmqexzoockycregsmyur.supabase.co";
// Service role key for admin operations - only use server-side or in admin context
const SUPABASE_SERVICE_ROLE_KEY = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_SERVICE_ROLE_KEY) {
  throw new Error('VITE_SUPABASE_SERVICE_ROLE_KEY environment variable is required');
}

// Admin client with elevated privileges
export const supabaseAdmin = createClient<Database>(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
}); 