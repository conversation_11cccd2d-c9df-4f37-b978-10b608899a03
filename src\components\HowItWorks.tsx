import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Calendar, Users, Award, Presentation, ShieldCheck } from 'lucide-react';

const programPillars = [
  {
    icon: Users,
    title: '10 Teams',
    description: 'Elite Santa Cruz crews. Diverse. Driven. Ready to build.',
  },
  {
    icon: Calendar,
    title: '10 Weeks',
    description: 'Fast-paced sprint. Build, iterate, ship, repeat.',
  },
  {
    icon: Award,
    title: '$10K+ Resources',
    description: 'Credits, tools, legal support, and potential funding.',
  },
  {
    icon: Presentation,
    title: '1 Demo Day',
    description: 'Your big moment. Investors, media, and the spotlight.',
  },
];

const HowItWorks = () => {
  return (
    <section id="program" className="animated-bg section-padding particles">
      <div className="max-w-5xl mx-auto">
        <h2 className="section-title text-accent-white">
          The <span className="text-sauce-red">10-in-10</span> Challenge
        </h2>
        <p className="section-subtitle text-crust-beige/80">
          Join the 10-in-10 challenge: 10 teams, 10 weeks, $10K+ in real resources. Ship weekly, get mentored, and pitch at Demo Day.
        </p>

        <div className="mt-12 grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {programPillars.map((pillar) => (
            <Card key={pillar.title} className="card-consistent h-full flex flex-col justify-between text-center hover:border-sauce-red/30 transition-all duration-300 transform hover:scale-105">
              <CardHeader>
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-sauce-red/10 mb-4">
                  <pillar.icon className="h-6 w-6 text-sauce-red" />
                </div>
                <CardTitle className="text-xl font-semibold text-oven-black">{pillar.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-oven-black/70">{pillar.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-16 text-center card-consistent p-8">
          <ShieldCheck className="h-12 w-12 text-cheese-gold mx-auto mb-4" />
          <h3 className="text-2xl font-semibold text-oven-black mb-2">Real Talk: This Is An Experiment</h3>
          <p className="text-oven-black/80 max-w-2xl mx-auto">
            Pie Fi is Santa Cruz's first founder accelerator. We're building this program with you, not for you. 
            What we <span className="font-semibold text-sauce-red">do guarantee</span>: serious mentorship, real resources, and a community that will push you to ship something meaningful.
          </p>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
