-- Add Weekly Goals table for structured goal management
CREATE TABLE IF NOT EXISTS weekly_goals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES user_projects(id) ON DELETE CASCADE,
  title VARCHAR NOT NULL,
  description TEXT,
  priority VARCHAR DEFAULT 'medium' CHECK (priority IN ('high', 'medium', 'low')),
  category VARCHAR DEFAULT 'general',
  completed BOOLEAN DEFAULT false,
  completed_date TIMESTAMP WITH TIME ZONE,
  week_of DATE NOT NULL,
  source VARCHAR DEFAULT 'manual' CHECK (source IN ('manual', 'ai_generated', 'daily_update_ai', 'onboarding')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_weekly_goals_user_id ON weekly_goals(user_id);
CREATE INDEX IF NOT EXISTS idx_weekly_goals_project_id ON weekly_goals(project_id);
CREATE INDEX IF NOT EXISTS idx_weekly_goals_week_of ON weekly_goals(week_of);
CREATE INDEX IF NOT EXISTS idx_weekly_goals_priority ON weekly_goals(priority);

-- RLS policies
ALTER TABLE weekly_goals ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own goals" ON weekly_goals
  FOR ALL USING (auth.uid() = user_id);

-- Updated trigger
CREATE TRIGGER update_weekly_goals_updated_at
  BEFORE UPDATE ON weekly_goals
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 