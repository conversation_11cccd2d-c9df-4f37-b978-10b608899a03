-- Create access_codes table for managing application access codes
CREATE TABLE IF NOT EXISTS access_codes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  description TEXT,
  usage_limit INTEGER DEFAULT NULL, -- NULL means unlimited
  usage_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL -- NULL means no expiration
);

-- Add access_code_used and application_type columns to applications table
ALTER TABLE applications 
ADD COLUMN IF NOT EXISTS access_code_used VARCHAR(50),
ADD COLUMN IF NOT EXISTS application_type VARCHAR(20) DEFAULT 'builder';

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_access_codes_code ON access_codes(code);
CREATE INDEX IF NOT EXISTS idx_access_codes_active ON access_codes(is_active);
CREATE INDEX IF NOT EXISTS idx_applications_access_code ON applications(access_code_used);
CREATE INDEX IF NOT EXISTS idx_applications_type ON applications(application_type);

-- Insert default access codes
INSERT INTO access_codes (code, description, usage_limit, is_active) VALUES 
  ('PIEFI2025', 'General access code for Pie Fi 2025', 100, true),
  ('EARLYBIRD2025', 'Early bird special access', 50, true),
  ('DEMO_DAY_25', 'Demo day attendees access', 30, true),
  ('MENTOR_2025', 'Mentor network access', 25, true),
  ('ALUMNI_25', 'Alumni access code', 40, true)
ON CONFLICT (code) DO NOTHING;
