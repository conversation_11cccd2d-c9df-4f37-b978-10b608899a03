import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowR<PERSON>, Rocket, Zap } from 'lucide-react';
import { motion } from 'framer-motion';
import { useScrollAnimation, scrollVariants, scaleInVariants } from '@/hooks/useScrollAnimation';
import { useIsMobile } from '@/hooks/use-mobile';

const TenInTenStats = () => {
  const { ref, isInView } = useScrollAnimation();
  const isMobile = useIsMobile();

  const handleLearnMoreClick = () => {
    window.location.href = '/ten-in-ten';
  };

  return (
    <section 
      className="relative py-16 sm:py-24 lg:py-32 overflow-hidden bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black" 
      ref={ref}
    >
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        {/* Main gradient orb */}
        <motion.div 
          className="absolute top-1/4 right-1/4 w-96 h-96 rounded-full bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 blur-3xl"
          animate={!isMobile ? {
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          } : undefined}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        
        {/* Secondary orb */}
        <motion.div 
          className="absolute bottom-1/4 left-1/4 w-64 h-64 rounded-full bg-gradient-to-r from-purple-500/15 to-blue-500/15 blur-2xl"
          animate={!isMobile ? {
            scale: [1.2, 1, 1.2],
            x: [0, 50, 0],
            y: [0, -30, 0],
          } : undefined}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Floating geometric shapes */}
        {(isMobile ? [...Array(6)] : [...Array(10)]).map((_, i) => (
          <motion.div
            key={i}
            className={`absolute w-4 h-4 ${i % 2 === 0 ? 'bg-cheese-gold/40' : 'bg-sauce-red/40'} rounded-full`}
            style={{
              left: `${20 + (i * 15)}%`,
              top: `${30 + (i * 10)}%`,
            }}
            animate={!isMobile ? {
              y: [-20, 20, -20],
              x: [-10, 10, -10],
              opacity: [0.3, 0.8, 0.3],
            } : undefined}
            transition={{
              duration: 4 + i,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.5,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Build-up Introduction */}
        <motion.div
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={scrollVariants}
          className="mb-12 sm:mb-16"
        >
          <motion.div
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 backdrop-blur-sm border border-sauce-red/20 rounded-full text-sauce-red font-semibold text-sm mb-8"
            whileHover={!isMobile ? { scale: 1.05 } : undefined}
          >
            🚀 Introducing the Program
          </motion.div>
          
          <motion.h2 
            className="text-4xl sm:text-5xl lg:text-6xl font-black text-accent-white mb-6 leading-tight"
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            The{" "}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red via-cheese-gold to-sauce-red">
              10 in 10
            </span>
            {" "}Program
          </motion.h2>
          
          <motion.p 
            className="text-xl sm:text-2xl text-crust-beige/90 max-w-4xl mx-auto mb-8 leading-relaxed"
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            Our flagship 10-week builder accelerator designed to turn ideas into shipped products
          </motion.p>
        </motion.div>
        
        {/* Stats Cards */}
        <motion.div 
          className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-5xl mx-auto mb-12"
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={scaleInVariants}
          transition={{ delay: 0.6 }}
        >
          {[
            { icon: Rocket, label: "10", desc: "Teams Selected", gradient: "from-blue-400 to-purple-500" },
            { icon: Zap, label: "10", desc: "Week Sprint", gradient: "from-sauce-red to-pink-500" },
            { label: "10", desc: "Pizzas Per Team", gradient: "from-cheese-gold to-orange-400", isEmoji: true },
            { icon: ArrowRight, label: "1", desc: "Demo Day", gradient: "from-green-400 to-blue-500" }
          ].map((item, index) => (
            <motion.div 
              key={index}
              className="relative group"
              whileHover={!isMobile ? { y: -10, scale: 1.05 } : undefined}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-xl" />
              
              <div className="relative bg-accent-white/10 backdrop-blur-md rounded-2xl p-6 border border-accent-white/20 hover:border-accent-white/40 transition-all duration-300 h-full flex flex-col justify-between">
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${item.gradient} ${item.isEmoji ? 'text-xl flex items-center justify-center' : 'p-3'} mx-auto mb-3 shadow-lg`}>
                  {item.isEmoji ? "🍕" : <item.icon className="w-6 h-6 text-white" />}
                </div>
                <div className="text-2xl font-bold text-accent-white mb-1">{item.label}</div>
                <div className="text-xs sm:text-sm text-crust-beige/80">{item.desc}</div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          className="space-y-6"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <p className="text-lg sm:text-xl text-crust-beige/80 max-w-2xl mx-auto">
            Ready to join the next cohort of builders?
          </p>
          
          <motion.div
            whileHover={!isMobile ? { scale: 1.05 } : undefined}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              onClick={handleLearnMoreClick}
              size="lg"
              className="bg-gradient-to-r from-sauce-red to-sauce-red/80 hover:from-sauce-red/90 hover:to-sauce-red/70 text-white px-8 py-4 text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Learn More About 10 in 10
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default TenInTenStats; 