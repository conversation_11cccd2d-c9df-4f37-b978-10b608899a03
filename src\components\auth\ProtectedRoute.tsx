import React, { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, loading } = useAuth();

  useEffect(() => {
    // Add debugging to understand routing decisions
    console.log('ProtectedRoute check:', { 
      user: user ? { id: user.id, onboarding_completed: user.onboarding_completed } : null,
      loading,
      currentPath: window.location.pathname 
    });

    // Check if user needs onboarding (but only if user object is fully loaded)
    if (user && !loading && !user.onboarding_completed && window.location.pathname !== '/onboarding') {
      console.log('Redirecting to onboarding - user has not completed onboarding');
      window.location.href = '/onboarding';
    }
  }, [user, loading]);

  if (loading) {
    return (
      <div className="min-h-screen bg-oven-black flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-cheese-gold" />
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Don't render children until user is fully loaded
  if (!user.onboarding_completed && window.location.pathname !== '/onboarding') {
    return (
      <div className="min-h-screen bg-oven-black flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-cheese-gold" />
      </div>
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute; 