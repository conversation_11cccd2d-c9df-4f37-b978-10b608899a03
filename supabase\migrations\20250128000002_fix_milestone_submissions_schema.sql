-- Fix milestone_submissions table schema
-- Add missing created_at column and improve indexing

-- Add created_at column if it doesn't exist
ALTER TABLE milestone_submissions 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Update existing records to have created_at = submission_date if created_at is null
UPDATE milestone_submissions 
SET created_at = submission_date 
WHERE created_at IS NULL AND submission_date IS NOT NULL;

-- Update existing records without submission_date to use current timestamp
UPDATE milestone_submissions 
SET created_at = NOW() 
WHERE created_at IS NULL;

-- Add updated_at column for better tracking
ALTER TABLE milestone_submissions 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Update existing records to have updated_at
UPDATE milestone_submissions 
SET updated_at = COALESCE(approval_date, submission_date, created_at, NOW()) 
WHERE updated_at IS NULL;

-- Create trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
R<PERSON>URNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop trigger if exists and recreate
DROP TRIGGER IF EXISTS update_milestone_submissions_updated_at ON milestone_submissions;
CREATE TRIGGER update_milestone_submissions_updated_at
    BEFORE UPDATE ON milestone_submissions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_milestone_submissions_user_id ON milestone_submissions(user_id);
CREATE INDEX IF NOT EXISTS idx_milestone_submissions_stage_id ON milestone_submissions(stage_id);
CREATE INDEX IF NOT EXISTS idx_milestone_submissions_milestone_id ON milestone_submissions(milestone_id);
CREATE INDEX IF NOT EXISTS idx_milestone_submissions_created_at ON milestone_submissions(created_at);
CREATE INDEX IF NOT EXISTS idx_milestone_submissions_user_stage ON milestone_submissions(user_id, stage_id);
CREATE INDEX IF NOT EXISTS idx_milestone_submissions_completed ON milestone_submissions(completed) WHERE completed = true;
CREATE INDEX IF NOT EXISTS idx_milestone_submissions_approved ON milestone_submissions(is_approved) WHERE is_approved = true;

-- Add constraints for data integrity
ALTER TABLE milestone_submissions 
ADD CONSTRAINT IF NOT EXISTS chk_stage_id_positive CHECK (stage_id >= 0);

-- Ensure RLS is enabled
ALTER TABLE milestone_submissions ENABLE ROW LEVEL SECURITY;

-- Update RLS policies to be more comprehensive
DROP POLICY IF EXISTS "Users can view their own milestone submissions" ON milestone_submissions;
DROP POLICY IF EXISTS "Users can insert their own milestone submissions" ON milestone_submissions;
DROP POLICY IF EXISTS "Users can update their own milestone submissions" ON milestone_submissions;

-- Create comprehensive RLS policies
CREATE POLICY "Users can view their own milestone submissions" ON milestone_submissions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own milestone submissions" ON milestone_submissions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own milestone submissions" ON milestone_submissions
    FOR UPDATE USING (auth.uid() = user_id);

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON milestone_submissions TO authenticated;
GRANT USAGE ON SEQUENCE milestone_submissions_id_seq TO authenticated;

-- Create stage_progress table if it doesn't exist for accurate tracking
CREATE TABLE IF NOT EXISTS stage_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    stage_id INTEGER NOT NULL,
    milestones_completed TEXT[] DEFAULT '{}',
    total_milestones INTEGER DEFAULT 0,
    progress_percentage INTEGER DEFAULT 0,
    confidence_score INTEGER DEFAULT 0,
    is_stage_complete BOOLEAN DEFAULT FALSE,
    stage_completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, stage_id)
);

-- Add indexes for stage_progress
CREATE INDEX IF NOT EXISTS idx_stage_progress_user_id ON stage_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_stage_progress_stage_id ON stage_progress(stage_id);
CREATE INDEX IF NOT EXISTS idx_stage_progress_user_stage ON stage_progress(user_id, stage_id);
CREATE INDEX IF NOT EXISTS idx_stage_progress_complete ON stage_progress(is_stage_complete) WHERE is_stage_complete = true;

-- Enable RLS for stage_progress
ALTER TABLE stage_progress ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for stage_progress
CREATE POLICY "Users can view their own stage progress" ON stage_progress
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own stage progress" ON stage_progress
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own stage progress" ON stage_progress
    FOR UPDATE USING (auth.uid() = user_id);

-- Grant permissions for stage_progress
GRANT SELECT, INSERT, UPDATE ON stage_progress TO authenticated;
GRANT USAGE ON SEQUENCE stage_progress_id_seq TO authenticated;

-- Create trigger for stage_progress updated_at
DROP TRIGGER IF EXISTS update_stage_progress_updated_at ON stage_progress;
CREATE TRIGGER update_stage_progress_updated_at
    BEFORE UPDATE ON stage_progress
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to calculate accurate stage progress
CREATE OR REPLACE FUNCTION calculate_stage_progress(p_user_id UUID, p_stage_id INTEGER)
RETURNS TABLE (
    stage_id INTEGER,
    completed_milestones INTEGER,
    total_milestones INTEGER,
    progress_percentage INTEGER,
    milestone_ids TEXT[],
    is_complete BOOLEAN
) AS $$
DECLARE
    v_completed_count INTEGER;
    v_total_count INTEGER;
    v_milestone_ids TEXT[];
    v_progress_pct INTEGER;
    v_is_complete BOOLEAN;
BEGIN
    -- Get completed milestones for this stage
    SELECT 
        COUNT(*) FILTER (WHERE (ms.is_approved = true OR ms.completed = true)),
        COUNT(*),
        ARRAY_AGG(ms.milestone_id) FILTER (WHERE (ms.is_approved = true OR ms.completed = true))
    INTO v_completed_count, v_total_count, v_milestone_ids
    FROM milestone_submissions ms
    WHERE ms.user_id = p_user_id AND ms.stage_id = p_stage_id;
    
    -- If no submissions exist, return zeros
    IF v_total_count = 0 THEN
        v_completed_count := 0;
        v_total_count := 0;
        v_milestone_ids := '{}';
        v_progress_pct := 0;
        v_is_complete := false;
    ELSE
        v_progress_pct := ROUND((v_completed_count::DECIMAL / v_total_count::DECIMAL) * 100);
        v_is_complete := (v_completed_count = v_total_count);
    END IF;
    
    RETURN QUERY SELECT 
        p_stage_id,
        v_completed_count,
        v_total_count,
        v_progress_pct,
        COALESCE(v_milestone_ids, '{}'),
        v_is_complete;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION calculate_stage_progress(UUID, INTEGER) TO authenticated;
