import React, { useState, useEffect } from 'react';
import { useIsMobile } from '@/hooks/use-mobile';

const MobilePerformanceMonitor: React.FC = () => {
  const isMobile = useIsMobile();
  const [isVisible, setIsVisible] = useState(false);
  const [fps, setFps] = useState(0);
  const [memoryUsage, setMemoryUsage] = useState(0);
  const [activeAnimations, setActiveAnimations] = useState(0);
  const [scrollPerformance, setScrollPerformance] = useState<'smooth' | 'janky' | 'unknown'>('unknown');

  // Only show in development mode
  const isDevelopment = import.meta.env.DEV;

  useEffect(() => {
    // Only activate on mobile devices in development mode
    if (!isMobile || !isDevelopment) {
      return;
    }

    setIsVisible(true);

    let lastTime = performance.now();
    let frameCount = 0;
    let animationFrame: number;

    // FPS monitoring
    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTime + 1000) {
        setFps(Math.round((frameCount * 1000) / (currentTime - lastTime)));
        frameCount = 0;
        lastTime = currentTime;
      }
      
      animationFrame = requestAnimationFrame(measureFPS);
    };

    // Memory monitoring (if available)
    const measureMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMemoryUsage(Math.round(memory.usedJSHeapSize / 1024 / 1024));
      }
    };

    // Animation monitoring
    const countAnimations = () => {
      const animatedElements = document.querySelectorAll('[data-framer-motion]');
      setActiveAnimations(animatedElements.length);
    };

    // Scroll performance monitoring
    let scrollFrameCount = 0;
    let lastScrollTime = performance.now();
    let scrollTimeout: NodeJS.Timeout;

    const handleScroll = () => {
      scrollFrameCount++;
      
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        const currentTime = performance.now();
        const scrollFPS = Math.round((scrollFrameCount * 1000) / (currentTime - lastScrollTime));
        
        setScrollPerformance(scrollFPS > 45 ? 'smooth' : 'janky');
        
        scrollFrameCount = 0;
        lastScrollTime = currentTime;
      }, 500);
    };

    // Start monitoring
    animationFrame = requestAnimationFrame(measureFPS);
    
    const memoryInterval = setInterval(measureMemory, 2000);
    const animationInterval = setInterval(countAnimations, 1000);
    
    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      cancelAnimationFrame(animationFrame);
      clearInterval(memoryInterval);
      clearInterval(animationInterval);
      clearTimeout(scrollTimeout);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isMobile, isDevelopment]);

  // Don't render if not visible
  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 right-4 z-[9999] bg-black/90 text-white text-xs p-3 rounded-lg border border-white/20 backdrop-blur-sm font-mono">
      <div className="mb-1 font-bold text-yellow-400">📱 Mobile Performance</div>
      <div className="space-y-1">
        <div className={`flex justify-between ${fps < 30 ? 'text-red-400' : fps < 50 ? 'text-yellow-400' : 'text-green-400'}`}>
          <span>FPS:</span>
          <span>{fps}</span>
        </div>
        {memoryUsage > 0 && (
          <div className={`flex justify-between ${memoryUsage > 50 ? 'text-red-400' : 'text-white'}`}>
            <span>Memory:</span>
            <span>{memoryUsage}MB</span>
          </div>
        )}
        <div className="flex justify-between">
          <span>Animations:</span>
          <span className={activeAnimations > 10 ? 'text-yellow-400' : 'text-white'}>{activeAnimations}</span>
        </div>
        <div className="flex justify-between">
          <span>Scroll:</span>
          <span className={
            scrollPerformance === 'smooth' ? 'text-green-400' : 
            scrollPerformance === 'janky' ? 'text-red-400' : 'text-gray-400'
          }>
            {scrollPerformance}
          </span>
        </div>
      </div>
    </div>
  );
};

export default MobilePerformanceMonitor; 