import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Disc3, Users } from 'lucide-react';

const CommunitySection = () => {
  return (
    <section id="community" className="animated-bg section-padding particles">
      <div className="max-w-5xl mx-auto text-center">
        <h2 className="section-title text-accent-white">
          Join the <span className="text-sauce-red">Community</span>
        </h2>
        <p className="section-subtitle text-crust-beige/80">
          Connect with fellow builders, share progress, and get support from the Pie Fi community. Our Discord is where the real conversations happen.
        </p>
        
        <div className="mt-12 grid md:grid-cols-2 gap-8 items-center">
          <div className="card-consistent text-left p-6">
            <Users className="h-12 w-12 text-sauce-red mb-4" />
            <h3 className="text-2xl font-bold text-oven-black mb-3">Join the Discord</h3>
            <p className="text-oven-black/70 mb-4">
              Our Discord is mission control: weekly check-ins, progress updates, peer support, and direct access to mentors. This is where the community comes together.
            </p>
            <Button className="bg-sauce-red hover:bg-sauce-red/90 text-accent-white font-semibold w-full sm:w-auto transition-all duration-300 transform hover:scale-105">
              <Disc3 className="mr-2 h-5 w-5" /> Join Discord
            </Button>
          </div>
          
          <div className="card-consistent-dark text-left p-6">
            <Users className="h-12 w-12 text-cheese-gold mb-4" />
            <h3 className="text-2xl font-bold text-accent-white mb-3">Public Build Progress</h3>
            <p className="text-crust-beige/70 mb-4">
              Share your progress, celebrate wins, and learn from other teams. Our public progress board will showcase the incredible work happening in the program.
            </p>
            <p className="text-xs text-cheese-gold/70">(Progress tracking dashboard - Coming Soon!)</p>
          </div>
        </div>

        <div className="mt-16">
          <h3 className="text-3xl font-bold text-accent-white mb-2">Alumni Showcase</h3>
          <p className="text-crust-beige/70 mb-6">The first cohort will set the foundation. Your team could be featured here.</p>
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-16 w-32 bg-accent-white/20 rounded flex items-center justify-center text-sm text-crust-beige/70 border border-cheese-gold/20">
                Team {i+1}
              </div>
            ))}
          </div>
        </div>
        <p className="mt-12 text-2xl font-bold text-sauce-red">
          Ready to build something amazing?
        </p>
      </div>
    </section>
  );
};

export default CommunitySection;
