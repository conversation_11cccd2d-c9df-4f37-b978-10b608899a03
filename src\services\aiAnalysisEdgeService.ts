import { supabase } from '@/integrations/supabase/client';

interface AnalysisRequest {
  type: 'milestone' | 'daily_update' | 'stage_progress';
  content: string;
  context: {
    userId: string;
    stageId?: number;
    milestoneId?: string;
    userContext?: any;
  };
}

interface AnalysisResponse {
  result: any;
  cached: boolean;
}

class AIAnalysisEdgeService {
  private baseUrl: string;

  constructor() {
    // Use Supabase project URL for edge functions
    this.baseUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1`;
  }

  /**
   * Analyze milestone submission using Supabase Edge Function
   */
  async analyzeMilestoneSubmission(
    content: string,
    context: {
      milestoneTitle: string;
      milestoneDescription: string;
      stageId: number;
      stageTitle: string;
      userContext: any;
      projectContext: string;
      milestoneDefinition?: any;
    }
  ): Promise<{
    consolidatedFeedback: string;
    followUpQuestions: string[];
    confidence: number;
    keyLearnings: string[];
    nextSteps: string[];
    profileUpdates: any;
    // Legacy fields for backward compatibility
    insights?: string;
    coachingInsight?: string;
  } | null> {
    
    try {
      const request: AnalysisRequest = {
        type: 'milestone',
        content,
        context: {
          userId: context.userContext?.user_id || '',
          stageId: context.stageId,
          milestoneId: context.milestoneTitle,
          milestoneTitle: context.milestoneTitle,
          milestoneDescription: context.milestoneDescription,
          stageTitle: context.stageTitle,
          userContext: context.userContext,
          milestoneDefinition: context.milestoneDefinition
        }
      };

      const response = await this.callEdgeFunction('ai-analysis', request);
      return response?.result || null;
    } catch (error) {
      console.error('Error in milestone analysis:', error);
      return this.getMockMilestoneAnalysis();
    }
  }

  /**
   * Analyze daily update using Supabase Edge Function
   */
  async analyzeDailyUpdate(
    content: string,
    context: any
  ): Promise<any> {
    
    try {
      const request: AnalysisRequest = {
        type: 'daily_update',
        content,
        context: {
          userId: context.userContext?.user_id || context.userContext?.userId || '',
          stageId: context.devJourneyStage,
          userContext: context
        }
      };

      const response = await this.callEdgeFunction('ai-analysis', request);
      return response?.result || this.getMockDailyAnalysis();
    } catch (error) {
      console.error('Error in daily update analysis:', error);
      return this.getMockDailyAnalysis();
    }
  }

  /**
   * Analyze stage progress using Supabase Edge Function
   */
  async analyzeUpdateForStageProgress(
    userId: string,
    updateContent: string,
    currentStage: number,
    userContext: any
  ): Promise<{
    detectedMilestones: string[];
    stageProgression: 'backward' | 'current' | 'forward';
    suggestedStage?: number;
    coachingInsight: string;
    followUpQuestions?: string[];
    confidence: number;
  } | null> {
    
    try {
      const request: AnalysisRequest = {
        type: 'stage_progress',
        content: updateContent,
        context: {
          userId,
          stageId: currentStage,
          userContext
        }
      };

      const response = await this.callEdgeFunction('ai-analysis', request);
      return response?.result || null;
    } catch (error) {
      console.error('Error in stage progress analysis:', error);
      return this.getMockStageAnalysis();
    }
  }

  /**
   * Generic method to call Supabase Edge Functions
   */
  private async callEdgeFunction(functionName: string, payload: any): Promise<AnalysisResponse> {
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      throw new Error('No authenticated session');
    }

    const response = await fetch(`${this.baseUrl}/${functionName}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Edge function error: ${response.status} - ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Check if edge functions are available, fallback to client-side if not
   */
  async isEdgeFunctionAvailable(): Promise<boolean> {
    try {
      console.log('🔍 Checking edge function availability at:', `${this.baseUrl}/ai-analysis`);

      const response = await fetch(`${this.baseUrl}/ai-analysis`, {
        method: 'OPTIONS',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('Edge function response status:', response.status);

      if (response.ok) {
        console.log('✅ Edge functions are available');
        return true;
      } else {
        console.warn(`⚠️ Edge function returned status ${response.status}`);
        return false;
      }
    } catch (error) {
      console.warn('❌ Edge functions not available:', error);
      console.warn('💡 To deploy edge functions, run: npm run deploy:functions');
      return false;
    }
  }

  // Mock responses for fallback
  private getMockMilestoneAnalysis() {
    return {
      consolidatedFeedback: "Good progress on this milestone. Your submission shows thoughtful consideration of the requirements. Consider diving deeper into the user impact - how does this move you closer to solving a real problem?",
      followUpQuestions: ["What specific user problem does this milestone help solve?", "How will you measure the success of this approach?"],
      confidence: 75,
      keyLearnings: ["Demonstrated understanding of milestone requirements", "Shows progress in current stage"],
      nextSteps: ["Continue building on this foundation", "Focus on user validation"],
      profileUpdates: {
        newSkills: ["milestone_completion"],
        updatedChallenges: [],
        strengthsRevealed: ["systematic_progress"]
      },
      // Legacy fields for backward compatibility
      insights: "Good progress on this milestone. Your submission shows thoughtful consideration of the requirements.",
      coachingInsight: "Great work! Consider diving deeper into the user impact of this milestone. How does this move you closer to solving a real problem?"
    };
  }

  private getMockDailyAnalysis() {
    return {
      sentiment: 0.7,
      progress: 0.8,
      achievements: ["Made good progress"],
      blockers: [],
      nextActions: ["Continue current approach"],
      goalUpdates: [
        { status: "progress", title: "Continue building", priority: "high" }
      ],
      insights: "Keep up the great work!",
      coachingInsight: "Great progress today! 🚀"
    };
  }

  private getMockStageAnalysis() {
    return {
      detectedMilestones: ["general_progress"],
      stageProgression: 'current' as const,
      coachingInsight: "Keep pushing forward with intentional progress.",
      confidence: 70
    };
  }

  /**
   * Migrate existing analysis to use edge functions
   */
  async migrateToEdgeFunctions(): Promise<boolean> {
    const isAvailable = await this.isEdgeFunctionAvailable();
    
    if (isAvailable) {
      console.log('✅ Edge functions available - migrating AI analysis');
      return true;
    } else {
      console.log('⚠️ Edge functions not available - continuing with client-side analysis');
      return false;
    }
  }

  /**
   * Batch analyze multiple items for efficiency
   */
  async batchAnalyze(requests: AnalysisRequest[]): Promise<AnalysisResponse[]> {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('No authenticated session');
      }

      const response = await fetch(`${this.baseUrl}/ai-analysis-batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({ requests }),
      });

      if (!response.ok) {
        throw new Error(`Batch analysis error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error in batch analysis:', error);
      // Fallback to individual analysis
      return Promise.all(
        requests.map(req => this.callEdgeFunction('ai-analysis', req))
      );
    }
  }
}

const aiAnalysisEdgeService = new AIAnalysisEdgeService();
export default aiAnalysisEdgeService;
