import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  Users, 
  Calendar, 
  School, 
  BarChart3, 
  PieChart, 
  Download,
  Clock,
  Target,
  Award,
  Filter,
  RefreshCw,
  Eye,
  UserCheck,
  UserX,
  Heart,
  Code,
  Lightbulb,
  MapPin
} from 'lucide-react';
import { supabaseAdmin } from '@/integrations/supabase/admin-client';
import { normalizeUniversityName } from '@/utils/universityNormalization';

interface Application {
  id: string;
  full_name: string;
  email: string;
  university: string;
  year: string;
  major: string | null;
  skills: string;
  team_status: string;
  status: string | null;
  access_code_used: string | null;
  application_type: string | null;
  created_at: string;
}

interface Supporter {
  id: string;
  name: string;
  email: string;
  relationship: string;
  role: string;
  interests: string;
  status: string;
  created_at: string;
}

interface StatsData {
  totalApplications: number;
  totalSupporters: number;
  todayApplications: number;
  weekApplications: number;
  monthApplications: number;
  conversionRate: number;
  avgResponseTime: number;
  topUniversities: { name: string; count: number }[];
  topSkills: { skill: string; count: number }[];
  accessCodeBreakdown: { code: string; count: number }[];
  applicationsByDay: { date: string; count: number }[];
  statusBreakdown: { status: string; count: number }[];
  supportersByRole: { role: string; count: number }[];
  typeBreakdown: { type: string; count: number }[];
}

const AdminStats: React.FC = () => {
  const [applications, setApplications] = useState<Application[]>([]);
  const [supporters, setSupporters] = useState<Supporter[]>([]);
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState<'week' | 'month' | 'quarter' | 'year'>('month');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadData();
  }, [timeframe]);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Fetch applications
      const { data: applicationsData, error: appsError } = await supabaseAdmin
        .from('applications')
        .select('*')
        .order('created_at', { ascending: false });

      if (appsError) throw appsError;

      // For supporters, we'll use mock data since table might not exist yet
      const mockSupporters: Supporter[] = [
        {
          id: '1',
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          relationship: 'Parent',
          role: 'Marketing Professional',
          interests: 'Technology, Education',
          status: 'contacted',
          created_at: new Date(Date.now() - 86400000).toISOString()
        },
        {
          id: '2',
          name: 'Mike Chen',
          email: '<EMAIL>',
          relationship: 'Family Friend',
          role: 'Software Engineer',
          interests: 'Coding, Mentorship',
          status: 'engaged',
          created_at: new Date(Date.now() - 172800000).toISOString()
        }
      ];

      setApplications(applicationsData || []);
      setSupporters(mockSupporters);
      
      generateStats(applicationsData || [], mockSupporters);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateStats = (apps: Application[], supporters: Supporter[]) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Filter based on timeframe
    let filteredApps = apps;
    switch (timeframe) {
      case 'week':
        filteredApps = apps.filter(app => new Date(app.created_at) >= weekAgo);
        break;
      case 'month':
        filteredApps = apps.filter(app => new Date(app.created_at) >= monthAgo);
        break;
      case 'quarter':
        const quarterAgo = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);
        filteredApps = apps.filter(app => new Date(app.created_at) >= quarterAgo);
        break;
      case 'year':
        const yearAgo = new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000);
        filteredApps = apps.filter(app => new Date(app.created_at) >= yearAgo);
        break;
    }

    // Basic counts
    const todayApps = apps.filter(app => new Date(app.created_at) >= today).length;
    const weekApps = apps.filter(app => new Date(app.created_at) >= weekAgo).length;
    const monthApps = apps.filter(app => new Date(app.created_at) >= monthAgo).length;

    // University analysis (using normalized names)
    const universityCount: { [key: string]: number } = {};
    filteredApps.forEach(app => {
      const normalizedName = normalizeUniversityName(app.university);
      universityCount[normalizedName] = (universityCount[normalizedName] || 0) + 1;
    });
    const topUniversities = Object.entries(universityCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([name, count]) => ({ name, count }));

    // Skills analysis
    const skillCount: { [key: string]: number } = {};
    filteredApps.forEach(app => {
      app.skills.split(',').forEach(skill => {
        const trimmedSkill = skill.trim().toLowerCase();
        skillCount[trimmedSkill] = (skillCount[trimmedSkill] || 0) + 1;
      });
    });
    const topSkills = Object.entries(skillCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([skill, count]) => ({ skill, count }));

    // Access code breakdown
    const accessCodeCount: { [key: string]: number } = {};
    apps.forEach(app => {
      if (app.access_code_used) {
        accessCodeCount[app.access_code_used] = (accessCodeCount[app.access_code_used] || 0) + 1;
      }
    });
    const accessCodeBreakdown = Object.entries(accessCodeCount)
      .sort(([,a], [,b]) => b - a)
      .map(([code, count]) => ({ code, count }));

    // Applications by day (last 30 days)
    const applicationsByDay: { date: string; count: number }[] = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      const count = apps.filter(app => 
        app.created_at.split('T')[0] === dateStr
      ).length;
      applicationsByDay.push({ date: dateStr, count });
    }

    // Status breakdown
    const statusCount: { [key: string]: number } = {};
    apps.forEach(app => {
      const status = app.status || 'pending';
      statusCount[status] = (statusCount[status] || 0) + 1;
    });
    const statusBreakdown = Object.entries(statusCount)
      .map(([status, count]) => ({ status, count }));

    // Supporter analysis
    const roleCount: { [key: string]: number } = {};
    supporters.forEach(supporter => {
      roleCount[supporter.role] = (roleCount[supporter.role] || 0) + 1;
    });
    const supportersByRole = Object.entries(roleCount)
      .sort(([,a], [,b]) => b - a)
      .map(([role, count]) => ({ role, count }));

    // Application type breakdown
    const typeCount: { [key: string]: number } = {};
    apps.forEach(app => {
      const type = app.application_type || 'builder';
      typeCount[type] = (typeCount[type] || 0) + 1;
    });
    const typeBreakdown = Object.entries(typeCount)
      .map(([type, count]) => ({ type, count }));

    const statsData: StatsData = {
      totalApplications: apps.length,
      totalSupporters: supporters.length,
      todayApplications: todayApps,
      weekApplications: weekApps,
      monthApplications: monthApps,
      conversionRate: apps.length > 0 ? (apps.filter(app => app.status === 'accepted').length / apps.length) * 100 : 0,
      avgResponseTime: 2.5, // Mock data - would calculate from actual response times
      topUniversities,
      topSkills,
      accessCodeBreakdown,
      applicationsByDay,
      statusBreakdown,
      supportersByRole,
      typeBreakdown
    };

    setStats(statsData);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const exportStats = () => {
    if (!stats) return;
    
    const csvData = [
      ['Metric', 'Value'],
      ['Total Applications', stats.totalApplications.toString()],
      ['Total Supporters', stats.totalSupporters.toString()],
      ['Today Applications', stats.todayApplications.toString()],
      ['Week Applications', stats.weekApplications.toString()],
      ['Month Applications', stats.monthApplications.toString()],
      ['Conversion Rate', `${stats.conversionRate.toFixed(2)}%`],
      ['Avg Response Time', `${stats.avgResponseTime} days`],
      ...stats.topUniversities.map(uni => [`University: ${uni.name}`, uni.count.toString()]),
      ...stats.topSkills.map(skill => [`Skill: ${skill.skill}`, skill.count.toString()])
    ];

    const csv = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `pie-fi-stats-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-16">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-sauce-red"></div>
      </div>
    );
  }

  if (!stats) return null;

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-accent-white mb-2">Analytics Dashboard</h2>
          <p className="text-crust-beige/80">Comprehensive insights into your Pie Fi applications</p>
        </div>
        <div className="flex items-center gap-3">
          <Select value={timeframe} onValueChange={(value: any) => setTimeframe(value)}>
            <SelectTrigger className="w-32 bg-accent-white/10 border-accent-white/30 text-accent-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-oven-black border-accent-white/30">
              <SelectItem value="week">Week</SelectItem>
              <SelectItem value="month">Month</SelectItem>
              <SelectItem value="quarter">Quarter</SelectItem>
              <SelectItem value="year">Year</SelectItem>
            </SelectContent>
          </Select>
          <Button 
            onClick={handleRefresh} 
            disabled={refreshing}
            variant="outline" 
            className="border-accent-white/30 text-accent-white hover:bg-accent-white/10"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button 
            onClick={exportStats}
            className="bg-gradient-to-r from-sauce-red to-orange-600"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.1 }}>
          <Card className="bg-gradient-to-br from-blue-500/10 to-blue-600/10 border-blue-500/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-400 text-sm font-medium">Total Applications</p>
                  <p className="text-3xl font-bold text-accent-white">{stats.totalApplications}</p>
                  <p className="text-xs text-blue-400/60 mt-1">+{stats.weekApplications} this week</p>
                </div>
                <Users className="h-12 w-12 text-blue-400" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.2 }}>
          <Card className="bg-gradient-to-br from-purple-500/10 to-purple-600/10 border-purple-500/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-400 text-sm font-medium">Supporters</p>
                  <p className="text-3xl font-bold text-accent-white">{stats.totalSupporters}</p>
                  <p className="text-xs text-purple-400/60 mt-1">Friends & Family</p>
                </div>
                <Heart className="h-12 w-12 text-purple-400" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.3 }}>
          <Card className="bg-gradient-to-br from-green-500/10 to-green-600/10 border-green-500/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-400 text-sm font-medium">Conversion Rate</p>
                  <p className="text-3xl font-bold text-accent-white">{stats.conversionRate.toFixed(1)}%</p>
                  <p className="text-xs text-green-400/60 mt-1">Applications to Acceptance</p>
                </div>
                <Target className="h-12 w-12 text-green-400" />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ delay: 0.4 }}>
          <Card className="bg-gradient-to-br from-orange-500/10 to-orange-600/10 border-orange-500/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-400 text-sm font-medium">Avg Response Time</p>
                  <p className="text-3xl font-bold text-accent-white">{stats.avgResponseTime}</p>
                  <p className="text-xs text-orange-400/60 mt-1">Days to review</p>
                </div>
                <Clock className="h-12 w-12 text-orange-400" />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Charts and Detailed Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Application Status Breakdown */}
        <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-accent-white flex items-center gap-2">
              <PieChart className="h-6 w-6 text-sauce-red" />
              Application Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {stats.statusBreakdown.map((item, index) => {
              const percentage = (item.count / stats.totalApplications) * 100;
              const colors = {
                pending: 'bg-yellow-500',
                shortlisted: 'bg-blue-500',
                accepted: 'bg-green-500',
                rejected: 'bg-red-500'
              };
              
              return (
                <div key={item.status} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-accent-white capitalize">{item.status}</span>
                    <span className="text-crust-beige">{item.count} ({percentage.toFixed(1)}%)</span>
                  </div>
                  <Progress 
                    value={percentage} 
                    className="h-3"
                    // @ts-ignore
                    indicatorClassName={colors[item.status as keyof typeof colors] || 'bg-gray-500'}
                  />
                </div>
              );
            })}
          </CardContent>
        </Card>

        {/* Top Universities */}
        <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-accent-white flex items-center gap-2">
              <School className="h-6 w-6 text-sauce-red" />
              Top Universities
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {stats.topUniversities.slice(0, 5).map((uni, index) => (
              <div key={uni.name} className="flex items-center justify-between p-3 bg-accent-white/5 rounded-lg">
                <div className="flex items-center gap-3">
                  <Badge className="bg-sauce-red/20 text-sauce-red">{index + 1}</Badge>
                  <span className="text-accent-white font-medium">{uni.name}</span>
                </div>
                <span className="text-crust-beige">{uni.count}</span>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Access Code Performance */}
        <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-accent-white flex items-center gap-2">
              <BarChart3 className="h-6 w-6 text-sauce-red" />
              Access Code Usage
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {stats.accessCodeBreakdown.slice(0, 5).map((code, index) => {
              const percentage = (code.count / stats.totalApplications) * 100;
              
              return (
                <div key={code.code} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-accent-white font-mono">{code.code}</span>
                    <span className="text-crust-beige">{code.count} ({percentage.toFixed(1)}%)</span>
                  </div>
                  <Progress value={percentage} className="h-2" />
                </div>
              );
            })}
          </CardContent>
        </Card>

        {/* Application Type Breakdown */}
        <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-accent-white flex items-center gap-2">
              <Users className="h-6 w-6 text-sauce-red" />
              Application Types
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {stats.typeBreakdown.map((type, index) => {
              const percentage = (type.count / stats.totalApplications) * 100;
              const colors = {
                builder: 'bg-blue-500',
                supporter: 'bg-purple-500'
              };
              
              return (
                <div key={type.type} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      {type.type === 'builder' ? <Code className="h-4 w-4" /> : <Heart className="h-4 w-4" />}
                      <span className="text-accent-white capitalize">{type.type}s</span>
                    </div>
                    <span className="text-crust-beige">{type.count} ({percentage.toFixed(1)}%)</span>
                  </div>
                  <Progress 
                    value={percentage} 
                    className="h-3"
                    // @ts-ignore
                    indicatorClassName={colors[type.type as keyof typeof colors] || 'bg-gray-500'}
                  />
                </div>
              );
            })}
          </CardContent>
        </Card>
      </div>

      {/* Top Skills */}
      <Card className="bg-accent-white/10 backdrop-blur-md border-accent-white/20">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-accent-white flex items-center gap-2">
            <Lightbulb className="h-6 w-6 text-sauce-red" />
            Most Popular Skills
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
            {stats.topSkills.slice(0, 10).map((skill, index) => (
              <div key={skill.skill} className="p-3 bg-accent-white/5 rounded-lg text-center">
                <p className="text-accent-white font-medium capitalize">{skill.skill}</p>
                <p className="text-crust-beige/60 text-sm">{skill.count} mentions</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminStats; 