-- User Progress Analytics System
-- Transform basic activity tracking into meaningful user journey insights

-- 1. Create comprehensive user progress analytics view
CREATE OR REPLACE VIEW user_progress_analytics AS
WITH user_onboarding_metrics AS (
  SELECT 
    u.id as user_id,
    u.full_name,
    u.email,
    u.track,
    u.onboarding_completed,
    u.created_at as user_created_at,
    or_data.created_at as onboarding_started_at,
    or_data.updated_at as onboarding_completed_at,
    CASE 
      WHEN u.onboarding_completed THEN 
        EXTRACT(EPOCH FROM (or_data.updated_at - or_data.created_at)) / 3600.0
      ELSE 
        EXTRACT(EPOCH FROM (NOW() - or_data.created_at)) / 3600.0
    END as onboarding_duration_hours,
    or_data.problem_description IS NOT NULL as has_problem_description,
    or_data.solution_approach IS NOT NULL as has_solution_approach,
    or_data.target_audience IS NOT NULL as has_target_audience,
    or_data.technical_background,
    or_data.primary_goal,
    or_data.biggest_challenge
  FROM users u
  LEFT JOIN onboarding_responses or_data ON u.id = or_data.user_id
),

user_stage_metrics AS (
  SELECT 
    user_id,
    stage_id as current_stage,
    confidence_score as current_confidence,
    stage_entered_at as current_stage_entered_at,
    EXTRACT(EPOCH FROM (NOW() - stage_entered_at)) / (24.0 * 3600.0) as days_in_current_stage,
    ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn
  FROM user_dev_journey_stages
),

user_milestone_metrics AS (
  SELECT 
    user_id,
    COUNT(*) as total_milestones_submitted,
    COUNT(CASE WHEN is_approved = true THEN 1 END) as approved_milestones,
    COUNT(DISTINCT stage_id) as stages_with_submissions,
    MAX(submission_date) as last_milestone_date,
    AVG(LENGTH(submission_content)) as avg_submission_length,
    COUNT(CASE WHEN ai_feedback IS NOT NULL THEN 1 END) as milestones_with_ai_feedback
  FROM milestone_submissions
  GROUP BY user_id
),

user_daily_update_metrics AS (
  SELECT 
    user_id,
    COUNT(*) as total_daily_updates,
    MAX(created_at) as last_update_date,
    AVG(sentiment_score) as avg_sentiment,
    AVG(LENGTH(content)) as avg_update_length,
    COUNT(CASE WHEN ai_insights IS NOT NULL THEN 1 END) as updates_with_ai_insights,
    -- Calculate consistency (updates in last 7 days)
    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) as updates_last_7_days,
    -- Calculate engagement trend
    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '30 days' THEN 1 END) as updates_last_30_days
  FROM daily_updates
  GROUP BY user_id
),

user_ai_interaction_metrics AS (
  SELECT 
    user_id,
    COUNT(*) as total_ai_interactions,
    COUNT(DISTINCT memory_type) as interaction_types,
    MAX(created_at) as last_ai_interaction,
    COUNT(CASE WHEN memory_type = 'insight' THEN 1 END) as ai_insights_received,
    COUNT(CASE WHEN memory_type = 'progress' THEN 1 END) as progress_tracked
  FROM user_memory_entries
  GROUP BY user_id
)

SELECT 
  uom.*,
  COALESCE(usm.current_stage, 0) as current_stage,
  COALESCE(usm.current_confidence, 50) as current_confidence,
  usm.current_stage_entered_at,
  COALESCE(usm.days_in_current_stage, 0) as days_in_current_stage,
  
  -- Milestone metrics
  COALESCE(umm.total_milestones_submitted, 0) as total_milestones_submitted,
  COALESCE(umm.approved_milestones, 0) as approved_milestones,
  COALESCE(umm.stages_with_submissions, 0) as stages_with_submissions,
  umm.last_milestone_date,
  COALESCE(umm.avg_submission_length, 0) as avg_submission_length,
  COALESCE(umm.milestones_with_ai_feedback, 0) as milestones_with_ai_feedback,
  
  -- Daily update metrics
  COALESCE(udum.total_daily_updates, 0) as total_daily_updates,
  udum.last_update_date,
  COALESCE(udum.avg_sentiment, 0.5) as avg_sentiment,
  COALESCE(udum.avg_update_length, 0) as avg_update_length,
  COALESCE(udum.updates_with_ai_insights, 0) as updates_with_ai_insights,
  COALESCE(udum.updates_last_7_days, 0) as updates_last_7_days,
  COALESCE(udum.updates_last_30_days, 0) as updates_last_30_days,
  
  -- AI interaction metrics
  COALESCE(uaim.total_ai_interactions, 0) as total_ai_interactions,
  COALESCE(uaim.interaction_types, 0) as interaction_types,
  uaim.last_ai_interaction,
  COALESCE(uaim.ai_insights_received, 0) as ai_insights_received,
  COALESCE(uaim.progress_tracked, 0) as progress_tracked,
  
  -- Calculated progress indicators
  CASE 
    WHEN uom.onboarding_completed AND COALESCE(usm.current_stage, 0) > 0 THEN 'Active'
    WHEN uom.onboarding_completed AND COALESCE(usm.current_stage, 0) = 0 THEN 'Onboarded - No Progress'
    WHEN NOT uom.onboarding_completed AND uom.onboarding_started_at IS NOT NULL THEN 'Onboarding In Progress'
    WHEN uom.onboarding_started_at IS NULL THEN 'Not Started'
    ELSE 'Unknown'
  END as progress_status,
  
  -- Engagement score (0-100)
  LEAST(100, GREATEST(0, 
    (COALESCE(udum.updates_last_7_days, 0) * 15) +
    (COALESCE(umm.total_milestones_submitted, 0) * 10) +
    (COALESCE(usm.current_stage, 0) * 15) +
    (CASE WHEN udum.last_update_date >= NOW() - INTERVAL '3 days' THEN 20 ELSE 0 END) +
    (CASE WHEN umm.last_milestone_date >= NOW() - INTERVAL '7 days' THEN 15 ELSE 0 END) +
    (CASE WHEN uom.onboarding_completed THEN 25 ELSE 0 END)
  )) as engagement_score,
  
  -- Risk indicators
  CASE 
    WHEN NOT uom.onboarding_completed AND uom.onboarding_duration_hours > 72 THEN 'High'
    WHEN uom.onboarding_completed AND COALESCE(usm.days_in_current_stage, 0) > 14 THEN 'High'
    WHEN COALESCE(udum.updates_last_7_days, 0) = 0 AND udum.last_update_date < NOW() - INTERVAL '7 days' THEN 'Medium'
    WHEN COALESCE(usm.current_confidence, 50) < 30 THEN 'Medium'
    ELSE 'Low'
  END as risk_level,
  
  -- Success indicators
  CASE 
    WHEN COALESCE(usm.current_stage, 0) >= 4 AND COALESCE(umm.approved_milestones, 0) >= 3 THEN 'High Performer'
    WHEN COALESCE(usm.current_stage, 0) >= 2 AND COALESCE(udum.avg_sentiment, 0) > 0.7 THEN 'On Track'
    WHEN uom.onboarding_completed AND COALESCE(udum.updates_last_7_days, 0) >= 3 THEN 'Engaged'
    ELSE 'Needs Attention'
  END as success_category

FROM user_onboarding_metrics uom
LEFT JOIN user_stage_metrics usm ON uom.user_id = usm.user_id AND usm.rn = 1
LEFT JOIN user_milestone_metrics umm ON uom.user_id = umm.user_id
LEFT JOIN user_daily_update_metrics udum ON uom.user_id = udum.user_id
LEFT JOIN user_ai_interaction_metrics uaim ON uom.user_id = uaim.user_id;

-- 2. Create stage progression analytics view
CREATE OR REPLACE VIEW stage_progression_analytics AS
WITH stage_transitions AS (
  SELECT 
    user_id,
    stage_id,
    stage_entered_at,
    confidence_score,
    LAG(stage_id) OVER (PARTITION BY user_id ORDER BY stage_entered_at) as previous_stage,
    LAG(stage_entered_at) OVER (PARTITION BY user_id ORDER BY stage_entered_at) as previous_stage_date,
    EXTRACT(EPOCH FROM (
      stage_entered_at - LAG(stage_entered_at) OVER (PARTITION BY user_id ORDER BY stage_entered_at)
    )) / (24.0 * 3600.0) as days_in_previous_stage
  FROM user_dev_journey_stages
)

SELECT 
  st.*,
  u.track,
  u.full_name,
  u.email,
  -- Calculate stage velocity
  CASE 
    WHEN st.days_in_previous_stage IS NOT NULL AND st.days_in_previous_stage > 0 
    THEN 1.0 / st.days_in_previous_stage 
    ELSE 0 
  END as stage_velocity,
  
  -- Identify stage progression patterns
  CASE 
    WHEN st.days_in_previous_stage <= 3 THEN 'Fast'
    WHEN st.days_in_previous_stage <= 7 THEN 'Normal'
    WHEN st.days_in_previous_stage <= 14 THEN 'Slow'
    ELSE 'Stuck'
  END as progression_pace

FROM stage_transitions st
JOIN users u ON st.user_id = u.id
WHERE st.previous_stage IS NOT NULL;

-- 3. Create cohort analysis view
CREATE OR REPLACE VIEW user_cohort_analytics AS
SELECT 
  track,
  DATE_TRUNC('week', user_created_at) as cohort_week,
  COUNT(*) as total_users,
  COUNT(CASE WHEN onboarding_completed THEN 1 END) as completed_onboarding,
  COUNT(CASE WHEN current_stage > 0 THEN 1 END) as started_journey,
  COUNT(CASE WHEN current_stage >= 2 THEN 1 END) as reached_stage_2,
  COUNT(CASE WHEN current_stage >= 4 THEN 1 END) as reached_stage_4,
  COUNT(CASE WHEN success_category = 'High Performer' THEN 1 END) as high_performers,
  COUNT(CASE WHEN risk_level = 'High' THEN 1 END) as high_risk_users,
  AVG(engagement_score) as avg_engagement_score,
  AVG(current_confidence) as avg_confidence,
  AVG(total_daily_updates) as avg_daily_updates,
  AVG(total_milestones_submitted) as avg_milestones_submitted
FROM user_progress_analytics
GROUP BY track, DATE_TRUNC('week', user_created_at)
ORDER BY cohort_week DESC, track;

-- 4. Create functions for specific analytics queries

-- Function to get users needing intervention
CREATE OR REPLACE FUNCTION get_users_needing_intervention()
RETURNS TABLE (
  user_id UUID,
  full_name VARCHAR,
  email VARCHAR,
  track VARCHAR,
  risk_level TEXT,
  reason TEXT,
  days_since_last_activity INTEGER,
  recommended_action TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    upa.user_id,
    upa.full_name,
    upa.email,
    upa.track,
    upa.risk_level,
    CASE 
      WHEN NOT upa.onboarding_completed AND upa.onboarding_duration_hours > 72 
        THEN 'Stuck in onboarding for over 3 days'
      WHEN upa.onboarding_completed AND upa.days_in_current_stage > 14 
        THEN 'No stage progression for over 2 weeks'
      WHEN upa.updates_last_7_days = 0 AND upa.last_update_date < NOW() - INTERVAL '7 days' 
        THEN 'No daily updates for over a week'
      WHEN upa.current_confidence < 30 
        THEN 'Very low confidence score'
      ELSE 'Multiple risk factors'
    END as reason,
    COALESCE(
      EXTRACT(EPOCH FROM (NOW() - GREATEST(upa.last_update_date, upa.last_milestone_date, upa.last_ai_interaction))) / (24.0 * 3600.0),
      999
    )::INTEGER as days_since_last_activity,
    CASE 
      WHEN NOT upa.onboarding_completed 
        THEN 'Send onboarding completion reminder and offer 1:1 help'
      WHEN upa.updates_last_7_days = 0 
        THEN 'Reach out to re-engage with daily updates'
      WHEN upa.current_confidence < 30 
        THEN 'Schedule confidence-building session'
      WHEN upa.days_in_current_stage > 14 
        THEN 'Provide stage-specific guidance and resources'
      ELSE 'General check-in and support offer'
    END as recommended_action
  FROM user_progress_analytics upa
  WHERE upa.risk_level IN ('High', 'Medium')
  ORDER BY 
    CASE upa.risk_level WHEN 'High' THEN 1 WHEN 'Medium' THEN 2 ELSE 3 END,
    days_since_last_activity DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get success stories
CREATE OR REPLACE FUNCTION get_success_stories()
RETURNS TABLE (
  user_id UUID,
  full_name VARCHAR,
  email VARCHAR,
  track VARCHAR,
  success_category TEXT,
  current_stage INTEGER,
  total_milestones_submitted INTEGER,
  approved_milestones INTEGER,
  engagement_score INTEGER,
  days_to_current_stage NUMERIC,
  success_metrics TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    upa.user_id,
    upa.full_name,
    upa.email,
    upa.track,
    upa.success_category,
    upa.current_stage,
    upa.total_milestones_submitted,
    upa.approved_milestones,
    upa.engagement_score,
    EXTRACT(EPOCH FROM (NOW() - upa.user_created_at)) / (24.0 * 3600.0) as days_to_current_stage,
    CASE 
      WHEN upa.success_category = 'High Performer' 
        THEN 'Advanced quickly through stages with high-quality submissions'
      WHEN upa.success_category = 'On Track' 
        THEN 'Consistent progress with positive sentiment'
      WHEN upa.success_category = 'Engaged' 
        THEN 'Highly engaged with regular updates'
      ELSE 'Showing positive momentum'
    END as success_metrics
  FROM user_progress_analytics upa
  WHERE upa.success_category IN ('High Performer', 'On Track', 'Engaged')
    AND upa.engagement_score >= 60
  ORDER BY 
    CASE upa.success_category 
      WHEN 'High Performer' THEN 1 
      WHEN 'On Track' THEN 2 
      WHEN 'Engaged' THEN 3 
      ELSE 4 
    END,
    upa.engagement_score DESC;
END;
$$ LANGUAGE plpgsql;

-- 5. Create indexes for performance on underlying tables
-- Note: Cannot create indexes on views, but we can optimize the underlying tables

-- Indexes for users table
CREATE INDEX IF NOT EXISTS idx_users_track ON users USING btree(track);
CREATE INDEX IF NOT EXISTS idx_users_onboarding_completed ON users USING btree(onboarding_completed);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users USING btree(created_at);

-- Indexes for user_dev_journey_stages table
CREATE INDEX IF NOT EXISTS idx_user_dev_journey_stages_user_id ON user_dev_journey_stages USING btree(user_id);
CREATE INDEX IF NOT EXISTS idx_user_dev_journey_stages_stage_id ON user_dev_journey_stages USING btree(stage_id);
CREATE INDEX IF NOT EXISTS idx_user_dev_journey_stages_created_at ON user_dev_journey_stages USING btree(created_at);
CREATE INDEX IF NOT EXISTS idx_user_dev_journey_stages_stage_entered_at ON user_dev_journey_stages USING btree(stage_entered_at);

-- Indexes for milestone_submissions table
CREATE INDEX IF NOT EXISTS idx_milestone_submissions_user_id ON milestone_submissions USING btree(user_id);
CREATE INDEX IF NOT EXISTS idx_milestone_submissions_stage_id ON milestone_submissions USING btree(stage_id);
CREATE INDEX IF NOT EXISTS idx_milestone_submissions_submission_date ON milestone_submissions USING btree(submission_date);
CREATE INDEX IF NOT EXISTS idx_milestone_submissions_is_approved ON milestone_submissions USING btree(is_approved);

-- Indexes for daily_updates table
CREATE INDEX IF NOT EXISTS idx_daily_updates_user_id ON daily_updates USING btree(user_id);
CREATE INDEX IF NOT EXISTS idx_daily_updates_created_at ON daily_updates USING btree(created_at);

-- Indexes for user_memory_entries table
CREATE INDEX IF NOT EXISTS idx_user_memory_entries_user_id ON user_memory_entries USING btree(user_id);
CREATE INDEX IF NOT EXISTS idx_user_memory_entries_memory_type ON user_memory_entries USING btree(memory_type);
CREATE INDEX IF NOT EXISTS idx_user_memory_entries_created_at ON user_memory_entries USING btree(created_at);

-- Indexes for onboarding_responses table
CREATE INDEX IF NOT EXISTS idx_onboarding_responses_user_id ON onboarding_responses USING btree(user_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_responses_created_at ON onboarding_responses USING btree(created_at);
CREATE INDEX IF NOT EXISTS idx_onboarding_responses_updated_at ON onboarding_responses USING btree(updated_at);

-- 6. Create materialized view for performance (refresh periodically)
CREATE MATERIALIZED VIEW IF NOT EXISTS user_progress_summary AS
SELECT 
  track,
  COUNT(*) as total_users,
  COUNT(CASE WHEN onboarding_completed THEN 1 END) as onboarding_completed_count,
  ROUND(AVG(engagement_score), 1) as avg_engagement_score,
  COUNT(CASE WHEN risk_level = 'High' THEN 1 END) as high_risk_count,
  COUNT(CASE WHEN success_category = 'High Performer' THEN 1 END) as high_performer_count,
  COUNT(CASE WHEN current_stage >= 2 THEN 1 END) as progressed_users,
  ROUND(AVG(current_confidence), 1) as avg_confidence
FROM user_progress_analytics
GROUP BY track;

-- Function to refresh materialized view
CREATE OR REPLACE FUNCTION refresh_progress_summary()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW user_progress_summary;
END;
$$ LANGUAGE plpgsql;

-- Comments for documentation
COMMENT ON VIEW user_progress_analytics IS 'Comprehensive user progress metrics for admin dashboard insights';
COMMENT ON VIEW stage_progression_analytics IS 'User stage progression patterns and velocity analysis';
COMMENT ON VIEW user_cohort_analytics IS 'Cohort-based user performance and retention analysis';
COMMENT ON FUNCTION get_users_needing_intervention() IS 'Identifies users requiring admin intervention with specific recommendations';
COMMENT ON FUNCTION get_success_stories() IS 'Highlights successful users and their achievement patterns';
