import React from 'react';
import { motion, useInView } from 'framer-motion';
import { Calendar, Clock, MapPin, Ticket, Sparkles, Star, Users, Music, Trophy, Zap, Rocket, Award, Mic, PartyPopper, Lightbulb } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { useRef } from 'react';

const DemoDay = () => {
  const isMobile = useIsMobile();
  const heroRef = useRef(null);
  const detailsRef = useRef(null);
  const ctaRef = useRef(null);
  
  const heroInView = useInView(heroRef, { once: true, amount: 0.3 });
  const detailsInView = useInView(detailsRef, { once: true, amount: 0.3 });
  const ctaInView = useInView(ctaRef, { once: true, amount: 0.3 });
  
  const eventSchedule = [
    {
      time: "2:00 - 3:00 PM",
      title: "VIP Builder Sessions",
      description: "Exclusive hang time with builders - limited access",
      icon: Star,
      color: "from-purple-400 to-pink-500"
    },
    {
      time: "3:00 - 5:30 PM",
      title: "Product Demos & Mingling",
      description: "Experience the 10 products built during Pie Fi",
      icon: Users,
      color: "from-blue-400 to-purple-500"
    },
    {
      time: "5:30 - 7:00 PM",
      title: "Reception & Celebration",
      description: "Food, drinks, and celebrating our builders",
      icon: Sparkles,
      color: "from-yellow-400 to-orange-500"
    },
    {
      time: "7:00 PM+",
      title: "Concert at Abbott Square",
      description: "Live music to cap off an incredible day",
      icon: Music,
      color: "from-green-400 to-blue-500"
    }
  ];

  return (
    <section className="relative py-32 overflow-hidden bg-gradient-to-br from-oven-black via-sauce-red/10 to-oven-black">
      {/* Background elements - simplified for mobile */}
      <div className="absolute inset-0">
        {/* Main gradient orbs */}
        <motion.div 
          className="absolute top-1/3 right-1/3 w-[800px] h-[800px] rounded-full bg-gradient-to-r from-sauce-red/40 to-cheese-gold/40 blur-3xl"
          animate={!isMobile ? {
            scale: [1, 1.5, 1],
            rotate: [0, 360],
            opacity: [0.3, 0.6, 0.3]
          } : undefined}
          transition={!isMobile ? {
            duration: 30,
            repeat: Infinity,
            ease: "linear"
          } : undefined}
        />
        <motion.div 
          className="absolute bottom-1/4 left-1/4 w-[600px] h-[600px] rounded-full bg-gradient-to-r from-purple-500/30 to-pink-500/30 blur-3xl"
          animate={!isMobile ? {
            scale: [1, 1.3, 1],
            rotate: [360, 0],
            opacity: [0.2, 0.5, 0.2]
          } : undefined}
          transition={!isMobile ? {
            duration: 25,
            repeat: Infinity,
            ease: "linear",
            delay: 2
          } : undefined}
        />
        
        {/* Floating celebration elements - only on desktop */}
        {!isMobile && [
          { icon: Trophy, size: 12, delay: 0 },
          { icon: Star, size: 10, delay: 0.5 },
          { icon: Rocket, size: 8, delay: 1 },
          { icon: Award, size: 10, delay: 1.5 },
          { icon: Zap, size: 8, delay: 2 },
          { icon: PartyPopper, size: 10, delay: 2.5 },
          { icon: Lightbulb, size: 8, delay: 3 },
          { icon: Mic, size: 10, delay: 3.5 }
        ].map((item, i) => (
          <motion.div
            key={i}
            className="absolute"
            style={{
              left: `${10 + (i * 12)}%`,
              top: `${15 + (i % 3) * 30}%`,
            }}
            animate={{
              y: [-40, 40, -40],
              rotate: [-20, 20, -20],
              opacity: [0.2, 0.6, 0.2],
              scale: [0.8, 1.2, 0.8]
            }}
            transition={{
              duration: 8 + i,
              repeat: Infinity,
              delay: item.delay,
            }}
          >
            <item.icon className={`w-${item.size} h-${item.size} text-cheese-gold/50`} />
          </motion.div>
        ))}
        
        {/* Confetti particles - desktop only */}
        {!isMobile && [...Array(20)].map((_, i) => (
          <motion.div
            key={`particle-${i}`}
            className="absolute w-2 h-2 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `-10px`,
              background: i % 3 === 0 ? '#FF4747' : i % 3 === 1 ? '#FFB74D' : '#FF6B6B',
            }}
            animate={{
              y: [0, window.innerHeight + 100],
              x: [0, (Math.random() - 0.5) * 200],
              rotate: [0, 360 * (Math.random() > 0.5 ? 1 : -1)],
              opacity: [1, 0]
            }}
            transition={{
              duration: 10 + Math.random() * 10,
              repeat: Infinity,
              delay: Math.random() * 20,
              ease: "linear"
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Demo Day Section */}
        <motion.div
          ref={heroRef}
          initial={{ opacity: 0, y: 50 }}
          animate={heroInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <motion.div 
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 backdrop-blur-sm border border-sauce-red/20 rounded-full text-sauce-red font-semibold text-sm mb-8"
            whileHover={!isMobile ? { scale: 1.05 } : undefined}
          >
            <Sparkles className="w-4 h-4" />
            The Grand Finale
            <Sparkles className="w-4 h-4" />
          </motion.div>
          
          <motion.h2 
            className="text-7xl sm:text-8xl lg:text-9xl font-black mb-8 text-transparent bg-clip-text bg-gradient-to-r from-sauce-red via-cheese-gold to-sauce-red leading-none"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={heroInView ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
            style={{
              backgroundSize: "200% 100%",
              filter: !isMobile ? "drop-shadow(0 8px 24px rgba(255, 71, 71, 0.5))" : "none",
            }}
          >
            DEMO DAY
          </motion.h2>
          
          <motion.div 
            className="flex items-center justify-center gap-4 mb-6 flex-wrap"
            initial={{ opacity: 0, y: 30 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <div className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-sauce-red/20 to-pink-500/20 rounded-2xl backdrop-blur-sm border border-sauce-red/30">
              <Calendar className="w-5 h-5 text-sauce-red" />
              <span className="text-xl font-bold text-accent-white">Saturday, September 27th</span>
            </div>
            <div className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-cheese-gold/20 to-orange-500/20 rounded-2xl backdrop-blur-sm border border-cheese-gold/30">
              <MapPin className="w-5 h-5 text-cheese-gold" />
              <span className="text-xl font-bold text-accent-white">Museum of Art & History</span>
            </div>
          </motion.div>
          
          <motion.p 
            className="text-2xl sm:text-3xl text-crust-beige/90 font-bold mb-4"
            initial={{ opacity: 0 }}
            animate={heroInView ? { opacity: 1 } : {}}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            The Ultimate Celebration of 10 Weeks of Building
          </motion.p>
          
          {/* Epic Stats */}
          <motion.div 
            className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 1 }}
          >
            {[
              { value: "10", label: "Live Product Demos", icon: Rocket, color: "from-blue-400 to-purple-500" },
              { value: "300+", label: "Expected Attendees", icon: Users, color: "from-green-400 to-emerald-500" },
              { value: "6", label: "Hours of Innovation", icon: Clock, color: "from-yellow-400 to-orange-500" },
              { value: "∞", label: "Possibilities Unlocked", icon: Sparkles, color: "from-red-400 to-pink-500" }
            ].map((stat, index) => (
              <motion.div 
                key={stat.label}
                className="text-center"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={heroInView ? { opacity: 1, scale: 1 } : {}}
                transition={{ duration: 0.4, delay: 1.2 + (index * 0.1) }}
                whileHover={!isMobile ? { scale: 1.05, y: -3 } : undefined}
              >
                <div className={`w-16 h-16 mx-auto mb-3 rounded-2xl bg-gradient-to-r ${stat.color} flex items-center justify-center shadow-lg`}>
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-3xl font-black text-accent-white mb-1">{stat.value}</div>
                <div className="text-sm text-crust-beige/70 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Event Details Card */}
        <motion.div
          ref={detailsRef}
          initial={{ opacity: 0, y: 50 }}
          animate={detailsInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="relative max-w-5xl mx-auto mb-20 px-4 sm:px-0"
        >
          <div className="absolute -inset-1 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-3xl blur-xl opacity-60" />
          
          <div className="relative bg-oven-black/90 backdrop-blur-xl rounded-2xl sm:rounded-3xl p-4 sm:p-8 lg:p-12 border border-sauce-red/30 shadow-2xl">
            <div className="grid md:grid-cols-2 gap-6 sm:gap-8 md:gap-12">
              {/* Left side - Key Info */}
              <div className="space-y-4 sm:space-y-6 lg:space-y-8">
                <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-accent-white mb-4 sm:mb-6 text-center md:text-left">Event Details</h3>
                
                <div className="space-y-4 sm:space-y-6">
                  {[
                    { icon: Calendar, label: "Date", value: "Saturday 27th", color: "from-sauce-red/20" },
                    { icon: Clock, label: "Time", value: "2:00 PM - 8:00 PM", color: "from-cheese-gold/20" },
                    { icon: MapPin, label: "Venue", value: "Museum of Art & History", subValue: "Downtown Santa Cruz", color: "from-purple-500/20" },
                    { icon: Ticket, label: "Access", value: "Ticketed Event", subValue: "Limited Availability", color: "from-green-500/20" }
                  ].map((detail, index) => (
                    <motion.div 
                      key={detail.label}
                      className="flex items-start space-x-3 sm:space-x-4 group"
                      initial={{ opacity: 0, x: -20 }}
                      animate={detailsInView ? { opacity: 1, x: 0 } : {}}
                      transition={{ duration: 0.4, delay: 0.2 + (index * 0.1) }}
                      whileHover={!isMobile ? { x: 10 } : undefined}
                    >
                      <div className={`p-2 sm:p-3 bg-gradient-to-r ${detail.color} rounded-xl sm:rounded-2xl group-hover:bg-opacity-30 transition-colors flex-shrink-0 mt-1`}>
                        <detail.icon className="w-5 h-5 sm:w-6 sm:h-6 text-sauce-red" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-xs sm:text-sm text-crust-beige/60 mb-1 font-medium uppercase tracking-wide">{detail.label}</p>
                        <p className="text-base sm:text-lg lg:text-xl font-bold text-accent-white leading-tight">{detail.value}</p>
                        {detail.subValue && <p className="text-xs sm:text-sm text-crust-beige/70 mt-1">{detail.subValue}</p>}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
              
              {/* Right side - Schedule */}
              <div className="mt-6 md:mt-0">
                <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-accent-white mb-4 sm:mb-6 text-center md:text-left">Schedule</h3>
                
                <div className="space-y-3 sm:space-y-4">
                  {eventSchedule.map((item, index) => (
                    <motion.div
                      key={index}
                      className="group relative"
                      initial={{ opacity: 0, x: 20 }}
                      animate={detailsInView ? { opacity: 1, x: 0 } : {}}
                      transition={{ duration: 0.4, delay: 0.4 + (index * 0.1) }}
                    >
                      <motion.div
                        className="flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 bg-accent-white/5 rounded-xl sm:rounded-2xl border border-accent-white/10 hover:border-accent-white/30 transition-all"
                        whileHover={!isMobile ? { scale: 1.02, x: 5 } : undefined}
                      >
                        <div className={`p-2 sm:p-3 rounded-lg sm:rounded-xl bg-gradient-to-r ${item.color} opacity-80 group-hover:opacity-100 transition-opacity flex-shrink-0 mt-1`}>
                          <item.icon className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-xs sm:text-sm text-cheese-gold font-semibold mb-1">{item.time}</p>
                          <p className="text-sm sm:text-base lg:text-lg font-bold text-accent-white leading-tight">{item.title}</p>
                          <p className="text-xs sm:text-sm text-crust-beige/70 mt-1 leading-relaxed">{item.description}</p>
                        </div>
                      </motion.div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
            
            {/* CTA Section */}
            <motion.div 
              className="mt-8 sm:mt-12 text-center pt-6 sm:pt-8 border-t border-accent-white/20"
              initial={{ opacity: 0, y: 20 }}
              animate={detailsInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              <p className="text-base sm:text-lg lg:text-xl text-crust-beige/80 mb-4 sm:mb-6 px-2 max-w-2xl mx-auto">
                Be part of the celebration as our builders showcase their products to Santa Cruz
              </p>
              <div className="flex justify-center">
                <motion.a
                  href="#apply"
                  whileHover={!isMobile ? { scale: 1.05 } : undefined} 
                  whileTap={{ scale: 0.95 }}
                >
                  <Button 
                    size="lg" 
                    className="bg-gradient-to-r from-sauce-red to-cheese-gold hover:from-sauce-red/90 hover:to-cheese-gold/90 text-oven-black font-bold text-sm sm:text-base lg:text-lg px-6 sm:px-10 py-4 sm:py-6 rounded-xl sm:rounded-2xl shadow-2xl border-0"
                  >
                    <Ticket className="mr-2 sm:mr-3 h-4 w-4 sm:h-6 sm:w-6" />
                    Apply for Demo Day
                  </Button>
                </motion.a>
              </div>
              <p className="text-xs sm:text-sm text-crust-beige/60 mt-3 sm:mt-4 px-2 max-w-xl mx-auto">
                Join the program to earn your spot on Demo Day • Limited teams selected
              </p>
            </motion.div>
          </div>
        </motion.div>

        {/* Bottom emphasis - Epic Finale */}
        <motion.div 
          ref={ctaRef}
          className="text-center mt-20"
          initial={{ opacity: 0, y: 50 }}
          animate={ctaInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <motion.div 
            className="mb-8"
            animate={!isMobile ? { y: [0, -10, 0] } : undefined}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <div className="text-6xl mb-4">🏆</div>
            <h3 className="text-3xl font-black text-accent-white mb-2">
              This is Your Moment
            </h3>
            <p className="text-xl text-crust-beige/80">
              10 weeks of building leads to this one spectacular day
            </p>
          </motion.div>

          <motion.div
            className="relative inline-flex flex-col sm:flex-row items-center gap-2 sm:gap-4 bg-gradient-to-r from-sauce-red/30 to-cheese-gold/30 backdrop-blur-md text-accent-white font-bold px-4 sm:px-8 lg:px-12 py-6 sm:py-8 rounded-2xl sm:rounded-3xl border-2 border-sauce-red/50 shadow-2xl max-w-xs sm:max-w-none mx-auto"
            animate={!isMobile ? { 
              scale: [1, 1.05, 1],
              opacity: [0.8, 1, 0.8],
              boxShadow: [
                "0 20px 40px rgba(255, 71, 71, 0.3)",
                "0 30px 60px rgba(255, 71, 71, 0.5)",
                "0 20px 40px rgba(255, 71, 71, 0.3)"
              ]
            } : undefined}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          >
            <motion.span 
              className="text-3xl sm:text-4xl"
              animate={!isMobile ? { rotate: [0, 360] } : undefined}
              transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
            >
              ⭐
            </motion.span>
            
            {/* Mobile: Stack the text vertically for better fit */}
            <div className="text-center sm:text-left">
              <div className="font-black text-lg sm:text-2xl lg:text-3xl leading-tight">
                <span className="block sm:inline">10 Teams</span>
                <span className="hidden sm:inline text-sauce-red mx-2">•</span>
                <span className="block sm:inline">10 Products</span>
                <span className="hidden sm:inline text-sauce-red mx-2">•</span>
                <span className="block sm:inline text-transparent bg-clip-text bg-gradient-to-r from-cheese-gold to-sauce-red">
                  1 Legendary Demo Day
                </span>
              </div>
            </div>
            
            <motion.span 
              className="text-3xl sm:text-4xl"
              animate={!isMobile ? { rotate: [360, 0] } : undefined}
              transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
            >
              🚀
            </motion.span>
          </motion.div>

          {/* Call to action particles - desktop only */}
          {!isMobile && (
            <div className="absolute inset-0 pointer-events-none">
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute"
                  style={{
                    left: '50%',
                    bottom: '10%',
                  }}
                  animate={{
                    x: [(i - 3) * 100, (i - 3) * 200],
                    y: [0, -200],
                    opacity: [0, 1, 0],
                    scale: [0, 1.5, 0],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    delay: i * 0.5,
                    ease: "easeOut"
                  }}
                >
                  <Sparkles className="w-6 h-6 text-cheese-gold" />
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </section>
  );
};

export default DemoDay; 