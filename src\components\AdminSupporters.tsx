import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { supabaseAdmin } from '@/integrations/supabase/admin-client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { 
  Heart,
  Users,
  Search,
  Filter,
  Eye,
  Edit,
  Mail,
  Phone,
  CheckCircle,
  Clock,
  AlertCircle,
  XCircle,
  Star,
  MessageCircle
} from 'lucide-react';

type Supporter = {
  id: string;
  full_name: string;
  email: string;
  phone: string | null;
  relationship: string;
  role: string;
  interests: string;
  status: string | null;
  created_at: string;
  notes: string | null;
  last_contacted: string | null;
  contact_method: string | null;
  engagement_level: string | null;
};

const AdminSupporters = () => {
  const [supporters, setSupporters] = useState<Supporter[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [relationshipFilter, setRelationshipFilter] = useState('all');
  const [selectedSupporter, setSelectedSupporter] = useState<Supporter | null>(null);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [editingNotes, setEditingNotes] = useState(false);
  const [tempNotes, setTempNotes] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    fetchSupporters();
  }, []);

  const fetchSupporters = async (showSuccessToast = false) => {
    try {
      setLoading(true);
      // Query the applications table filtering for supporter type
      const { data, error } = await supabaseAdmin
        .from('applications')
        .select('*')
        .eq('application_type', 'supporter')
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      const supporterData = (data || []).map(app => ({
        id: app.id,
        full_name: app.full_name,
        email: app.email,
        phone: app.phone,
        relationship: app.major || 'Friend', // Using major field to store relationship
        role: app.skills || 'General Support', // Using skills field to store role
        interests: app.why_pie_fi || '', // Using why_pie_fi field to store interests
        status: app.status || 'pending',
        created_at: app.created_at,
        notes: app.references || '', // Using references field to store notes
        last_contacted: null, // Could be added as a new field later
        contact_method: null,
        engagement_level: null
      }));
      
      setSupporters(supporterData);
      
      if (showSuccessToast) {
        toast({
          title: "Supporters Refreshed",
          description: `Successfully loaded ${supporterData.length} supporter${supporterData.length !== 1 ? 's' : ''}.`,
        });
      }
    } catch (error) {
      console.error('Error fetching supporters:', error);
      toast({
        title: "Error",
        description: "Failed to load supporters. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshClick = () => {
    fetchSupporters(true);
  };

  const updateSupporterStatus = async (supporterId: string, newStatus: string) => {
    setIsUpdatingStatus(true);
    try {
      const { data, error } = await supabaseAdmin
        .from('applications')
        .update({ status: newStatus })
        .eq('id', supporterId);

      if (error) {
        console.error('Database error:', error);
        throw error;
      }

      console.log('Update successful:', data);
      toast({
        title: "Status Updated",
        description: `Supporter status updated to ${newStatus}`,
      });

      // Refresh the supporters list
      fetchSupporters();
    } catch (error) {
      console.error('Error updating supporter status:', error);
      toast({
        title: "Error",
        description: "Failed to update supporter status",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const updateSupporterNotes = async (supporterId: string, notes: string) => {
    try {
      const { error } = await supabaseAdmin
        .from('applications')
        .update({ references: notes })
        .eq('id', supporterId);

      if (error) throw error;

      setSupporters(prev => prev.map(supporter => 
        supporter.id === supporterId 
          ? { ...supporter, notes }
          : supporter
      ));

      if (selectedSupporter?.id === supporterId) {
        setSelectedSupporter(prev => prev ? { ...prev, notes } : null);
      }

      setEditingNotes(false);
      toast({
        title: "Notes Updated",
        description: "Supporter notes have been updated successfully.",
      });
    } catch (error) {
      console.error('Error updating supporter notes:', error);
      toast({
        title: "Error",
        description: "Failed to update notes. Please try again.",
        variant: "destructive",
      });
    }
  };

  const calculateStats = (supporters: Supporter[]) => {
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    const thisWeek = supporters.filter(supporter => 
      new Date(supporter.created_at) >= weekAgo
    ).length;
    
    const engaged = supporters.filter(supporter => 
      supporter.status === 'engaged' || supporter.status === 'active'
    ).length;
    const pending = supporters.filter(supporter => 
      !supporter.status || supporter.status === 'pending'
    ).length;

    const statusCounts = {
      pending: pending,
      contacted: supporters.filter(s => s.status === 'contacted').length,
      engaged: engaged,
      inactive: supporters.filter(s => s.status === 'inactive').length,
    };

    const relationshipCounts = supporters.reduce((acc, supporter) => {
      const rel = supporter.relationship || 'Unknown';
      acc[rel] = (acc[rel] || 0) + 1;
      return acc;
    }, {} as {[key: string]: number});

    return {
      total: supporters.length,
      week: thisWeek,
      ...statusCounts,
      relationships: relationshipCounts
    };
  };

  const stats = calculateStats(supporters);

  const filteredSupporters = supporters.filter(supporter => {
    const matchesSearch = searchTerm === '' || 
      supporter.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supporter.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supporter.relationship.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supporter.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supporter.interests.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || 
      (statusFilter === 'pending' && (!supporter.status || supporter.status === 'pending')) ||
      (statusFilter !== 'pending' && supporter.status === statusFilter);

    const matchesRelationship = relationshipFilter === 'all' || 
      supporter.relationship === relationshipFilter;

    return matchesSearch && matchesStatus && matchesRelationship;
  });

  const uniqueRelationships = [...new Set(supporters.map(s => s.relationship))].sort();

  const getStatusIcon = (status: string | null) => {
    switch (status) {
      case 'contacted': return <MessageCircle className="w-4 h-4 text-blue-500" />;
      case 'engaged': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'active': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'inactive': return <XCircle className="w-4 h-4 text-gray-500" />;
      default: return <Clock className="w-4 h-4 text-yellow-500" />;
    }
  };

  const getStatusBadgeClass = (status: string | null) => {
    switch (status) {
      case 'contacted': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'engaged': return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Clock className="w-8 h-8 animate-spin text-blue-500" />
        <span className="ml-2 text-lg">Loading supporters...</span>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="space-y-8 text-accent-white"
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-accent-white flex items-center gap-3">
            <Heart className="h-8 w-8 text-sauce-red" />
            Community Supporters
          </h2>
          <p className="text-crust-beige/80 mt-2">Manage and track community support</p>
        </div>
        <Button 
          onClick={handleRefreshClick}
          disabled={loading}
          className="bg-gradient-to-r from-sauce-red to-cheese-gold hover:from-sauce-red/90 hover:to-cheese-gold/90 text-oven-black font-bold"
        >
          <Clock className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          { label: 'Total Supporters', value: stats.total, icon: Users, color: 'from-blue-400 to-blue-600' },
          { label: 'This Week', value: stats.week, icon: Clock, color: 'from-green-400 to-green-600' },
          { label: 'Active', value: stats.engaged, icon: CheckCircle, color: 'from-emerald-400 to-emerald-600' },
          { label: 'Pending', value: stats.pending, icon: Clock, color: 'from-yellow-400 to-yellow-600' }
        ].map((stat, index) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="relative overflow-hidden border border-accent-white/20 bg-gradient-to-br from-accent-white/10 to-accent-white/5 backdrop-blur-md">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-crust-beige/80">{stat.label}</p>
                    <p className="text-3xl font-bold text-accent-white">{stat.value}</p>
                  </div>
                  <div className={`p-3 rounded-2xl bg-gradient-to-r ${stat.color}`}>
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search supporters..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="contacted">Contacted</SelectItem>
                <SelectItem value="engaged">Engaged</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>

            <Select value={relationshipFilter} onValueChange={setRelationshipFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by relationship" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Relationships</SelectItem>
                {uniqueRelationships.map(rel => (
                  <SelectItem key={rel} value={rel}>{rel}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Supporters Table */}
      <Card className="bg-accent-white/10 backdrop-blur-md border border-accent-white/20 shadow-xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-accent-white">
            <Users className="h-5 w-5" />
            Supporters ({filteredSupporters.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-sauce-red mx-auto"></div>
              <p className="mt-2 text-crust-beige/80">Loading supporters...</p>
            </div>
          ) : filteredSupporters.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-crust-beige/60 mx-auto mb-4" />
              <p className="text-crust-beige/80">No supporters found</p>
            </div>
          ) : (
            <div className="overflow-x-auto rounded-xl border border-accent-white/20">
              <Table>
                <TableHeader className="bg-accent-white/5">
                  <TableRow>
                    <TableHead className="text-accent-white font-semibold">Name</TableHead>
                    <TableHead className="text-accent-white font-semibold">Email</TableHead>
                    <TableHead className="text-accent-white font-semibold">Relationship</TableHead>
                    <TableHead className="text-accent-white font-semibold">Role</TableHead>
                    <TableHead className="text-accent-white font-semibold">Status</TableHead>
                    <TableHead className="text-accent-white font-semibold">Joined</TableHead>
                    <TableHead className="text-accent-white font-semibold">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSupporters.map((supporter, index) => (
                    <motion.tr
                      key={supporter.id}
                      className="hover:bg-accent-white/5 transition-colors border-accent-white/10"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                    >
                      <TableCell className="font-medium text-accent-white">{supporter.full_name}</TableCell>
                      <TableCell>
                        <a href={`mailto:${supporter.email}`} className="text-cheese-gold hover:text-cheese-gold/80 hover:underline">
                          {supporter.email}
                        </a>
                      </TableCell>
                      <TableCell className="text-crust-beige/80">{supporter.relationship}</TableCell>
                      <TableCell>
                        <div className="max-w-xs">
                          <span className="text-crust-beige/80 text-sm">
                            {supporter.role.split(' - ')[0]}
                            {supporter.role.includes('Skills:') && (
                              <div className="text-xs text-crust-beige/60 mt-1">
                                {supporter.role.split('Skills: ')[1]}
                              </div>
                            )}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge 
                          className={
                            supporter.status === 'engaged' || supporter.status === 'active' 
                              ? 'bg-green-500/20 text-green-400 border-green-500/30' 
                              : supporter.status === 'contacted'
                              ? 'bg-blue-500/20 text-blue-400 border-blue-500/30'
                              : supporter.status === 'inactive'
                              ? 'bg-red-500/20 text-red-400 border-red-500/30'
                              : 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
                          }
                        >
                          {getStatusIcon(supporter.status)}
                          {supporter.status || 'pending'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-crust-beige/80">{new Date(supporter.created_at).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-blue-400 hover:text-blue-300 hover:bg-blue-500/20"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl bg-oven-black border border-accent-white/20">
                            <DialogHeader>
                              <DialogTitle className="flex items-center gap-2 text-accent-white">
                                <Heart className="h-5 w-5 text-sauce-red" />
                                {supporter.full_name}
                              </DialogTitle>
                              <DialogDescription className="text-crust-beige/80">
                                View and manage supporter details, contact information, and status.
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-6 text-accent-white">
                              {/* Contact Information */}
                              <div className="bg-accent-white/10 backdrop-blur-md rounded-2xl border border-accent-white/20 p-6">
                                <h3 className="font-semibold text-sauce-red mb-4 flex items-center gap-2">
                                  <Mail className="h-5 w-5" />
                                  Contact Information
                                </h3>
                                <div className="space-y-2">
                                  <div className="flex items-center gap-3">
                                    <Mail className="h-4 w-4 text-cheese-gold" />
                                    <a href={`mailto:${supporter.email}`} className="text-cheese-gold hover:text-cheese-gold/80 hover:underline">
                                      {supporter.email}
                                    </a>
                                  </div>
                                  {supporter.phone && (
                                    <div className="flex items-center gap-3">
                                      <Phone className="h-4 w-4 text-cheese-gold" />
                                      <a href={`tel:${supporter.phone}`} className="text-cheese-gold hover:text-cheese-gold/80 hover:underline">
                                        {supporter.phone}
                                      </a>
                                    </div>
                                  )}
                                </div>
                              </div>

                              {/* Role & Interests */}
                              <div className="bg-accent-white/10 backdrop-blur-md rounded-2xl border border-accent-white/20 p-6">
                                <h3 className="font-semibold text-sauce-red mb-4 flex items-center gap-2">
                                  <Star className="h-5 w-5" />
                                  Role & Interests
                                </h3>
                                <div className="space-y-3">
                                  <div>
                                    <span className="text-crust-beige/80 text-sm">Role:</span>
                                    <p className="text-accent-white mt-1">{supporter.role.split(' | Skills:')[0]}</p>
                                  </div>
                                  {supporter.role.includes('Skills:') && (
                                    <div>
                                      <span className="text-crust-beige/80 text-sm">Skills:</span>
                                      <p className="text-accent-white mt-1">{supporter.role.split('Skills: ')[1]}</p>
                                    </div>
                                  )}
                                </div>
                                {supporter.interests && (
                                  <div className="mt-4">
                                    <span className="text-crust-beige/80 text-sm">Interests:</span>
                                    <p className="text-accent-white mt-1">{supporter.interests}</p>
                                  </div>
                                )}
                              </div>

                              {/* Status Management */}
                              <div className="bg-accent-white/10 backdrop-blur-md rounded-2xl border border-accent-white/20 p-6">
                                <h3 className="font-semibold text-sauce-red mb-4 flex items-center gap-2">
                                  <CheckCircle className="h-5 w-5" />
                                  Status Management
                                </h3>
                                <div className="flex items-center gap-4">
                                  <span className="text-sm font-medium text-crust-beige/80">Current Status:</span>
                                  <Select
                                    value={supporter.status || 'pending'}
                                    onValueChange={(value) => updateSupporterStatus(supporter.id, value)}
                                    disabled={isUpdatingStatus}
                                  >
                                    <SelectTrigger className="w-32 bg-accent-white/20 border-accent-white/30 text-accent-white">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent className="bg-oven-black border-accent-white/20">
                                      <SelectItem value="pending">Pending</SelectItem>
                                      <SelectItem value="contacted">Contacted</SelectItem>
                                      <SelectItem value="engaged">Engaged</SelectItem>
                                      <SelectItem value="active">Active</SelectItem>
                                      <SelectItem value="inactive">Inactive</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  {isUpdatingStatus && <Clock className="w-4 h-4 animate-spin" />}
                                </div>
                              </div>

                              {/* Notes */}
                              <div className="bg-accent-white/10 backdrop-blur-md rounded-2xl border border-accent-white/20 p-6">
                                <h3 className="font-semibold text-sauce-red mb-4 flex items-center gap-2">
                                  <MessageCircle className="h-5 w-5" />
                                  Notes
                                </h3>
                                {supporter.notes ? (
                                  <div className="bg-accent-white/5 rounded-lg p-4 border border-accent-white/10">
                                    <p className="text-crust-beige">{supporter.notes}</p>
                                  </div>
                                ) : (
                                  <p className="text-crust-beige/60 italic">No notes available</p>
                                )}
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </motion.tr>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default AdminSupporters;
