-- Streamlined Onboarding Tables - simplified and focused
-- Drop the complex tables and recreate with streamlined structure

-- Drop existing tables if they exist
DROP TABLE IF EXISTS user_learning_events CASCADE;
DROP TABLE IF EXISTS comprehensive_user_profiles CASCADE;
DROP TABLE IF EXISTS ai_followup_conversations CASCADE;
DROP TABLE IF EXISTS onboarding_responses CASCADE;

-- 1. Streamlined Onboarding Responses (14 focused questions)
CREATE TABLE onboarding_responses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  track VARCHAR NOT NULL CHECK (track IN ('newbie', 'builder', 'scaler')),
  
  -- Core Concept (4 questions)
  problem_description TEXT,
  solution_approach TEXT,
  target_audience TEXT,
  unique_value_proposition TEXT,
  
  -- Background & Skills (3 questions)
  technical_background VARCHAR CHECK (technical_background IN ('beginner', 'intermediate', 'expert')),
  previous_experience TEXT,
  technical_skills TEXT, -- simplified to single text field
  
  -- Time & Goals (4 questions)
  available_time_per_week INTEGER,
  primary_goal VARCHAR CHECK (primary_goal IN ('learn', 'build_mvp', 'find_users', 'scale')),
  success_metrics TEXT,
  biggest_challenge TEXT,
  
  -- Learning & Support (3 questions)
  learning_style VARCHAR CHECK (learning_style IN ('hands-on', 'video', 'reading', 'mentorship')),
  preferred_communication VARCHAR CHECK (preferred_communication IN ('daily', 'weekly', 'bi-weekly')),
  collaboration_preference VARCHAR CHECK (collaboration_preference IN ('solo', 'small-team', 'community', 'mentor-guided')),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. AI Follow-up Conversations (Phase 2)
CREATE TABLE ai_followup_conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  onboarding_response_id UUID REFERENCES onboarding_responses(id) ON DELETE CASCADE,
  
  conversation_data JSONB NOT NULL, -- full conversation history
  ai_satisfaction_score INTEGER DEFAULT 0, -- 0-100
  areas_needing_clarification TEXT[],
  key_insights_extracted JSONB,
  
  total_questions_asked INTEGER DEFAULT 0,
  conversation_completed BOOLEAN DEFAULT false,
  completion_reason VARCHAR, -- 'satisfied', 'max_questions', 'user_stop'
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Comprehensive User Profiles (Phase 3)
CREATE TABLE comprehensive_user_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  onboarding_response_id UUID REFERENCES onboarding_responses(id),
  followup_conversation_id UUID REFERENCES ai_followup_conversations(id),
  
  -- AI-Generated Profile Analysis
  profile_summary TEXT,
  strengths TEXT[],
  knowledge_gaps TEXT[],
  personality_traits JSONB,
  
  -- Project Analysis
  project_viability_score INTEGER,
  market_opportunity_assessment TEXT,
  technical_feasibility_analysis TEXT,
  recommended_next_steps TEXT[],
  potential_blockers TEXT[],
  
  -- Personalization Data
  optimal_learning_path JSONB,
  mentor_matching_criteria JSONB,
  resource_preferences JSONB,
  communication_strategy JSONB,
  
  -- Goals & Tracking
  personalized_goals JSONB,
  success_probability_factors JSONB,
  risk_factors JSONB,
  
  -- Dashboard Personalization
  preferred_dashboard_layout VARCHAR,
  key_metrics_to_track TEXT[],
  notification_preferences JSONB,
  
  profile_confidence_score INTEGER DEFAULT 0,
  last_updated_by_ai TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_onboarding_responses_user_id ON onboarding_responses(user_id);
CREATE INDEX idx_onboarding_responses_track ON onboarding_responses(track);

CREATE INDEX idx_ai_followup_conversations_user_id ON ai_followup_conversations(user_id);
CREATE INDEX idx_ai_followup_conversations_completed ON ai_followup_conversations(conversation_completed);

CREATE INDEX idx_comprehensive_user_profiles_user_id ON comprehensive_user_profiles(user_id);
CREATE INDEX idx_comprehensive_user_profiles_confidence ON comprehensive_user_profiles(profile_confidence_score);

-- RLS Policies
ALTER TABLE onboarding_responses ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can manage own onboarding responses" ON onboarding_responses
  FOR ALL USING (auth.uid() = user_id);

ALTER TABLE ai_followup_conversations ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can manage own AI conversations" ON ai_followup_conversations
  FOR ALL USING (auth.uid() = user_id);

ALTER TABLE comprehensive_user_profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can read own comprehensive profile" ON comprehensive_user_profiles
  FOR ALL USING (auth.uid() = user_id);

-- Triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_onboarding_responses_updated_at
  BEFORE UPDATE ON onboarding_responses
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_followup_conversations_updated_at
  BEFORE UPDATE ON ai_followup_conversations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_comprehensive_user_profiles_updated_at
  BEFORE UPDATE ON comprehensive_user_profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 