import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Cloud, 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  ExternalLink,
  Copy,
  Terminal,
  Settings
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import edgeFunctionDeploymentService from '@/services/edgeFunctionDeployment';

const EdgeFunctionDeployment: React.FC = () => {
  const [deploymentStatus, setDeploymentStatus] = useState<{
    deployed: boolean;
    working: boolean;
    url?: string;
  }>({ deployed: false, working: false });
  
  const [isDeploying, setIsDeploying] = useState(false);
  const [deploymentResult, setDeploymentResult] = useState<any>(null);
  const [showInstructions, setShowInstructions] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkStatus();
  }, []);

  const checkStatus = async () => {
    setLoading(true);
    try {
      const status = await edgeFunctionDeploymentService.checkDeploymentStatus();
      setDeploymentStatus(status);
    } catch (error) {
      console.error('Error checking deployment status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeploy = async () => {
    setIsDeploying(true);
    setDeploymentResult(null);
    
    try {
      const result = await edgeFunctionDeploymentService.deployAIAnalysisFunction();
      setDeploymentResult(result);
      
      if (result.success) {
        // Recheck status after successful deployment
        setTimeout(checkStatus, 2000);
      }
    } catch (error) {
      setDeploymentResult({
        success: false,
        message: 'Deployment failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsDeploying(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getStatusBadge = () => {
    if (loading) {
      return <Badge variant="secondary"><Loader2 className="h-3 w-3 mr-1 animate-spin" />Checking...</Badge>;
    }
    
    if (deploymentStatus.deployed && deploymentStatus.working) {
      return <Badge className="bg-green-500/20 text-green-400 border-green-500/30"><CheckCircle className="h-3 w-3 mr-1" />Deployed & Working</Badge>;
    } else if (deploymentStatus.deployed) {
      return <Badge variant="outline" className="text-yellow-400 border-yellow-400/30"><AlertCircle className="h-3 w-3 mr-1" />Deployed (Issues)</Badge>;
    } else {
      return <Badge variant="outline" className="text-red-400 border-red-400/30"><AlertCircle className="h-3 w-3 mr-1" />Not Deployed</Badge>;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <Card className="bg-gradient-to-br from-accent-white/10 to-accent-white/5 backdrop-blur-xl border border-accent-white/20 shadow-2xl rounded-2xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl text-accent-white flex items-center gap-3">
              <Cloud className="h-6 w-6 text-blue-400" />
              Edge Function Deployment
            </CardTitle>
            {getStatusBadge()}
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Status Section */}
          <div className="space-y-4">
            <h3 className="text-accent-white font-semibold">Current Status</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-accent-white/5 rounded-lg border border-accent-white/10">
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-3 h-3 rounded-full ${deploymentStatus.deployed ? 'bg-green-400' : 'bg-red-400'}`} />
                  <span className="text-accent-white/80 text-sm font-medium">Deployment Status</span>
                </div>
                <p className="text-accent-white text-sm">
                  {deploymentStatus.deployed ? 'Function is deployed' : 'Function not found'}
                </p>
              </div>
              
              <div className="p-4 bg-accent-white/5 rounded-lg border border-accent-white/10">
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-3 h-3 rounded-full ${deploymentStatus.working ? 'bg-green-400' : 'bg-red-400'}`} />
                  <span className="text-accent-white/80 text-sm font-medium">Function Health</span>
                </div>
                <p className="text-accent-white text-sm">
                  {deploymentStatus.working ? 'Responding correctly' : 'Not responding'}
                </p>
              </div>
            </div>

            {deploymentStatus.url && (
              <div className="p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-400 text-sm font-medium">Function URL</p>
                    <p className="text-accent-white/80 text-sm font-mono">{deploymentStatus.url}</p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(deploymentStatus.url!)}
                      className="border-blue-400/30 text-blue-400 hover:bg-blue-400/10"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => window.open(deploymentStatus.url, '_blank')}
                      className="border-blue-400/30 text-blue-400 hover:bg-blue-400/10"
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Deployment Result */}
          {deploymentResult && (
            <Alert className={deploymentResult.success ? 'border-green-500/30 bg-green-500/10' : 'border-red-500/30 bg-red-500/10'}>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-accent-white">
                {deploymentResult.message}
                {deploymentResult.error && (
                  <div className="mt-2 text-sm text-red-400">
                    Error: {deploymentResult.error}
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex gap-4">
            <Button
              onClick={handleDeploy}
              disabled={isDeploying}
              className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white"
            >
              {isDeploying ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deploying...
                </>
              ) : (
                <>
                  <Cloud className="h-4 w-4 mr-2" />
                  Deploy Function
                </>
              )}
            </Button>

            <Button
              onClick={checkStatus}
              variant="outline"
              disabled={loading}
              className="border-accent-white/30 text-accent-white hover:bg-accent-white/10"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <CheckCircle className="h-4 w-4 mr-2" />
              )}
              Refresh Status
            </Button>

            <Button
              onClick={() => setShowInstructions(!showInstructions)}
              variant="outline"
              className="border-accent-white/30 text-accent-white hover:bg-accent-white/10"
            >
              <Terminal className="h-4 w-4 mr-2" />
              Manual Setup
            </Button>
          </div>

          {/* Manual Instructions */}
          {showInstructions && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="p-4 bg-accent-white/5 rounded-lg border border-accent-white/10"
            >
              <h4 className="text-accent-white font-semibold mb-3 flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Manual Deployment Instructions
              </h4>
              <div className="space-y-3 text-sm text-accent-white/80">
                <div>
                  <p className="font-medium text-accent-white mb-2">Option 1: Supabase Dashboard</p>
                  <ol className="list-decimal list-inside space-y-1 ml-4">
                    <li>Go to <a href={`https://supabase.com/dashboard/project/xmqexzoockycregsmyur/functions`} target="_blank" className="text-blue-400 hover:underline">Supabase Functions Dashboard</a></li>
                    <li>Click "Create Function" and name it "ai-analysis"</li>
                    <li>Copy the function code from the project files</li>
                    <li>Set the required environment variables</li>
                  </ol>
                </div>
                
                <div>
                  <p className="font-medium text-accent-white mb-2">Option 2: Install Supabase CLI</p>
                  <div className="bg-oven-black/50 p-3 rounded font-mono text-xs">
                    <p>npm install -g supabase</p>
                    <p>supabase functions deploy ai-analysis --project-ref xmqexzoockycregsmyur</p>
                  </div>
                </div>

                <div>
                  <p className="font-medium text-accent-white mb-2">Required Environment Variables:</p>
                  <div className="bg-oven-black/50 p-3 rounded font-mono text-xs space-y-1">
                    <p>GEMINI_API_KEY: {import.meta.env.VITE_GEMINI_API_KEY || 'Not set'}</p>
                    <p>SUPABASE_URL: https://xmqexzoockycregsmyur.supabase.co</p>
                    <p>SUPABASE_ANON_KEY: {import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Set' : 'Not set'}</p>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default EdgeFunctionDeployment;
