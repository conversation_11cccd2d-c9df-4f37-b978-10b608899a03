// User Memory Service for tracking progress, context, and personalized AI interactions
import { supabase } from '@/integrations/supabase/client';

export interface UserMemoryEntry {
  id?: string;
  user_id: string;
  memory_type: 'interaction' | 'progress' | 'insight' | 'preference' | 'context';
  content: any;
  metadata?: {
    stage_id?: number;
    confidence_score?: number;
    source?: string;
    tags?: string[];
  };
  created_at?: string;
  updated_at?: string;
}

export interface UserContext {
  current_stage: number;
  stage_confidence: number;
  key_challenges: string[];
  recent_progress: string[];
  preferences: {
    learning_style?: string;
    communication_frequency?: string;
    help_format?: string;
  };
  project_info: {
    description?: string;
    current_focus?: string;
    next_milestones?: string[];
    target_audience?: string;
    technical_background?: string;
    primary_goal?: string;
  };
  ai_insights: {
    strengths?: string[];
    knowledge_gaps?: string[];
    recommended_actions?: string[];
  };
}

class UserMemoryService {
  // Store a memory entry
  async storeMemory(entry: Omit<UserMemoryEntry, 'id' | 'created_at' | 'updated_at'>): Promise<UserMemoryEntry | null> {
    try {
      const { data, error } = await supabase
        .from('user_memory_entries')
        .insert(entry)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error storing memory:', error);
      return null;
    }
  }

  // Get user memories by type
  async getUserMemories(
    userId: string, 
    memoryType?: string, 
    limit = 50
  ): Promise<UserMemoryEntry[]> {
    try {
      let query = supabase
        .from('user_memory_entries')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (memoryType) {
        query = query.eq('memory_type', memoryType);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching memories:', error);
      return [];
    }
  }

  // Build comprehensive user context
  async buildUserContext(userId: string): Promise<UserContext> {
    try {
      // Get recent memories
      const memories = await this.getUserMemories(userId, undefined, 100);

      // Get user profile data
      const { data: profile } = await supabase
        .from('comprehensive_user_profiles')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      // Get recent updates
      const { data: recentUpdates } = await supabase
        .from('daily_updates')
        .select('content, ai_insights, created_at')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(10);

      // Get current stage from authoritative source
      const { data: currentStageData } = await supabase
        .from('user_dev_journey_stages')
        .select('stage_id, confidence_score, reasoning, next_actions, applicable_frameworks')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      // Get onboarding data for comprehensive context
      const { data: onboardingData } = await supabase
        .from('onboarding_responses')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      // Get project context from onboarding data if no dedicated project table exists
      const project = onboardingData ? {
        description: onboardingData.problem_description,
        solution_approach: onboardingData.solution_approach,
        target_audience: onboardingData.target_audience,
        technical_background: onboardingData.technical_background,
        primary_goal: onboardingData.primary_goal
      } : null;

      // Build context from available data
      const context: UserContext = {
        current_stage: this.extractCurrentStage(currentStageData, memories, profile),
        stage_confidence: this.extractStageConfidence(currentStageData, memories, profile),
        key_challenges: this.extractChallenges(memories, recentUpdates, profile, onboardingData),
        recent_progress: this.extractRecentProgress(recentUpdates),
        preferences: this.extractPreferences(memories, profile),
        project_info: this.extractProjectInfo(project, profile, onboardingData),
        ai_insights: this.extractAIInsights(profile, recentUpdates)
      };

      return context;
    } catch (error) {
      console.error('Error building user context:', error);
      return this.getDefaultContext();
    }
  }

  // Store user interaction for learning
  async storeInteraction(
    userId: string,
    interactionType: 'question' | 'help_request' | 'update' | 'feedback',
    content: string,
    context?: any
  ): Promise<void> {
    await this.storeMemory({
      user_id: userId,
      memory_type: 'interaction',
      content: {
        type: interactionType,
        content: content,
        context: context
      },
      metadata: {
        source: 'user_interaction',
        tags: [interactionType]
      }
    });
  }

  // Store progress milestone
  async storeProgress(
    userId: string,
    milestone: string,
    details: any,
    stageId?: number
  ): Promise<void> {
    await this.storeMemory({
      user_id: userId,
      memory_type: 'progress',
      content: {
        milestone: milestone,
        details: details,
        timestamp: new Date().toISOString()
      },
      metadata: {
        stage_id: stageId,
        source: 'progress_tracking',
        tags: ['milestone', 'progress']
      }
    });
  }

  // Store AI insight
  async storeInsight(
    userId: string,
    insight: string,
    category: string,
    confidence: number
  ): Promise<void> {
    await this.storeMemory({
      user_id: userId,
      memory_type: 'insight',
      content: {
        insight: insight,
        category: category,
        generated_at: new Date().toISOString()
      },
      metadata: {
        confidence_score: confidence,
        source: 'ai_analysis',
        tags: [category, 'ai_insight']
      }
    });
  }

  // Update user preferences
  async updatePreferences(
    userId: string,
    preferences: Record<string, any>
  ): Promise<void> {
    await this.storeMemory({
      user_id: userId,
      memory_type: 'preference',
      content: preferences,
      metadata: {
        source: 'user_preference',
        tags: ['preferences']
      }
    });
  }

  // Update comprehensive user profile with AI analysis results
  async updateProfileFromAIAnalysis(
    userId: string,
    aiAnalysis: {
      keyLearnings: string[];
      profileUpdates: {
        newSkills?: string[];
        updatedChallenges?: string[];
        strengthsRevealed?: string[];
      };
    },
    context: {
      milestoneId: string;
      stageId: number;
      submissionContent: string;
    }
  ): Promise<void> {
    try {
      // Get current profile
      const { data: currentProfile } = await supabase
        .from('comprehensive_user_profiles')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (currentProfile) {
        // Update existing profile with new insights
        const updatedProfile = {
          ...currentProfile,
          // Add new skills
          skills: [
            ...(currentProfile.skills || []),
            ...(aiAnalysis.profileUpdates.newSkills || [])
          ].filter((skill, index, arr) => arr.indexOf(skill) === index), // Remove duplicates

          // Update knowledge gaps/challenges
          knowledge_gaps: [
            ...(currentProfile.knowledge_gaps || []),
            ...(aiAnalysis.profileUpdates.updatedChallenges || [])
          ].filter((gap, index, arr) => arr.indexOf(gap) === index),

          // Add revealed strengths
          strengths: [
            ...(currentProfile.strengths || []),
            ...(aiAnalysis.profileUpdates.strengthsRevealed || [])
          ].filter((strength, index, arr) => arr.indexOf(strength) === index),

          // Add key learnings to profile summary
          profile_summary: currentProfile.profile_summary +
            `\n\nMilestone Learning (${context.milestoneId}): ${aiAnalysis.keyLearnings.join('; ')}`,

          // Update last modified
          updated_at: new Date().toISOString()
        };

        // Store updated profile
        await supabase
          .from('comprehensive_user_profiles')
          .update(updatedProfile)
          .eq('id', currentProfile.id);

        console.log('✅ Profile updated with AI analysis insights');
      }

      // Also store the learning as a memory entry
      await this.storeMemory({
        user_id: userId,
        memory_type: 'insight',
        content: {
          milestone_learning: true,
          milestone_id: context.milestoneId,
          stage_id: context.stageId,
          key_learnings: aiAnalysis.keyLearnings,
          profile_updates: aiAnalysis.profileUpdates,
          submission_content: context.submissionContent.substring(0, 500) // Store first 500 chars
        },
        metadata: {
          stage_id: context.stageId,
          source: 'milestone_ai_analysis',
          tags: ['milestone', 'learning', 'profile_update']
        }
      });

    } catch (error) {
      console.error('Error updating profile from AI analysis:', error);
    }
  }

  // Get contextual memory for AI
  async getContextualMemory(
    userId: string,
    currentSituation: string,
    maxTokens = 2000
  ): Promise<string> {
    const memories = await this.getUserMemories(userId);
    const context = await this.buildUserContext(userId);
    
    // Build contextual summary
    let contextualMemory = `# User Context Summary

`;
    contextualMemory += `Current Stage: Stage ${context.current_stage} (${context.stage_confidence}% confidence)
`;
    contextualMemory += `Key Challenges: ${context.key_challenges.join(', ')}
`;
    contextualMemory += `Recent Progress: ${context.recent_progress.slice(0, 3).join('; ')}
`;

    // Add project information
    if (context.project_info.description) {
      contextualMemory += `Project: ${context.project_info.description}
`;
    }
    if (context.project_info.target_audience) {
      contextualMemory += `Target Audience: ${context.project_info.target_audience}
`;
    }
    if (context.project_info.primary_goal) {
      contextualMemory += `Primary Goal: ${context.project_info.primary_goal}
`;
    }

    // Add AI insights
    if (context.ai_insights.strengths?.length) {
      contextualMemory += `Strengths: ${context.ai_insights.strengths.join(', ')}
`;
    }

    contextualMemory += `
`;

    // Add relevant memories based on situation
    const relevantMemories = memories.filter(memory => {
      const content = JSON.stringify(memory.content).toLowerCase();
      const situation = currentSituation.toLowerCase();
      
      return content.includes(situation) ||
             memory.metadata?.tags?.some(tag => situation.includes(tag)) ||
             memory.memory_type === 'insight';
    }).slice(0, 5);

    if (relevantMemories.length > 0) {
      contextualMemory += `## Relevant Previous Context
`;
      relevantMemories.forEach(memory => {
        contextualMemory += `- ${memory.memory_type}: ${JSON.stringify(memory.content).substring(0, 200)}...
`;
      });
    }

    // Truncate if too long
    if (contextualMemory.length > maxTokens) {
      contextualMemory = contextualMemory.substring(0, maxTokens) + '...';
    }

    return contextualMemory;
  }

  // Helper methods for context extraction
  private extractCurrentStage(currentStageData: any, memories: UserMemoryEntry[], profile: any): number {
    // Prioritize authoritative stage data from user_dev_journey_stages table
    if (currentStageData?.stage_id !== undefined) {
      return currentStageData.stage_id;
    }

    // Fallback to stage info in memories
    const stageMemory = memories.find(m => m.metadata?.stage_id !== undefined);
    if (stageMemory) return stageMemory.metadata!.stage_id!;

    // Final fallback to profile or default
    return profile?.dev_journey_stage || 0;
  }

  private extractStageConfidence(currentStageData: any, memories: UserMemoryEntry[], profile: any): number {
    // Prioritize authoritative confidence from user_dev_journey_stages table
    if (currentStageData?.confidence_score !== undefined) {
      return currentStageData.confidence_score;
    }

    // Fallback to confidence in memories
    const confidenceMemory = memories.find(m => m.metadata?.confidence_score !== undefined);
    if (confidenceMemory) return confidenceMemory.metadata!.confidence_score!;

    return profile?.stage_confidence || profile?.profile_confidence_score || 50;
  }

  private extractChallenges(memories: UserMemoryEntry[], updates: any[], profile: any, onboardingData?: any): string[] {
    const challenges = new Set<string>();

    // From memories
    memories.filter(m => m.memory_type === 'interaction')
      .forEach(m => {
        if (m.content?.content?.includes('challenge') || m.content?.content?.includes('problem')) {
          challenges.add(m.content.content.substring(0, 100));
        }
      });

    // From profile
    if (profile?.knowledge_gaps) {
      profile.knowledge_gaps.forEach((gap: string) => challenges.add(gap));
    }

    // From onboarding data
    if (onboardingData) {
      if (onboardingData.biggest_challenge) {
        challenges.add(onboardingData.biggest_challenge);
      }
      if (onboardingData.technical_background === 'beginner') {
        challenges.add('Technical implementation');
      }
      if (onboardingData.available_time_per_week && onboardingData.available_time_per_week < 10) {
        challenges.add('Limited time availability');
      }
    }

    // From recent updates
    updates?.forEach(update => {
      if (update.content?.includes('struggle') || update.content?.includes('difficult')) {
        challenges.add(update.content.substring(0, 100));
      }
    });

    return Array.from(challenges).slice(0, 5);
  }

  private extractRecentProgress(updates: any[]): string[] {
    return updates?.map(update => update.content.substring(0, 150)).slice(0, 5) || [];
  }

  private extractPreferences(memories: UserMemoryEntry[], profile: any): UserContext['preferences'] {
    const prefMemories = memories.filter(m => m.memory_type === 'preference');
    const latestPrefs = prefMemories[0]?.content || {};
    
    return {
      learning_style: profile?.resource_preferences?.learning_format || latestPrefs.learning_style,
      communication_frequency: profile?.communication_strategy?.frequency || latestPrefs.communication_frequency,
      help_format: latestPrefs.help_format || 'detailed'
    };
  }

  private extractProjectInfo(project: any, profile: any, onboardingData?: any): UserContext['project_info'] {
    return {
      description: project?.description ||
                  onboardingData?.problem_description ||
                  profile?.market_opportunity_assessment ||
                  'No project description available',
      current_focus: project?.current_stage ||
                    onboardingData?.solution_approach ||
                    profile?.recommended_next_steps?.[0] ||
                    'ideation',
      next_milestones: profile?.recommended_next_steps || [],
      target_audience: onboardingData?.target_audience || 'Not specified',
      technical_background: onboardingData?.technical_background || 'Not specified',
      primary_goal: onboardingData?.primary_goal || 'Not specified'
    };
  }

  private extractAIInsights(profile: any, updates: any[]): UserContext['ai_insights'] {
    return {
      strengths: profile?.strengths || [],
      knowledge_gaps: profile?.knowledge_gaps || [],
      recommended_actions: profile?.recommended_next_steps || []
    };
  }

  private getDefaultContext(): UserContext {
    return {
      current_stage: 0,
      stage_confidence: 50,
      key_challenges: [],
      recent_progress: [],
      preferences: {},
      project_info: {},
      ai_insights: {}
    };
  }
}

export const userMemoryService = new UserMemoryService();
export default userMemoryService; 