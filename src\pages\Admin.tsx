import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { Shield, LogIn } from 'lucide-react';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import AdminDashboard from '@/components/AdminDashboard';

const Admin = () => {
  const { isAuthenticated, isLoading, login, logout } = useAdminAuth();
  const [adminPassword, setAdminPassword] = useState('');

  const handleLogin = () => {
    const success = login(adminPassword);
    if (success) {
      toast.success('Admin access granted');
      setAdminPassword('');
    } else {
      toast.error('Invalid admin password');
    }
  };

  const handleLogout = () => {
    logout();
    toast.success('Logged out successfully');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black flex items-center justify-center">
        <div className="text-center">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 border-4 border-sauce-red border-t-transparent rounded-full mx-auto mb-4"
          />
          <p className="text-accent-white text-xl">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black text-accent-white flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="w-full max-w-md"
        >
          <Card className="bg-accent-white/10 backdrop-blur-xl border-accent-white/20 shadow-2xl rounded-3xl">
            <CardHeader className="text-center p-8">
              <motion.div 
                className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 rounded-full border border-sauce-red/20 mb-4 mx-auto"
                whileHover={{ scale: 1.05 }}
              >
                <Shield className="h-5 w-5 text-sauce-red" />
                <span className="text-sauce-red font-semibold">Secure Access</span>
              </motion.div>
              <CardTitle className="text-3xl font-black text-accent-white">
                Admin{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                  Portal
                </span>
              </CardTitle>
              <p className="text-crust-beige/80 mt-2">Enter credentials to access the dashboard</p>
            </CardHeader>
            <CardContent className="space-y-6 p-8 pt-0">
              <div>
                <Label htmlFor="password" className="text-accent-white font-medium mb-2 block">Admin Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={adminPassword}
                  onChange={(e) => setAdminPassword(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleLogin()}
                  className="bg-accent-white/10 backdrop-blur-md border-accent-white/30 text-accent-white placeholder-crust-beige/60 rounded-2xl h-12 focus:border-sauce-red/50 focus:ring-sauce-red/20"
                  placeholder="Enter admin password"
                />
              </div>
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button 
                  onClick={handleLogin}
                  className="w-full bg-gradient-to-r from-sauce-red to-orange-600 hover:from-sauce-red/90 hover:to-orange-600/90 text-accent-white font-bold py-3 rounded-2xl shadow-xl"
                >
                  <LogIn className="h-5 w-5 mr-2" />
                  Access Admin Panel
                </Button>
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    );
  }

  // When authenticated, render the AdminDashboard as a full-screen component
  return <AdminDashboard onLogout={handleLogout} />;
};

export default Admin; 