import { supabase } from '@/integrations/supabase/client';
import geminiService from './geminiService';
import userMemoryService from './userMemoryService';
import onboardingToJourneyService from './onboardingToJourneyService';
import stageProgressService from './stageProgressService';
import devJourneyService from './devJourneyService';

export interface RefreshProgress {
  step: string;
  progress: number;
  message: string;
  completed: boolean;
  error?: string;
}

export interface RefreshResult {
  success: boolean;
  message: string;
  stepsCompleted: number;
  totalSteps: number;
  newStage?: number;
  newConfidence?: number;
  errors: string[];
}

class DashboardRefreshService {
  private progressCallback?: (progress: RefreshProgress) => void;

  /**
   * Refresh all AI-powered content for a user while preserving their data
   */
  async refreshDashboard(
    userId: string,
    onProgress?: (progress: RefreshProgress) => void
  ): Promise<RefreshResult> {
    this.progressCallback = onProgress;
    const errors: string[] = [];
    let stepsCompleted = 0;
    const totalSteps = 6;

    console.log('🔄 Starting comprehensive dashboard refresh for user:', userId);

    try {
      // Step 1: Clear cached AI analyses
      await this.updateProgress('clear_cache', 0, 'Clearing cached AI analyses...', false);
      try {
        await this.clearCachedAnalyses(userId);
        stepsCompleted++;
        await this.updateProgress('clear_cache', 17, 'Cached analyses cleared', true);
      } catch (error) {
        const errorMsg = `Failed to clear cached analyses: ${error}`;
        errors.push(errorMsg);
        await this.updateProgress('clear_cache', 17, errorMsg, true, errorMsg);
      }

      // Step 2: Regenerate stage analysis
      await this.updateProgress('stage_analysis', 17, 'Regenerating stage analysis...', false);
      let newStage: number | undefined;
      let newConfidence: number | undefined;
      try {
        const stageResult = await this.regenerateStageAnalysis(userId);
        newStage = stageResult.stage;
        newConfidence = stageResult.confidence;
        stepsCompleted++;
        await this.updateProgress('stage_analysis', 33, `Stage analysis updated: Stage ${newStage} (${newConfidence}% confidence)`, true);
      } catch (error) {
        const errorMsg = `Failed to regenerate stage analysis: ${error}`;
        errors.push(errorMsg);
        await this.updateProgress('stage_analysis', 33, errorMsg, true, errorMsg);
      }

      // Step 3: Regenerate milestone personalization
      await this.updateProgress('milestones', 33, 'Regenerating personalized milestones...', false);
      try {
        await this.regenerateMilestonePersonalization(userId);
        stepsCompleted++;
        await this.updateProgress('milestones', 50, 'Milestone personalization updated', true);
      } catch (error) {
        const errorMsg = `Failed to regenerate milestones: ${error}`;
        errors.push(errorMsg);
        await this.updateProgress('milestones', 50, errorMsg, true, errorMsg);
      }

      // Step 4: Re-analyze daily updates
      await this.updateProgress('daily_updates', 50, 'Re-analyzing daily updates...', false);
      try {
        await this.reanalyzeDailyUpdates(userId);
        stepsCompleted++;
        await this.updateProgress('daily_updates', 67, 'Daily updates re-analyzed', true);
      } catch (error) {
        const errorMsg = `Failed to re-analyze daily updates: ${error}`;
        errors.push(errorMsg);
        await this.updateProgress('daily_updates', 67, errorMsg, true, errorMsg);
      }

      // Step 5: Refresh user context and insights
      await this.updateProgress('user_context', 67, 'Refreshing user context and insights...', false);
      try {
        await this.refreshUserContext(userId);
        stepsCompleted++;
        await this.updateProgress('user_context', 83, 'User context refreshed', true);
      } catch (error) {
        const errorMsg = `Failed to refresh user context: ${error}`;
        errors.push(errorMsg);
        await this.updateProgress('user_context', 83, errorMsg, true, errorMsg);
      }

      // Step 6: Regenerate milestone feedback
      await this.updateProgress('milestone_feedback', 83, 'Regenerating milestone feedback...', false);
      try {
        await this.regenerateMilestoneFeedback(userId);
        stepsCompleted++;
        await this.updateProgress('milestone_feedback', 100, 'Milestone feedback regenerated', true);
      } catch (error) {
        const errorMsg = `Failed to regenerate milestone feedback: ${error}`;
        errors.push(errorMsg);
        await this.updateProgress('milestone_feedback', 100, errorMsg, true, errorMsg);
      }

      const success = errors.length === 0;
      const message = success 
        ? `Dashboard refresh completed successfully! ${stepsCompleted}/${totalSteps} steps completed.`
        : `Dashboard refresh completed with ${errors.length} errors. ${stepsCompleted}/${totalSteps} steps completed.`;

      console.log('✅ Dashboard refresh completed:', { success, stepsCompleted, totalSteps, errors });

      return {
        success,
        message,
        stepsCompleted,
        totalSteps,
        newStage,
        newConfidence,
        errors
      };

    } catch (error) {
      console.error('❌ Dashboard refresh failed:', error);
      return {
        success: false,
        message: `Dashboard refresh failed: ${error}`,
        stepsCompleted,
        totalSteps,
        errors: [...errors, `Critical error: ${error}`]
      };
    }
  }

  /**
   * Clear all cached AI analyses for the user
   */
  private async clearCachedAnalyses(userId: string): Promise<void> {
    console.log('🗑️ Clearing cached AI analyses for user:', userId);
    
    const { error } = await supabase
      .from('cached_ai_analyses')
      .delete()
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Failed to clear cached analyses: ${error.message}`);
    }

    console.log('✅ Cached AI analyses cleared');
  }

  /**
   * Regenerate stage analysis from current user context
   */
  private async regenerateStageAnalysis(userId: string): Promise<{ stage: number; confidence: number }> {
    console.log('🎯 Regenerating stage analysis for user:', userId);
    
    // Force fresh stage analysis
    const stageAnalysis = await geminiService.analyzeUserStage(userId);
    
    console.log('✅ Stage analysis regenerated:', stageAnalysis);
    return {
      stage: stageAnalysis.current_stage,
      confidence: stageAnalysis.confidence_score
    };
  }

  /**
   * Regenerate milestone personalization from onboarding data
   */
  private async regenerateMilestonePersonalization(userId: string): Promise<void> {
    console.log('📋 Regenerating milestone personalization for user:', userId);
    
    // Get user's onboarding data
    const { data: onboardingData, error: onboardingError } = await supabase
      .from('onboarding_responses')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    if (onboardingError) {
      throw new Error(`Failed to fetch onboarding data: ${onboardingError.message}`);
    }

    if (!onboardingData) {
      console.log('⚠️ No onboarding data found, skipping milestone personalization');
      return;
    }

    // Clear existing auto-populated milestones (preserve user submissions)
    const { error: clearError } = await supabase
      .from('milestone_submissions')
      .delete()
      .eq('user_id', userId)
      .eq('metadata->>source', 'onboarding_population');

    if (clearError) {
      throw new Error(`Failed to clear existing auto-populated milestones: ${clearError.message}`);
    }

    // Regenerate milestone personalization
    const result = await onboardingToJourneyService.populateJourneyFromOnboarding(userId);
    
    if (!result.success) {
      throw new Error(`Failed to regenerate milestone personalization: ${result.message}`);
    }

    console.log('✅ Milestone personalization regenerated:', result);
  }

  /**
   * Re-analyze existing daily updates with current AI logic
   */
  private async reanalyzeDailyUpdates(userId: string): Promise<void> {
    console.log('📝 Re-analyzing daily updates for user:', userId);
    
    // Get recent daily updates
    const { data: dailyUpdates, error } = await supabase
      .from('daily_updates')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10); // Re-analyze last 10 updates

    if (error) {
      throw new Error(`Failed to fetch daily updates: ${error.message}`);
    }

    if (!dailyUpdates || dailyUpdates.length === 0) {
      console.log('⚠️ No daily updates found, skipping re-analysis');
      return;
    }

    // Re-analyze each update
    for (const update of dailyUpdates) {
      try {
        const userContext = await userMemoryService.buildUserContext(userId);
        const stageInfo = devJourneyService.getStage(userContext.current_stage);
        
        const aiAnalysis = await geminiService.analyzeDailyUpdate(
          update.content,
          {
            devJourneyStage: userContext.current_stage,
            stageTitle: stageInfo?.title,
            userContext: {
              ...userContext,
              user_id: userId
            },
            stageFrameworks: Object.keys(stageInfo?.frameworks || {}),
            currentChallenges: userContext.key_challenges
          }
        );

        // Update the daily update with new AI insights
        const { error: updateError } = await supabase
          .from('daily_updates')
          .update({
            ai_insights: {
              insights: aiAnalysis.insights || aiAnalysis.coachingInsight,
              coachingInsight: aiAnalysis.coachingInsight,
              encouragement: aiAnalysis.encouragement,
              regenerated_at: new Date().toISOString()
            },
            updated_at: new Date().toISOString()
          })
          .eq('id', update.id);

        if (updateError) {
          console.error(`Failed to update daily update ${update.id}:`, updateError);
        }
      } catch (error) {
        console.error(`Failed to re-analyze daily update ${update.id}:`, error);
      }
    }

    console.log('✅ Daily updates re-analyzed');
  }

  /**
   * Refresh user context and insights
   */
  private async refreshUserContext(userId: string): Promise<void> {
    console.log('🧠 Refreshing user context for user:', userId);
    
    // Clear existing user memory entries of type 'insight'
    const { error: clearError } = await supabase
      .from('user_memory_entries')
      .delete()
      .eq('user_id', userId)
      .eq('memory_type', 'insight');

    if (clearError) {
      console.error('Failed to clear existing insights:', clearError);
    }

    // Rebuild user context (this will generate fresh insights)
    const userContext = await userMemoryService.buildUserContext(userId);
    
    console.log('✅ User context refreshed:', userContext);
  }

  /**
   * Regenerate milestone feedback for completed milestones
   */
  private async regenerateMilestoneFeedback(userId: string): Promise<void> {
    console.log('💬 Regenerating milestone feedback for user:', userId);
    
    // Get completed milestones with user submissions
    const { data: milestones, error } = await supabase
      .from('milestone_submissions')
      .select('*')
      .eq('user_id', userId)
      .eq('completed', true)
      .not('submission_content', 'is', null);

    if (error) {
      throw new Error(`Failed to fetch milestone submissions: ${error.message}`);
    }

    if (!milestones || milestones.length === 0) {
      console.log('⚠️ No completed milestones found, skipping feedback regeneration');
      return;
    }

    // Regenerate feedback for each milestone
    for (const milestone of milestones) {
      try {
        const userContext = await userMemoryService.buildUserContext(userId);
        
        const aiAnalysis = await geminiService.analyzeMilestoneSubmission(
          milestone.submission_content,
          {
            milestoneTitle: milestone.milestone_id,
            milestoneDescription: milestone.metadata?.personalized_description || '',
            stageId: userContext.current_stage,
            stageTitle: devJourneyService.getStage(userContext.current_stage)?.title || '',
            userContext: {
              ...userContext,
              user_id: userId
            },
            projectContext: milestone.metadata?.project_context || ''
          }
        );

        // Update milestone with new AI feedback
        const { error: updateError } = await supabase
          .from('milestone_submissions')
          .update({
            ai_feedback: aiAnalysis?.consolidatedFeedback || aiAnalysis?.coachingInsight || aiAnalysis?.insights,
            ai_insights: JSON.stringify({
              followUpQuestions: aiAnalysis?.followUpQuestions || [],
              keyLearnings: aiAnalysis?.keyLearnings || [],
              nextSteps: aiAnalysis?.nextSteps || []
            })
          })
          .eq('id', milestone.id);

        if (updateError) {
          console.error(`Failed to update milestone ${milestone.id}:`, updateError);
        }
      } catch (error) {
        console.error(`Failed to regenerate feedback for milestone ${milestone.id}:`, error);
      }
    }

    console.log('✅ Milestone feedback regenerated');
  }

  /**
   * Update progress callback
   */
  private async updateProgress(
    step: string,
    progress: number,
    message: string,
    completed: boolean,
    error?: string
  ): Promise<void> {
    if (this.progressCallback) {
      this.progressCallback({
        step,
        progress,
        message,
        completed,
        error
      });
    }
    
    // Small delay to allow UI updates
    await new Promise(resolve => setTimeout(resolve, 100));
  }
}

export default new DashboardRefreshService();
