# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment files
.env
.env.*
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
build/
out/

# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/
*.lcov

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist/

# Temporary files
*.tmp
*.temp
.temp/
temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Reference and documentation files (development only)
REFERENCE FILES/
*REFERENCE*
*.reference.*
MOBILE_OPTIMIZATION_SUMMARY.md
NETWORK_SETUP.md
SECURITY.md

# Development documentation and guides
*_GUIDE.md
*_SETUP.md
*_IMPLEMENTATION.md
*_DEPLOYMENT.md
*_TROUBLESHOOTING.md
*_MONITORING.md
ACTIVITY_MONITORING_SETUP.md
CORRECTED_MIGRATION_GUIDE.md
EDGE_FUNCTIONS_DEPLOYMENT.md
PIE_FI_DASHBOARD_IMPLEMENTATION.md
README_DEV_JOURNEY_INTEGRATION.md
SQL_MIGRATION_TROUBLESHOOTING.md
TESTING_ACTIVITY_MONITORING.md
USER_MANAGEMENT_SYSTEM_GUIDE.md
USER_PROGRESS_ANALYTICS_GUIDE.md

# Specific development files to ignore
analysis_cache.sql
enhanced_milestone_tracking.sql
initialize_user_cache.sql
pie-fi-imagine-animation.html
piefi_cohort_welcome_email.html
piefidevjourney.txt
deploy-functions.sh

# Development scripts and tools
configure-firewall.ps1
*.ps1
debug-*.js
test-*.js
deploy-functions.sh
*.sh

# Development SQL files and cache
analysis_cache.sql
enhanced_milestone_tracking.sql
initialize_user_cache.sql
*.cache.sql
*_cache.sql

# Development HTML files and prototypes
pie-fi-imagine-animation.html
piefi_cohort_welcome_email.html
*.prototype.html
*.demo.html

# Development text files and notes
piefidevjourney.txt
*.notes.txt
*.dev.txt
*.todo.txt

# Supabase temporary files
supabase/.temp/
supabase/logs/
supabase/.branches/
# Note: supabase/migrations/ should be tracked as it contains database schema

# Lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml
bun.lockb

# IDE and editor files
*.swp
*.swo
*~

# Public assets (already specified)
public/piefi-assets/

# Additional development artifacts
*.backup
*.bak
*.orig
*.rej
*.patch
*.diff

# Database dumps and exports
*.sql.gz
*.dump
*.backup.sql
database_backup_*
db_export_*

# Configuration backups
config.backup.*
*.config.backup

# Development utilities and test files (be more specific)
src/utils/test-*.ts
src/utils/debug-*.ts
src/utils/*Test.ts
src/utils/*Debug.ts
src/utils/temp*.ts
src/utils/scratch*.ts

# Temporary component files (be more specific)
src/components/Test-*.tsx
src/components/Debug-*.tsx
src/components/Temp-*.tsx
src/components/test-*.tsx
src/components/debug-*.tsx
src/components/temp-*.tsx

# Development services (be more specific)
src/services/test-*.ts
src/services/debug-*.ts
src/services/*Test.ts
src/services/*Debug.ts
src/services/temp*.ts
src/services/scratch*.ts

# Development pages (be more specific)
src/pages/Test-*.tsx
src/pages/Debug-*.tsx
src/pages/Temp-*.tsx
src/pages/test-*.tsx
src/pages/debug-*.tsx
src/pages/temp-*.tsx

# Cursor AI and IDE specific files
.cursor/
.vscode/settings.json
.vscode/launch.json
.vscode/tasks.json
.vscode/*.code-workspace

# Package manager artifacts
.yarn/
.pnp.*
.yarnrc.yml

# Runtime and process files
*.pid
*.seed
*.pid.lock

# Coverage and testing artifacts
.nyc_output/
coverage/
*.lcov
.coverage/
htmlcov/

# Miscellaneous development files
scratch/
playground/
experiments/
prototypes/
drafts/
archive/
old/
backup/

# Future development artifacts (add patterns as needed)
*.wip
*.draft
*.sketch
*.mockup
*.wireframe
*-DRAFT.*
*-WIP.*
*-TODO.*
*-TEMP.*

# AI and automation artifacts
.ai/
.automation/
*.ai-generated
*.auto-generated

# Performance and monitoring
*.perf
*.benchmark
*.profile
performance-*
benchmark-*
