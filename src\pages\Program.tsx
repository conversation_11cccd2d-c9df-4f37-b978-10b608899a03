import React from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowRight, Calendar, Users, Trophy, Lightbulb, Code, DollarSign, Home, Zap, Target, Clock, Award } from 'lucide-react';
import { motion } from 'framer-motion';
import { useScrollAnimation, scrollVariants, staggerContainer } from '@/hooks/useScrollAnimation';

const Program = () => {
  const { ref: heroRef, isInView: heroInView } = useScrollAnimation();
  const { ref: structureRef, isInView: structureInView } = useScrollAnimation();
  const { ref: resourcesRef, isInView: resourcesInView } = useScrollAnimation();
  const { ref: weeklyRef, isInView: weeklyInView } = useScrollAnimation();

  const programStructure = [
    {
      week: 'Weeks 1-2',
      title: 'Foundation & Formation',
      description: 'Team formation, idea validation, and setting up your development environment.',
      icon: Users,
      color: 'from-blue-400 to-blue-600',
      activities: ['Team matching', 'Idea pitches', 'Market research', 'Legal setup']
    },
    {
      week: 'Weeks 3-5',
      title: 'Build & Iterate',
      description: 'Core development phase with weekly mentor check-ins and user feedback loops.',
      icon: Code,
      color: 'from-green-400 to-green-600',
      activities: ['MVP development', 'User interviews', 'Feature prioritization', 'Technical mentorship']
    },
    {
      week: 'Weeks 6-8',
      title: 'Scale & Refine',
      description: 'Product refinement, go-to-market strategy, and preparing for launch.',
      icon: Zap,
      color: 'from-purple-400 to-purple-600',
      activities: ['Product polish', 'Marketing strategy', 'Partnership outreach', 'Pitch practice']
    },
    {
      week: 'Weeks 9-10',
      title: 'Launch & Demo',
      description: 'Final preparations, product launch, and Demo Day presentation.',
      icon: Trophy,
      color: 'from-red-400 to-red-600',
      activities: ['Product launch', 'Media outreach', 'Demo Day prep', 'Investor meetings']
    }
  ];

  const resources = [
    {
      title: 'Mentorship Network',
      description: 'Weekly 1:1s with industry veterans, technical experts, and successful founders.',
      icon: Lightbulb,
      color: 'from-yellow-400 to-orange-500'
    },
    {
      title: 'Workspace Access',
      description: 'Dedicated workspace in Santa Cruz with high-speed internet and collaboration areas.',
      icon: Home,
      color: 'from-green-400 to-blue-500'
    },
    {
      title: 'Tech Credits',
      description: '$5K+ in AWS, OpenAI, Figma, and other essential tools for modern startups.',
      icon: Code,
      color: 'from-blue-400 to-purple-500'
    },
    {
      title: 'Legal Support',
      description: 'Free incorporation, IP guidance, and legal framework setup through our partners.',
      icon: Award,
      color: 'from-purple-400 to-pink-500'
    },
    {
      title: 'Micro-grants',
      description: 'Up to $10K available for teams that demonstrate progress and need funding.',
      icon: DollarSign,
      color: 'from-emerald-400 to-teal-500'
    },
    {
      title: 'Demo Day Platform',
      description: 'Present to 200+ investors, media, and community leaders at our Demo Day.',
      icon: Target,
      color: 'from-red-400 to-rose-500'
    }
  ];

  const weeklySchedule = [
    { day: 'Monday', activity: 'Team Standup & Goal Setting', icon: Target },
    { day: 'Tuesday', activity: 'Mentor Sessions (Technical)', icon: Code },
    { day: 'Wednesday', activity: 'Workshop Wednesday', icon: Lightbulb },
    { day: 'Thursday', activity: 'Mentor Sessions (Business)', icon: Users },
    { day: 'Friday', activity: 'Demo Friday & Peer Feedback', icon: Trophy }
  ];

  return (
    <div className="bg-oven-black text-accent-white min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black" ref={heroRef}>
          {/* Animated background */}
          <div className="absolute inset-0">
            {[...Array(25)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [-30, 30, -30],
                  x: [-20, 20, -20],
                  opacity: [0.2, 0.8, 0.2],
                  scale: [0.5, 1.2, 0.5],
                }}
                transition={{
                  duration: 5 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2,
                }}
              />
            ))}
          </div>

          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial="hidden"
              animate={heroInView ? "visible" : "hidden"}
              variants={scrollVariants}
            >
              <motion.div 
                className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-sauce-red/10 to-cheese-gold/10 rounded-full border border-sauce-red/20 mb-8"
                whileHover={{ scale: 1.05 }}
              >
                <Calendar className="w-4 h-4 text-sauce-red" />
                <span className="text-sauce-red font-semibold">The Program</span>
              </motion.div>
              
              <h1 className="text-6xl sm:text-7xl lg:text-8xl font-black mb-8">
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                  10 Weeks
                </span>{" "}
                <span className="text-accent-white">to Launch</span>
              </h1>
              
              <p className="text-2xl text-crust-beige/90 max-w-4xl mx-auto mb-12 leading-relaxed">
                A comprehensive builder experience designed to take you from{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold font-bold">
                  idea to Demo Day in just 10 weeks.
                </span>
              </p>
            </motion.div>
          </div>
        </section>

        {/* Program Structure */}
        <section className="relative py-32 overflow-hidden bg-gradient-to-br from-crust-beige via-accent-white to-crust-beige" ref={structureRef}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              animate={structureInView ? "visible" : "hidden"}
              variants={scrollVariants}
              className="text-center mb-20"
            >
              <h2 className="text-5xl font-black text-oven-black mb-6">
                Program{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                  Structure
                </span>
              </h2>
              <p className="text-xl text-oven-black/70 max-w-3xl mx-auto">
                Four phases designed to maximize your learning and building velocity
              </p>
            </motion.div>

            <motion.div 
              className="grid gap-8"
              variants={staggerContainer}
              initial="hidden"
              animate={structureInView ? "visible" : "hidden"}
            >
              {programStructure.map((phase, index) => (
                <motion.div 
                  key={index}
                  className="group relative"
                  variants={scrollVariants}
                  whileHover={{ scale: 1.02, y: -5 }}
                >
                  {/* Glow effect */}
                  <div className={`absolute -inset-1 bg-gradient-to-r ${phase.color} rounded-3xl blur-xl opacity-0 group-hover:opacity-40 transition-opacity duration-500`} />
                  
                  {/* Main card */}
                  <Card className="relative bg-accent-white/90 backdrop-blur-md border border-oven-black/10 rounded-3xl shadow-xl overflow-hidden">
                    <div className="grid md:grid-cols-12 gap-0">
                      {/* Icon and week */}
                      <div className="md:col-span-3 bg-gradient-to-br from-accent-white/50 to-transparent p-8 flex flex-col items-center justify-center border-b md:border-b-0 md:border-r border-oven-black/10">
                        <motion.div 
                          className={`p-4 rounded-2xl bg-gradient-to-r ${phase.color} shadow-lg mb-4`}
                          whileHover={{ rotate: 360, scale: 1.1 }}
                          transition={{ duration: 0.6 }}
                        >
                          <phase.icon className="h-10 w-10 text-white" />
                        </motion.div>
                        <h3 className="text-xl font-bold text-oven-black text-center">{phase.week}</h3>
                      </div>
                      
                      {/* Content */}
                      <div className="md:col-span-9 p-8">
                        <h4 className="text-2xl font-bold text-oven-black mb-4">{phase.title}</h4>
                        <p className="text-oven-black/70 mb-6 text-lg leading-relaxed">{phase.description}</p>
                        
                        <div className="grid grid-cols-2 gap-4">
                          {phase.activities.map((activity, actIndex) => (
                            <motion.div 
                              key={actIndex}
                              className="flex items-center space-x-2"
                              whileHover={{ x: 5 }}
                            >
                              <div className="w-2 h-2 bg-sauce-red rounded-full" />
                              <span className="text-oven-black/80">{activity}</span>
                            </motion.div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Resources Section */}
        <section className="relative py-32 overflow-hidden bg-gradient-to-br from-oven-black via-purple-900/20 to-oven-black" ref={resourcesRef}>
          <div className="absolute inset-0">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-sauce-red/40 rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  scale: [0, 1, 0],
                  opacity: [0, 0.6, 0],
                }}
                transition={{
                  duration: 3 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2,
                }}
              />
            ))}
          </div>

          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              animate={resourcesInView ? "visible" : "hidden"}
              variants={scrollVariants}
              className="text-center mb-20"
            >
              <h2 className="text-5xl sm:text-6xl font-black text-accent-white mb-6">
                Your{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                  Arsenal
                </span>
              </h2>
              <p className="text-xl text-crust-beige/80 max-w-3xl mx-auto">
                Everything you need to build, launch, and scale your startup
              </p>
            </motion.div>

            <motion.div 
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
              variants={staggerContainer}
              initial="hidden"
              animate={resourcesInView ? "visible" : "hidden"}
            >
              {resources.map((resource, index) => (
                <motion.div 
                  key={resource.title}
                  className="group relative"
                  variants={scrollVariants}
                  whileHover={{ y: -10, scale: 1.05 }}
                >
                  {/* Glow effect */}
                  <div className={`absolute -inset-1 bg-gradient-to-r ${resource.color} rounded-3xl blur-xl opacity-0 group-hover:opacity-60 transition-opacity duration-500`} />
                  
                  {/* Main card */}
                  <div className="relative bg-accent-white/10 backdrop-blur-md rounded-3xl p-8 border border-accent-white/20 h-full">
                    <motion.div 
                      className={`w-16 h-16 bg-gradient-to-r ${resource.color} rounded-2xl flex items-center justify-center mb-6 shadow-lg`}
                      whileHover={{ rotate: 360, scale: 1.1 }}
                      transition={{ duration: 0.6 }}
                    >
                      <resource.icon className="h-8 w-8 text-white" />
                    </motion.div>
                    
                    <h3 className="text-xl font-bold text-accent-white mb-4">{resource.title}</h3>
                    <p className="text-crust-beige/80 leading-relaxed">{resource.description}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Weekly Schedule */}
        <section className="relative py-32 overflow-hidden bg-gradient-to-br from-crust-beige via-accent-white to-crust-beige" ref={weeklyRef}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              animate={weeklyInView ? "visible" : "hidden"}
              variants={scrollVariants}
              className="text-center mb-20"
            >
              <h2 className="text-5xl font-black text-oven-black mb-6">
                Weekly{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-sauce-red to-cheese-gold">
                  Rhythm
                </span>
              </h2>
              <p className="text-xl text-oven-black/70 max-w-3xl mx-auto">
                Structured schedule designed to maximize progress and learning
              </p>
            </motion.div>

            <motion.div 
              className="grid md:grid-cols-5 gap-6"
              variants={staggerContainer}
              initial="hidden"
              animate={weeklyInView ? "visible" : "hidden"}
            >
              {weeklySchedule.map((day, index) => (
                <motion.div 
                  key={day.day}
                  className="group relative"
                  variants={scrollVariants}
                  whileHover={{ y: -10, scale: 1.05 }}
                >
                  <div className="absolute -inset-1 bg-gradient-to-r from-sauce-red/20 to-cheese-gold/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-60 transition-opacity duration-500" />
                  
                  <div className="relative bg-accent-white/90 backdrop-blur-md rounded-3xl p-6 border border-oven-black/10 shadow-xl text-center h-full">
                    <motion.div 
                      className="w-12 h-12 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-2xl flex items-center justify-center mx-auto mb-4"
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.5 }}
                    >
                      <day.icon className="h-6 w-6 text-white" />
                    </motion.div>
                    
                    <h3 className="font-bold text-oven-black mb-2">{day.day}</h3>
                    <p className="text-oven-black/70 text-sm leading-relaxed">{day.activity}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>

            {/* CTA Section */}
            <motion.div 
              className="mt-20 text-center"
              initial={{ opacity: 0, y: 50 }}
              animate={weeklyInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
              transition={{ delay: 0.8 }}
            >
              <div className="bg-gradient-to-r from-sauce-red/10 via-cheese-gold/10 to-sauce-red/10 backdrop-blur-md border border-sauce-red/20 rounded-3xl p-12">
                <h3 className="text-3xl font-bold text-oven-black mb-6">Ready to Start Building?</h3>
                <p className="text-xl text-oven-black/70 mb-8 max-w-2xl mx-auto">
                  Applications are open now. Be among the first 10 teams to launch from Santa Cruz.
                </p>
                
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="relative group inline-block"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-sauce-red to-cheese-gold rounded-2xl blur-xl opacity-70 group-hover:opacity-100 transition-opacity" />
                  <Button 
                    size="lg" 
                    onClick={() => window.location.href = '/apply#application-form'}
                    className="relative bg-gradient-to-r from-sauce-red to-sauce-red/90 hover:from-sauce-red/90 hover:to-sauce-red text-accent-white font-bold text-lg px-10 py-6 rounded-2xl shadow-2xl border-0"
                  >
                    Apply Now <ArrowRight className="ml-3 h-6 w-6" />
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Program;
