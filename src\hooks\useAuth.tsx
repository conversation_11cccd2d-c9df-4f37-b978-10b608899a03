import { useEffect, useState, createContext, useContext } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { User } from '@supabase/supabase-js';
import { activityTrackingService } from '@/services/activityTrackingService';

interface UserProfile extends User {
  full_name?: string;
  track?: 'newbie' | 'builder' | 'scaler';
  onboarding_completed?: boolean;
  notion_workspace_id?: string;
  discord_user_id?: string;
}

interface AuthContextType {
  user: UserProfile | null;
  loading: boolean;
  signUp: (email: string, password: string, fullName: string) => Promise<{ data: any; error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  updateUserProfile: (updates: Partial<UserProfile>) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check active sessions and sets the user
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (session?.user) {
        fetchUserProfile(session.user.id);
      } else {
        setLoading(false);
      }
    });

    // Listen for changes on auth state (sign in, sign out, etc.)
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        fetchUserProfile(session.user.id);

        // Track login activity
        if (event === 'SIGNED_IN') {
          await activityTrackingService.trackLogin(session.user.id);
        }
      } else {
        // Track logout activity if we had a user before
        if (user && event === 'SIGNED_OUT') {
          await activityTrackingService.trackLogout(user.id);
        }
        setUser(null);
        setLoading(false);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const fetchUserProfile = async (userId: string) => {
    try {
      console.log('Fetching user profile for ID:', userId);
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) {
        console.log('No auth user found');
        setLoading(false);
        return;
      }
      console.log('Auth user:', authUser);

      // Fix: Use correct query format for Supabase
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .maybeSingle(); // Use maybeSingle() instead of single() to handle no rows gracefully

      if (error) {
        console.error('Error fetching user profile:', error);
        setLoading(false);
        return;
      }

      if (!data) {
        // User profile doesn't exist, create it
        console.log('User profile not found, creating new one for:', userId);
        const newUserProfile = {
          id: authUser.id,
          email: authUser.email!,
          full_name: authUser.user_metadata?.full_name || 'Builder',
          onboarding_completed: false,
          track: null // Will be set during onboarding
        };

        const { data: insertData, error: insertError } = await supabase
          .from('users')
          .insert(newUserProfile)
          .select()
          .single();

        if (insertError) {
          console.error('Error creating user profile:', insertError);
          // If it's a duplicate key error, try to fetch the existing user
          if (insertError.code === '23505') {
            console.log('User already exists, fetching existing profile...');
            const { data: existingData, error: fetchError } = await supabase
              .from('users')
              .select('*')
              .eq('id', userId)
              .maybeSingle();
            
            if (!fetchError && existingData) {
              const userProfile = { ...authUser, ...existingData } as UserProfile;
              console.log('Setting existing user profile:', userProfile);
              setUser(userProfile);
            } else {
              console.error('Failed to fetch existing user after duplicate error:', fetchError);
              // Create a minimal profile with auth data only
              const userProfile = { 
                ...authUser, 
                full_name: authUser.user_metadata?.full_name || 'Builder',
                onboarding_completed: false 
              } as UserProfile;
              setUser(userProfile);
            }
          } else {
            // For other errors, still set user with auth data
            const userProfile = { 
              ...authUser, 
              full_name: authUser.user_metadata?.full_name || 'Builder',
              onboarding_completed: false 
            } as UserProfile;
            setUser(userProfile);
          }
        } else {
          const userProfile = { ...authUser, ...insertData } as UserProfile;
          console.log('Setting new user profile:', userProfile);
          setUser(userProfile);
        }
      } else {
        // User profile exists, use it
        const userProfile = { ...authUser, ...data } as UserProfile;
        console.log('Setting existing user profile:', userProfile);
        setUser(userProfile);
      }
    } catch (error) {
      console.error('Error in fetchUserProfile:', error);
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, fullName: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName
          }
        }
      });

      // Enhanced error handling for signup
      if (error) {
        let userFriendlyMessage = error.message;
        
        switch (error.message) {
          case 'User already registered':
            userFriendlyMessage = 'An account with this email already exists. Please sign in instead.';
            break;
          case 'Password should be at least 6 characters':
            userFriendlyMessage = 'Password must be at least 6 characters long.';
            break;
          case 'Signup requires a valid password':
            userFriendlyMessage = 'Please enter a valid password.';
            break;
          case 'Invalid email':
            userFriendlyMessage = 'Please enter a valid email address.';
            break;
          default:
            if (error.message.includes('rate limit')) {
              userFriendlyMessage = 'Too many signup attempts. Please wait a moment and try again.';
            } else if (error.message.includes('weak password')) {
              userFriendlyMessage = 'Password is too weak. Please choose a stronger password.';
            }
        }
        
        return { data, error: { ...error, message: userFriendlyMessage } };
      }

      if (data.user) {
        console.log('Creating user profile for:', data.user.id);

        // Track signup activity
        await activityTrackingService.trackActivity('signup', {
          action: 'user_signup',
          email: email,
          full_name: fullName,
          success: true
        }, data.user.id);

        // Check if user profile already exists (edge case handling)
        const { data: existingProfile } = await supabase
          .from('users')
          .select('id')
          .eq('id', data.user.id)
          .maybeSingle();

        if (!existingProfile) {
          // Only create profile if it doesn't exist
          const { error: profileError } = await supabase
            .from('users')
            .insert({
              id: data.user.id,
              email: email,
              full_name: fullName,
              onboarding_completed: false,
              track: null
            });

          if (profileError) {
            console.error('Error creating user profile during signup:', profileError);
            // Don't throw error here, user can still proceed
          } else {
            console.log('User profile created successfully');
          }
        } else {
          console.log('User profile already exists, skipping creation');
        }
      }

      return { data, error: null };
    } catch (error: any) {
      console.error('Error in signUp:', error);
      return { 
        data: null, 
        error: { 
          message: 'An unexpected error occurred during signup. Please try again.' 
        } 
      };
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      // Enhanced error handling with specific user messages
      if (error) {
        let userFriendlyMessage = error.message;
        
        switch (error.message) {
          case 'Invalid login credentials':
            userFriendlyMessage = 'Incorrect email or password. Please check your credentials and try again.';
            break;
          case 'Email not confirmed':
            userFriendlyMessage = 'Please check your email and click the confirmation link before signing in.';
            break;
          case 'Too many requests':
            userFriendlyMessage = 'Too many login attempts. Please wait a few minutes and try again.';
            break;
          case 'User not found':
            userFriendlyMessage = 'No account found with this email address. Please check the email or create a new account.';
            break;
          default:
            if (error.message.includes('rate limit')) {
              userFriendlyMessage = 'Too many attempts. Please wait a moment and try again.';
            } else if (error.message.includes('network')) {
              userFriendlyMessage = 'Network error. Please check your internet connection and try again.';
            }
        }
        
        return { data, error: { ...error, message: userFriendlyMessage } };
      }

      return { data, error: null };
    } catch (error: any) {
      console.error('Error in signIn:', error);
      return { 
        data: null, 
        error: { 
          message: 'An unexpected error occurred. Please try again.' 
        } 
      };
    }
  };

  const signOut = async () => {
    // Track logout before signing out
    if (user) {
      await activityTrackingService.trackLogout(user.id);
    }
    await supabase.auth.signOut();
    setUser(null);
  };

  // Method to update user object after profile changes
  const updateUserProfile = (updates: Partial<UserProfile>) => {
    if (user) {
      setUser({ ...user, ...updates });
    }
  };

  const value = {
    user,
    loading,
    signUp,
    signIn,
    signOut,
    updateUserProfile
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 